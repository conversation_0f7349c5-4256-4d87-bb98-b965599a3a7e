{"extends": ["react-app", "plugin:prettier/recommended", "plugin:react-hooks/recommended", "plugin:@tencent/tea-i18n/recommended", "@tencent/eslint-config-tencent/ts", "prettier"], "plugins": ["react", "@typescript-eslint", "prettier", "react-hooks", "@tencent/tea-i18n"], "env": {"browser": true, "jest": true}, "rules": {"@typescript-eslint/no-misused-promises": 0, "react/prop-types": 0, "no-underscore-dangle": 0, "no-unused-vars": 1, "import/imports-first": [0, "absolute-first"], "import/newline-after-import": "error", "import/no-default-export": 0, "semi": ["error", "always"], "@typescript-eslint/semi": ["error", "always"], "@typescript-eslint/naming-convention": 0, "@typescript-eslint/no-unused-vars": 0, "react-hooks/exhaustive-deps": ["warn", {"additionalHooks": "(useAsyncRetryFunc|useGlobalAppDetails|useAsyncFn)"}], "@tencent/tea-i18n/no-missing-import": ["error", {"i18nSource": "@src/utils/i18n"}], "react-hooks/rules-of-hooks": "error", "react/jsx-filename-extension": [1, {"extensions": [".js", ".jsx", ".tsx"]}]}, "globals": {"window": true, "document": true, "localStorage": true, "FormData": true, "FileReader": true, "Blob": true, "navigator": true, "Headers": true, "Request": true, "fetch": true}}