# build stage
FROM node:lts-alpine as build-stage
RUN mkdir -p /app
WORKDIR /app
COPY ./package*.json ./
# RUN npm install --registry=https://mirrors.tencent.com/npm/
COPY . .
# RUN npm run build

FROM csighub.tencentyun.com/public/tnginx:latest as production-stage
# FROM csighub.cloud.tencent.com/public/tnginx:latest as production-stage
# COPY --from=0 /app/build /usr/share/nginx/html

COPY ./nginx /usr/local/tnginx/conf

ENTRYPOINT \
  ls -al && \
  sh /usr/local/services/tnginx_1_0_0-1.0/admin/start.sh all &&\
  sleep infinity