/* eslint-disable */
const CompressionPlugin = require('compression-webpack-plugin');
const SimpleProgressWebpackPlugin = require('simple-progress-webpack-plugin');

const path = require('path');

const {
  override,
  useBabelRc,
  // addLessLoader,
  overrideDevServer,
  watchAll,
  addBundleVisualizer,
  addWebpackPlugin,
  removeModuleScopePlugin,
  addWebpackAlias,
  setWebpackOptimizationSplitChunks,
  addBabelPlugins,
} = require('customize-cra');
const addLessLoader = require('customize-cra-less-loader');

const loaderOptions = {
  lessLoaderOptions: {
    lessOptions: {
      javascriptEnabled: true,
      modifyVars: { '@primary-color': '#1DA57A' },
      cssModules: {
        localIdentName: '[path][name]__[local]--[hash:base64:5]',
      },
    },
  },
};

const addProxy = () => (configFunction) => {
  configFunction.proxy = {
    '/apis/': {
      target: 'http://test-api-csms-supplier.polaris',
      // target: 'http://************',
      changeOrigin: true,
      pathRewrite: { '^/apis': '/' },
    },
    '/api-inner/': {
      target: 'http://test-inner-api-csms-supplier.polaris',
      // target: 'http://test-inner-api-isms-purchase.polaris',

      // target: 'http://************',
      changeOrigin: true,
      pathRewrite: { '^/api-inner': '/' },
    },
  };

  return configFunction;
};

const addPerformance = () => {
  return (config) => {
    config.performance = {
      hints: 'warning',
      // 1M 大小
      maxAssetSize: 1 * 1024 * 1024,
      // 1M 大小
      maxEntrypointSize: 1 * 1024 * 1024,
      assetFilter: function (assetFilename) {
        return /\.js$|\.ts|\.tsx|\.jsx|\.json$/.test(assetFilename);
      },
    };
    return config;
  };
};

const isProduction = process.env.NODE_ENV === 'production';
console.log('you are in production', isProduction);

const dropConsole = () => {
  return (config) => {
    if (config.optimization.minimizer) {
      config.optimization.minimizer.forEach((minimizer) => {
        if (minimizer.constructor.name === 'TerserPlugin') {
          minimizer.options.minimizer.options.compress = {
            ...minimizer.options.minimizer.options.compress,
            drop_console: true,
            drop_debugger: true,
          };
        }
      });
    }
    return config;
  };
};

module.exports = {
  webpack: override(
    useBabelRc(),
    addLessLoader(loaderOptions),
    removeModuleScopePlugin(),
    addWebpackAlias({
      ['@src']: path.resolve(__dirname, 'src/'),
      ['@i18n']: path.resolve(__dirname, 'i18n/'),
      ['@tea/app']: path.resolve(
        __dirname,
        'node_modules/@tencent/tea-app/lib',
      ),
      '@tea/component': path.resolve(
        __dirname,
        'node_modules/@tencent/tea-component/lib',
      ),
      'react-hot-loader': path.resolve(
        __dirname,
        'node_modules/@tencent/tea-scripts/engineering/webpack/alias/react-hot-loader',
      ),
    }),
    addWebpackPlugin(new SimpleProgressWebpackPlugin()),
    isProduction && addBundleVisualizer(),
    isProduction &&
      addWebpackPlugin(
        new CompressionPlugin({
          test: /\.(js|html|css)$/,
          // 10K 大小
          threshold: 10 * 1024,
          minRatio: 0.8,
        }),
      ),
    isProduction && addPerformance(),
    isProduction &&
      setWebpackOptimizationSplitChunks({
        chunks: 'all',
        // 1M 大小
        maxSize: 1 * 1024 * 1024,
        name: false,
      }),
    dropConsole(),
  ),
  devServer: overrideDevServer(addProxy(), watchAll()),
};
