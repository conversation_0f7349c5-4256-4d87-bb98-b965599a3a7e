## 配置 docker 设置

{
"builder": {
"gc": {
"defaultKeepStorage": "20GB",
"enabled": true
}
},
"experimental": false,
"features": {
"buildkit": true
}
}

```js


"registry-mirrors": [
"http://csighub.tencentyun.com"
],
"insecure-registries": [
"bk.artifactory.oa.com:8080"
]
```

在 Docker Engine

## MAC 电脑，需要退出 Docker【重启都没用】

osascript -e 'quit app "Docker"'

## 手动 build

# 手动上传

<!-- csighub.tencentyun.com/tx-sms/csms-supplier-platform -->

*********** dev.tencent.purchase.com
*********** dev.supplier.purchase.com

docker build --network=host -t csighub.tencentyun.com/tx-sms/csms-supplier-platform:0901_1 .

docker build --file Dockerfile --network=host -t csighub.tencentyun.com/tx-sms/csms-supplier-platform:0901_1 .
docker build --file Dockerfile2 --network=host -t csighub.tencentyun.com/tx-sms/csms-supplier-platform:0901_1 .

docker push csighub.tencentyun.com/tx-sms/csms-supplier-platform:0901_1
docker pull csighub.tencentyun.com/tx-sms/csms-supplier-platform:0901_1

## 相关文章

https://km.woa.com/group/17746/articles/show/396593?kmref=search&from_page=1&no=3
https://km.woa.com/group/17746/articles/show/396593?kmref=search&from_page=1&no=1
https://stackoverflow.com/questions/40080887/how-do-i-restart-docker-for-mac-from-the-terminal
http://wiki.kubernetes.oa.com/quickstart/csighub.html#%E6%93%8D%E4%BD%9C%E6%AD%A5%E9%AA%A4
docker pull csighub.tencentyun.com/admin/tlinux2.2-bridge-tcloud-underlay
docker build --network=host -t csighub.tencentyun.com/kanesong/testimages:v1 ./

## 域名申请

http://udns.woa.com/#/dashboard

## 智能网关接入
