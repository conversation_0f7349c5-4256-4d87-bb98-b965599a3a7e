/* eslint-disable */
/**
 * @fileoverview
 *
 * 本文件词条由 `tea scan` 命令扫描生成，请勿手动编辑
 *
 * 国际化方案，请参考文档 http://tapd.oa.com/tcp_access/markdown_wikis/0#1020399462008817031
 */

/**
 * @type {import('@tea/app').I18NTranslation}
 */
var translation = {
  "k_0003yx6": "Yes",
  "k_00041oj": "No",
  "k_002qrgn": "Time",
  "k_002qzi3": "Close",
  "k_002r79h": "All",
  "k_002rasg": "Marketing",
  "k_002rflt": "Delete",
  "k_002umy9": "Failed",
  "k_002uny0": "Approve",
  "k_002vxya": "Edit",
  "k_002wh4y": "Query",
  "k_002wh8b": "Test",
  "k_003hnkj": "Delivery Rate (DR)",
  "k_003hnkk": "Conversion Rate (CR)",
  "k_003j4vl": "Deactivate",
  "k_003ko62": "Notification",
  "k_003l8ai": "Email",
  "k_003lpre": "Reject",
  "k_003lxmq": "Refresh",
  "k_003m2q7": "Chinese",
  "k_003m4ze": "Clear",
  "k_003m9uv": "No data",
  "k_003mfaa": "Password",
  "k_003mow6": "Details",
  "k_003n2zl": "Version",
  "k_003n40n": "Renew",
  "k_003nevv": "Cancel",
  "k_003nrij": "Supported",
  "k_003nzwy": "Status",
  "k_003ojje": "Remarks",
  "k_003p7ir": "Hong Kong",
  "k_003p9am": "Price",
  "k_003polf": "Export",
  "k_003ps50": "Account",
  "k_003psvr": "Domain name",
  "k_003qjcv": "Short number",
  "k_003qjz0": "Company TAX(VAT) Number",
  "k_003qnm7": "Back",
  "k_003qo3t": "Time Zone",
  "k_003qq9t": "EUR",
  "k_003qqzh": "No.",
  "k_003qu5k": "USD",
  "k_003r6vf": "Log in",
  "k_003rk1s": "Save",
  "k_003rzap": "OK",
  "k_003s38r": "More",
  "k_003s7kb": "Required",
  "k_003tyh3": "Unit price",
  "k_003tywh": "Submit",
  "k_003u02c": "Operation",
  "k_003uxsh": "Adjust price",
  "k_003uzuf": "Attachment",
  "k_00hvy6u": "Submit selected entries on this page",
  "k_00io84s": "Reason for Supplementary",
  "k_00l7efv": "Pure local route for local brands",
  "k_00obfty": "Error message: An error occurred in row <1></1> of the imported table, which corresponds to row <5></5> of the template table. <8></8>",
  "k_01iqwx6": ">=90%",
  "k_01iqxrf": ">=80%",
  "k_01iz5jf": "<=39%",
  "k_01mdlv9": "Modified successfully. Please log in again.",
  "k_01qg1gw": "Guidelines for Quota by Nets",
  "k_02iffq6": "Amount of failure",
  "k_02j4owi": "Pure int'l",
  "k_02j9qyt": "Amount of success",
  "k_02jaius": "Pure voice message",
  "k_02jbr2a": "Commitment",
  "k_02mox5k": "Please check",
  "k_02n9mzk": "Pure direct",
  "k_02w7uh2": "Limited quantity",
  "k_02z35tu": "Uin",
  "k_03766u3": "SID",
  "k_038cwok": "No charge",
  "k_038cx3x": "With charge",
  "k_038j3oj": "Disabled",
  "k_038jvsg": "Enabled",
  "k_03aj66h": "OTP",
  "k_03be766": "Expired",
  "k_03bkivm": "New password",
  "k_03bkix1": "Current password",
  "k_03c67bm": "Please select",
  "k_03cho97": "Finished",
  "k_03cshuu": "Not supported",
  "k_03d0b5u": "SubStatus",
  "k_03dmkfq": "Operator",
  "k_03e25cp": "Pending review",
  "k_03edlo3": "Authorization Letter",
  "k_03eqyl2": "Pure local",
  "k_03euyfo": "Singapore",
  "k_03f4chz": "Pending launch",
  "k_03fh9cq": "Pure SIM farm",
  "k_03fkcas": "Please enter",
  "k_03fwli4": "Numeric SID",
  "k_03i7qq9": "Under review",
  "k_03i8dlr": "In effect",
  "k_03idjo0": "Contact",
  "k_03igk9g": "Draft",
  "k_03ihhm4": "In Progress",
  "k_03jbl7n": "Executing",
  "k_03jcgxi": "Testing",
  "k_03mhcqh": "Apply ID",
  "k_03myncp": "Register ID",
  "k_03oiozl": "Supplier ID",
  "k_04n73f0": "Account Management / Create account",
  "k_04r1fad": "The country {{attr0}} you selected dose not currently support the quota by networks, please modify and re-submit.",
  "k_04uiqkv": "Please select entries in \"Draft\" status.",
  "k_0599kgb": "Test failed",
  "k_06xj804": "Sender ID Management",
  "k_06xuofl": "This operation is for adding a new quote, not editing the quote.",
  "k_0709wvl": "2. Upload the Excel file",
  "k_076uk70": "I have readed and agreed",
  "k_0801xuq": "Fetch Attachment Failed",
  "k_08djotj": "View unfinished tasks",
  "k_09flj51": "Commitment (messages/month)",
  "k_0buq3l7": "Route quality",
  "k_0bya3vq": "Created Date",
  "k_0bybdc3": "Update time",
  "k_0bybfpa": "Expiration date",
  "k_0bybocw": "Effective date",
  "k_0bybvwn": "Select Time",
  "k_0bycm1h": "Renewal date",
  "k_0c1qesx": "Primary IP/port",
  "k_0c522qb": "Blended routes",
  "k_0cbw9ra": "Domain name access",
  "k_0cefcjj": "Notification Marketing",
  "k_0cjhbiv": "Batch delete",
  "k_0ckff1n": "Amount of the task",
  "k_0dinpvb": "May 29, 2023: The batch import template has been updated. If you need to import batch quotes, please download the batch import template again.",
  "k_0dk786u": "Export entries on this page",
  "k_0es7670": "SMPP password",
  "k_0f1raw2": "Specified carrier",
  "k_0f2p2aw": "Global SMS Management Console",
  "k_0f7dbkw": "Operation failed",
  "k_0f7eqgn": "Export failed",
  "k_0f7esyd": "Rejected",
  "k_0f7fefr": "API failed",
  "k_0f7tyyu": "Test passed",
  "k_0f7zpcx": "Approved",
  "k_0fp482d": "Please enter the account",
  "k_0fpw2e6": "Create account",
  "k_0fw7bkz": "IP List",
  "k_0g03mbp": "Business Registration Number",
  "k_0gb3ddi": "Direct IP connection",
  "k_0gbhwer": "Tencent Cloud network node",
  "k_0gbisgh": "The number of Binds/ Sessions cannot be lower than the maximum number of Binds/ Sessions allowed by your server's SMPP account. Otherwise, the test cannot commence. Kindly verify this with your technical team before proceeding with the submission.",
  "k_0gfp2pm": "Supplier network node",
  "k_0go07yo": "The password format is incorrect. It can include numbers, letters, and symbols. Letters are case-sensitive.",
  "k_0groyj8": "No test",
  "k_0gvlt5l": "Company Number",
  "k_0gz2a01": "Invalid parameter",
  "k_0hfz14a": "OTP/Marketing",
  "k_0hoio01": "Quote List",
  "k_0hqyj6t": "Please ensure that the following IP addresses have been whitelisted. ",
  "k_0ilydbc": "Binds/ Sessions",
  "k_0itp4o5": "Click to select time",
  "k_0klcx3f": "SID type",
  "k_0kxhpci": "SIM farm messages",
  "k_0l5fatv": "OTP/Notification/Marketing",
  "k_0lmnvmq": "The imported data is empty",
  "k_0m9bwrc": "SMPP Account TPS ",
  "k_0mdxgds": "Supplier/Carrier",
  "k_0me48vr": "Sending speed limit (messages/sec)",
  "k_0mwh0a9": "Backup IP/Port",
  "k_0nbzln1": "Sender ID Registration List",
  "k_0nvqi9k": "Via WA, Viber, etc.",
  "k_0oodexf": "Low quality (LQ)",
  "k_0ooejb9": "High quality (HQ)",
  "k_0peu6rj": "The email format is incorrect.",
  "k_0pjw55u": "Register sender",
  "k_0pslafe": "Usually, suppliers cannot select OTP/Notification/Marketing as the traffic type when uploading routes to our portal, only when our procurement team requires you to choose OTP/Notification/Marketing as the traffic type. ",
  "k_0pvc55z": "Hop",
  "k_0pz40rs": "Send again ({{count}}s)",
  "k_0pz40rs_plural": "Send again ({{count}}s)",
  "k_0qge60x": "Voice message",
  "k_0qgx1ld": "Created Date Range",
  "k_0qmpzzr": "Please read this service agreement in its entirety",
  "k_0qqjrsy": "Please enter valid email address and password first.",
  "k_0re7dzs": "Estimated Monthly Traffic",
  "k_0s8yqeh": "Quote Management / Adjust price ({{attr0}})",
  "k_0silkwp": "Please enter the correct numeric format.",
  "k_0sqq0kz": "Enter the new password",
  "k_0ss301w": "Enter the current password",
  "k_0svcvak": "Quote Management / Quote details ({{attr0}})",
  "k_0t76q8o": "Preferred connection method",
  "k_0t8luk2": "80%-89%",
  "k_0tbrcs3": "40%-59%",
  "k_0tf5qeq": "60%-69%",
  "k_0tftpbm": "70%-79%",
  "k_0v74y9o": "Change SMS content",
  "k_0vblqlv": "Please enter a value between {{min}} and {{max}}.",
  "k_0w3r73l": "Supplier Name",
  "k_0wa8lcr": "Enter an email address",
  "k_0wc0o96": "SMPP account",
  "k_0x02cri": "Mid-low quality (MLQ)",
  "k_0x12qiw": "Mid-high quality (MHQ)",
  "k_0x7irua": "View task details",
  "k_0xke5ha": "Sender type",
  "k_0xvovyk": "<0>There is {{count}} incorrect entry. Please correct it and upload again.</0>",
  "k_0xvovyk_plural": "<0>There are {{count}} incorrect entries. Please correct them and upload again.</0>",
  "k_0y1wbxk": "Send",
  "k_0y51x36": "Commitment",
  "k_0yhgynu": "<60%",
  "k_0yjbnzs": "Exclusive short code",
  "k_0yjc2xz": "Exclusive long code",
  "k_0yjoc8z": "Price ID",
  "k_0yjpsyi": "Renewal ID",
  "k_0yjqe0p": "Supplier Account ID",
  "k_0yjt1wy": "Task ID",
  "k_0yjt8zd": "Supplier Account ID",
  "k_0yjv0hm": "Quote ID",
  "k_0yjwldx": "Pure OTT",
  "k_0yk0sdj": "Pure SMS",
  "k_0yq8xqk": "Account Management / Account details ({{attr0}})",
  "k_0zixu9v": "Quote Management / Add quote",
  "k_0zri53m": "Shared/Default SID",
  "k_0zslj9g": "Customer Deactivate",
  "k_0zubmz4": "Quote Management",
  "k_0zudfgz": "Account Management",
  "k_10b3884": "Amount of failure",
  "k_10c4tma": "Quotation task list",
  "k_10oclwa": "Operating License",
  "k_10ri66r": "The account is too short.",
  "k_10rm8qt": "The password is too short.",
  "k_10ycasp": "Click to view.",
  "k_1117kk0": "International",
  "k_111jiz8": "Domestic",
  "k_1163v5m": "Download template",
  "k_118lezo": "Advanced Search",
  "k_11kb1bg": "Company Name",
  "k_11kc8nd": "Account Name",
  "k_11khais": "Frankfurt",
  "k_11khgha": "Company Email",
  "k_11zgmmw": "Other Materials",
  "k_122qrg8": "Website URL",
  "k_126f5go": "Account Management / Edit account ({{attr0}})",
  "k_12799tm": "Only accounts in the \"Draft\", \"Pending review\", and \"Rejected\" status can be deleted",
  "k_12a5t9c": "Effective",
  "k_12kotxx": "Change password",
  "k_12rvwak": "Task details",
  "k_1330o12": "Expiration date",
  "k_1330tjc": "Effective date",
  "k_13a34oy": "Back to Quote List",
  "k_13bwklw": "Whether there is a charge",
  "k_13ccvt1": "Only quotes in the status of \"Draft\" and \"Rejected\" can be deleted",
  "k_13mdvb1": "Basic information",
  "k_13ozr8k": "Currently do not support",
  "k_13qe1r6": "Import data",
  "k_13ugsnk": "Not support export",
  "k_13w7t9y": "Registration Status",
  "k_13wa1rs": "Upload failed, please try again",
  "k_142sdg4": "Application source",
  "k_157twge": "Reason for Deactivate",
  "k_157y7x3": "Reason for rejection",
  "k_159qg8j": "Int'l-local blended",
  "k_15asmne": "Batch import",
  "k_15d5s9f": "Edit account",
  "k_15dfb11": "Create account",
  "k_15fxrzd": "The domain format is incorrect. The correct format is: domain:port , for example: example.com:8080.",
  "k_15i1ius": "Protocol type",
  "k_15i7870": "Route type",
  "k_15icxq1": "SMS Type",
  "k_15in87s": "Brand Type",
  "k_15invqj": "Route type",
  "k_15khvs1": "Company Address",
  "k_15myciu": "Error message: An error occurred in row <1></1> of the imported table, which corresponds to row <4></4> of the template table. <7></7>",
  "k_15wcibc": "Renewed successfully",
  "k_15wdhxq": "Completed",
  "k_15wdq5o": "Exported successfully",
  "k_15weour": "Imported successfully",
  "k_15wgku3": "Operation succeeded",
  "k_15wgmqm": "Submitted successfully",
  "k_15wrir2": "Deleted successfully",
  "k_160mxpt": "Rejected",
  "k_165gdjw": "Rejected history",
  "k_165guzk": "Operation history",
  "k_16d5ys1": "When the account is initiated, the System Type of the SMPP account parameters will be set to <1>nsg</1> to connect to the supplier's gateway. Please ensure that your account permits binding requests carrying the System Type as <3>nsg</3>, otherwise, the test cannot commence. Kindly verify this with your technical team before proceeding with the submission.",
  "k_16kts8h": "Log out",
  "k_16lo4qq": "Customized SID",
  "k_16r3sej": "Country/Region",
  "k_1706kub": "1. Download the template and fill out it",
  "k_17ay7g5": "SMS Content",
  "k_17b8sk9": "Please scroll down the page to read the following service agreement in its entirety",
  "k_17mkzbz": "Modify root account password",
  "k_17rmitd": "Failed to obtain user information",
  "k_185lsga": "I agree to receive <1>《Tencent Cloud Overseas Communication Service Provider Service Agreement - Overseas SMS》and《Tencent Cloud Overseas SMS Fault Report》</1>",
  "k_18fru34": "OTP/Notification",
  "k_18hw0t4": "Supplier Deactivate",
  "k_18nin9s": "Edit quote",
  "k_18ny1ax": "Add quote",
  "k_18o0b97": "Currency",
  "k_18o1b9u": "Upload again",
  "k_18ow07l": "Quote Review List",
  "k_18s6ea3": "Customize column display",
  "k_18x152f": "Batch submit",
  "k_19qydh5": "Check Attachment",
  "k_19sezbh": "Business Industry",
  "k_19y0ukd": "Supplement Materials",
  "k_1adzgod": "Brand Ownership Certificates",
  "k_1bb2t22": "Ordinary sender",
  "k_1c7fx39": "Sure to delete {{attr0}}?",
  "k_1df8isc": "OTP/Notification Marketing",
  "k_1e2zyli": "Quote Renewal Management List",
  "k_1f60b9l": "SMPP domain name",
  "k_1f76ept": "Extend validity period to ",
  "k_1fkxxlb": "Testing resource",
  "k_1fu8g2k": "Random numeric sender",
  "k_1fubuzf": "General numeric sender",
  "k_1h9ye2o": "Please enter an integer.",
  "k_1hby3gy": "Add quote",
  "k_1hlvp6p": "Limited quantity (messages/month)",
  "k_1i0yv6u": "General alpha sender",
  "k_1i14hjt": "Exclusive alpha sender",
  "k_1ic7vbt": "Notification/marketing",
  "k_1k3c2bw": "Limited quantity",
  "k_1kis1hn": "You must read and agree to the supplier's service agreement",
  "k_1kk2bvc": "System Type ",
  "k_1mjd0wv": "Pure local route for int'l brands",
  "k_1nq9kgg": "Enter code sent to your email",
  "k_1o40t7w": "Pure direct blended with blank",
  "k_1p3xsp8": "Procurement reviewer",
  "k_1p45wzk": "Product reviewer",
  "k_1p6hq6q": "Password modified successfully. Please log in again.",
  "k_1pnpaua": "Supplier Account List",
  "k_1ps15fj": "Amount of success",
  "k_1pw6eai": "Supplier Authorization Letter",
  "k_1q917zb": "Range: 0-255",
  "k_1qer5sl": "Please read this service agreement in its entirety",
  "k_1qnhyjq": "US/Canada Application Template",
  "k_1rdptnb": "Only record quotation tasks within 24 hours",
  "k_1rm4d3k": "XLSX files can be uploaded. Please check the file type.",
  "k_1s9ju6n": "Reselling",
  "k_1sjf7ns": "Tencent Cloud Global SMS Management Console",
  "k_1sxdeau": "Delete selected entries on this page",
  "k_1szer31": "Upload failed, please try again[{{attr0}}]",
  "k_1t9kzib": "Please enter the password",
  "k_1wn011i": "Sure to delete {{attr0}}?",
  "k_1wt4lc8": "Quote Management / Edit quote ({{attr0}})",
  "k_1ypktw4": "Time-out",
  "k_1yz0vrs": "Remark",
};

module.exports = { translation: translation };
