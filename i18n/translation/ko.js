/* eslint-disable */
/**
 * @fileoverview
 *
 * 本文件词条由 `tea scan` 命令扫描生成，请勿手动编辑
 *
 * 国际化方案，请参考文档 http://tapd.oa.com/tcp_access/markdown_wikis/0#1020399462008817031
 */

/**
 * @type {import('@tea/app').I18NTranslation}
 */
var translation = {
  "k_00046qn": " ", // fallback from en
  "k_00046tr": "", // fallback from en
  "k_002qezw": "Public key", // fallback from en
  "k_002qgzg": " minutes", // fallback from en
  "k_002qh1i": "Key", // fallback from en
  "k_002qmyp": "Private key", // fallback from en
  "k_002qqb2": "News", // fallback from en
  "k_002qzi3": "Disable", // fallback from en
  "k_002r1jz": "Enable", // fallback from en
  "k_002r2e6": "Selected ", // fallback from en
  "k_002r4mo": "Modification", // fallback from en
  "k_002r79h": "All", // fallback from en
  "k_002rflt": "Delete", // fallback from en
  "k_002s86q": "Video", // fallback from en
  "k_002tyy6": "Vituperation", // fallback from en
  "k_002u46m": "Education", // fallback from en
  "k_002utip": "Renew", // fallback from en
  "k_002uu5g": "Hide", // fallback from en
  "k_002uvkx": "Financing", // fallback from en
  "k_002v5g0": "Communications", // fallback from en
  "k_002v637": "Navigation", // fallback from en
  "k_002v9zj": "Confirm", // fallback from en
  "k_002vetb": "Total", // fallback from en
  "k_002vxya": "Edit", // fallback from en
  "k_002wddw": "Mute", // fallback from en
  "k_002wsuz": "Reading", // fallback from en
  "k_003j4vl": "Disable", // fallback from en
  "k_003j5ky": "On", // fallback from en
  "k_003k8rl": "Shopping", // fallback from en
  "k_003l8z3": "Note", // fallback from en
  "k_003lhvk": "Nickname", // fallback from en
  "k_003lm4h": "System", // fallback from en
  "k_003lm9b": "Meizu", // fallback from en
  "k_003lmhx": "Group", // fallback from en
  "k_003lpqx": "Xiaomi", // fallback from en
  "k_003lrad": "Disband", // fallback from en
  "k_003lvyd": "Upgrade", // fallback from en
  "k_003m6xh": "Set", // fallback from en
  "k_003m9lb": "Downgrade", // fallback from en
  "k_003mam4": "Tag", // fallback from en
  "k_003mhrz": "Children", // fallback from en
  "k_003mnf6": "Pornography", // fallback from en
  "k_003mnsy": "Note", // fallback from en
  "k_003nevv": "Cancel", // fallback from en
  "k_003nh35": "Lifestyle", // fallback from en
  "k_003nh7l": "Politics", // fallback from en
  "k_003niz6": "Game", // fallback from en
  "k_003nyn5": "Violence/Terrorism", // fallback from en
  "k_003nzwy": "Status", // fallback from en
  "k_003o9vc": "Tourism", // fallback from en
  "k_003p3ha": "Drugs", // fallback from en
  "k_003pmuv": "Tool", // fallback from en
  "k_003pnfo": "Security", // fallback from en
  "k_003ppbr": "Office", // fallback from en
  "k_003psxc": "Advertising", // fallback from en
  "k_003ptal": "Member", // fallback from en
  "k_003pvhf": "Bulletins", // fallback from en
  "k_003py1h": "Type", // fallback from en
  "k_003q0tt": "Comprehensive", // fallback from en
  "k_003q5fi": "Copy", // fallback from en
  "k_003qdlq": "Add", // fallback from en
  "k_003qie5": "Beautifying", // fallback from en
  "k_003qzac": "Yesterday", // fallback from en
  "k_003r0af": "Healthy", // fallback from en
  "k_003r11s": "Photography", // fallback from en
  "k_003r1w8": "Visitor", // fallback from en
  "k_003rcwm": "Enable", // fallback from en
  "k_003re34": "Show More", // fallback from en
  "k_003rzap": "Confirm", // fallback from en
  "k_003s3gw": "Other", // fallback from en
  "k_003tr0a": "Group Owner", // fallback from en
  "k_003tu3o": "Entertainment", // fallback from en
  "k_003tue7": "Socializing", // fallback from en
  "k_003tywh": "Submit", // fallback from en
  "k_003tzjl": "Huawei", // fallback from en
  "k_003u02c": "Operation", // fallback from en
  "k_003uyjq": "Music", // fallback from en
  "k_003v1aw": "Purchase", // fallback from en
  "k_00ngmma": "Message Sent Successfully after Blacklisting", // fallback from en
  "k_00smlth": "<0>Triple-device login</0> allows single-device login across Android and iOS, plus simultaneous online on both Windows devices and web browser.", // fallback from en
  "k_00wvzgq": "Are you sure you want to delete selected members?", // fallback from en
  "k_010a0a6": "Your IM {{name}} will be changed to the trial version. Are you sure you want to continue?", // fallback from en
  "k_011p1s7": "<0>Used to invoke the RESTful API or disbanding groups. For more information</0>.", // fallback from en
  "k_016wyge": "即时通信 IM 消息存储时长是多久？", // fallback from en
  "k_01bi4um": "Platform setup failed. Please retry.", // fallback from en
  "k_01m9flu": "No Upper Limit for AVChatRoom Quantity", // fallback from en
  "k_01ph6vn": "IM allows users to send one-to-one messages to friends and strangers. With this feature enabled, relationship check will be performed when a one-to-one chat is initiated. Only friends can send one-to-one messages to each other. When someone sends a one-to-one message to a stranger.", // fallback from en
  "k_01u8ldw": "No iOS certificates added", // fallback from en
  "k_029axg6": "Error 70009 occurs upon login?", // fallback from en
  "k_02a80ko": "Private", // fallback from en
  "k_02iq161": "Disabled", // fallback from en
  "k_02is0i5": "Enabled", // fallback from en
  "k_02is31u": "Disable", // fallback from en
  "k_02j3yb1": "Read", // fallback from en
  "k_02j51cf": "Write", // fallback from en
  "k_02k59el": "Reserve at least one group type.", // fallback from en
  "k_02lk81d": "Operator", // fallback from en
  "k_02nssam": "No speaking", // fallback from en
  "k_02ntno8": "Sensitive Words", // fallback from en
  "k_02o2kxh": "Keywords", // fallback from en
  "k_02txozh": "Sender ID", // fallback from en
  "k_02ycxvq": "Tencent Real-Time Communication (TRTC) is an independent Tencent Cloud service. For billing details, please see <1>Price</1>.", // fallback from en
  "k_032maj7": "Failed to acquire key. Try later.", // fallback from en
  "k_03768rw": "Group ID", // fallback from en
  "k_039jcei": "Flagship Edition", // fallback from en
  "k_039kbj4": "Starter Edition", // fallback from en
  "k_039nofm": "Trial Version", // fallback from en
  "k_039v1wk": "Pro Edition", // fallback from en
  "k_03agq58": "Group Name", // fallback from en
  "k_03avj1p": "Public", // fallback from en
  "k_03b0tex": "Private", // fallback from en
  "k_03bcjkv": "Not set", // fallback from en
  "k_03byhup": " CNY/month", // fallback from en
  "k_03c4agr": "Validity", // fallback from en
  "k_03c67bm": "Please select", // fallback from en
  "k_03cowsj": "Root user", // fallback from en
  "k_03epq1a": "Unlimited", // fallback from en
  "k_03eq60o": "Enabled", // fallback from en
  "k_03eq71c": "Disabled", // fallback from en
  "k_03erpei": "Admin", // fallback from en
  "k_03es1ox": "Group Type", // fallback from en
  "k_03exdnb": "Username", // fallback from en
  "k_03fqryh": "APPID", // fallback from en
  "k_03ft8cu": "WeChat Mini Programs", // fallback from en
  "k_03g2bdw": "Enabled", // fallback from en
  "k_03gu05e": "ChatRoom", // fallback from en
  "k_03i9mfe": "Group Info", // fallback from en
  "k_03kyi6r": "Callback URL", // fallback from en
  "k_03mr8fm": "AppSecret", // fallback from en
  "k_03psznl": "Enter correct AppID", // fallback from en
  "k_03qyfi9": "Can contain a maximum of 16 characters, including digits, English characters, and underscores (_)", // fallback from en
  "k_0484z5g": "Online status change callback", // fallback from en
  "k_04aq6rs": "Enter AppID", // fallback from en
  "k_04q3f64": "Unreadable but Writable", // fallback from en
  "k_04xaf5w": "You can create only one group for each type.", // fallback from en
  "k_057gpez": "iOS certificate (.p12)", // fallback from en
  "k_05gjxwn": "One-to-One Chat Message Senders", // fallback from en
  "k_05ltmqg": "How does IM charge?", // fallback from en
  "k_05nspni": "Custom Field", // fallback from en
  "k_05t6icy": "6,000 per group", // fallback from en
  "k_05t6id2": "2,000 per group", // fallback from en
  "k_05ts8fx": "Callback after disbanding groups", // fallback from en
  "k_062xdf9": "答复：您可参考<1>“群@消息”与“红包消息”</1>文档详细了解如何实现以上功能。", // fallback from en
  "k_06adbqg": "The Content Filtering Basic feature only provides the default dictionary and you cannot add custom dictionary. To use the custom sensitive word feature, you can click Upgrade or purchase the Content Filtering Pro service on the purchase page.", // fallback from en
  "k_06xywa4": "Extending message retention duration is a paid service, which takes effect immediately after activation. For fees details.", // fallback from en
  "k_07b7xm7": "Readable by App Admin", // fallback from en
  "k_07uzjir": "After muted, the selected member will not be able to send messages for 24 hours.", // fallback from en
  "k_07xkth6": "Add User Custom Field", // fallback from en
  "k_088sjf2": "Allows users to stay online simultaneously online on web, Windows, Android, and iOS devices.", // fallback from en
  "k_08auph6": "Login Authentication", // fallback from en
  "k_08chr4o": "Are you sure you want to delete the sensitive words?", // fallback from en
  "k_08huvvt": "Introduces how to quickly run through IM demo", // fallback from en
  "k_08j9fh6": "Your IM {{name}} will be immediately disabled and will not be recoverable. Are you sure you want to disable it?", // fallback from en
  "k_08m8wz6": "Group Member Custom Field", // fallback from en
  "k_08mh1ig": "Group Custom Field", // fallback from en
  "k_08t2ot4": "Online Web Instances", // fallback from en
  "k_08xohj3": "Note: Statistical data will be updated at roughly 12:00 the next day.", // fallback from en
  "k_090f2kr": "Enter the field name", // fallback from en
  "k_090ptt5": "Compile and run to experience your exclusive IM demo", // fallback from en
  "k_09ga9ab": "Failed to get UserSig", // fallback from en
  "k_09gabq1": "Failed to copy UserSig", // fallback from en
  "k_09k71s6": "BChatRoom", // fallback from en
  "k_09uy5z0": "Callback after group creation", // fallback from en
  "k_09yxjzp": "You can enter the console to view features and configurations.", // fallback from en
  "k_09z6365": "Writable by App Admin", // fallback from en
  "k_0adpciq": "No admins", // fallback from en
  "k_0afnu48": "<0>答复：</0><1><0>单聊消息</0><1>确认消息是否发送成功。<1></1>确认接收方是否登录成功。<3></3>确认发送消息的指定会话是否与接收方一致。</1><2>群组消息</2><3>确认消息是否发送成功。<1></1>确认接收方是否登录成功。<3></3>确认接收方是否是群成员。</3><4>不管是 C2C 消息还是群消息，在以上步骤无法确认问题的时候，需要继续确认以下情况：</4><5>1.确认是否注册了消息监听器。<1></1>2.确认发送方发送消息的时候，是否把elem添加到消息中了（发消息的时候需要检查addElement的返回值）。<3></3>3.Android 的需要确认是否注册了多个消息监听器，并且在消息监听器中返回了true。</5></1>", // fallback from en
  "k_0ahfsp0": "Up to 5 admins can be added", // fallback from en
  "k_0b8qyxi": "Upload a {{ftype}} file under 5 MB.", // fallback from en
  "k_0b9pyb0": "Confirm to Delete Certificate", // fallback from en
  "k_0bbcp5f": "Instant Messaging - Console", // fallback from en
  "k_0bep6ks": "Historical Message Retention Settings ", // fallback from en
  "k_0bp5ypu": "Display key", // fallback from en
  "k_0bqcb95": "Hide key", // fallback from en
  "k_0bqch6n": "30 days (500 CNY/month)", // fallback from en
  "k_0bqghw4": "Generate UserSig", // fallback from en
  "k_0bqoxb4": "Copy UserSig", // fallback from en
  "k_0by7uxo": "Added At", // fallback from en
  "k_0by8deq": "Added Time", // fallback from en
  "k_0bya3vq": "Creation Time", // fallback from en
  "k_0byanns": "Generation Time", // fallback from en
  "k_0bycevc": "Expiry Time", // fallback from en
  "k_0bycvr3": "Expiry Time", // fallback from en
  "k_0c6jivb": "Activate", // fallback from en
  "k_0c7w0m6": "Send", // fallback from en
  "k_0cbqjnt": "What is Usersig and how do I generate a Usersig?", // fallback from en
  "k_0ch28bu": "<0>答复：</0><1>管理员和群主可以通过 IM SDK 提供的接口对成员设置或取消禁言（取消禁言只需要把禁言时间设置为 0 即可）。<1></1>查询成员禁言信息，是通过查询群成员资料来实现的。详细设置文档可参阅以下 SDK 文档：<3>Android<1>获取本人在群里的资料</1><2></2>iOS<4>获取本人在群里的资料</4><5></5>Web<7>获取群成员列表</7></3></1>", // fallback from en
  "k_0ci17g2": "Modification Notification", // fallback from en
  "k_0ci1kw3": "Deletion notice", // fallback from en
  "k_0ci7nsj": "Disable notice", // fallback from en
  "k_0ci8nrb": "Upgrade notice", // fallback from en
  "k_0ci9p3h": "Downgrade notice", // fallback from en
  "k_0cid4x3": "Repeat Reminder", // fallback from en
  "k_0cim82p": "Change Permissions", // fallback from en
  "k_0ck7q8c": "Friends", // fallback from en
  "k_0ck81wd": "Accounts", // fallback from en
  "k_0cnhzv4": "Remove", // fallback from en
  "k_0ct7cq0": "Ultra-fast Integration (Including UI Library)", // fallback from en
  "k_0cxhufo": "2. Configuration change to each feature pack can be made once a month.", // fallback from en
  "k_0cyhsxt": "Please select \"increase the upper limit of members per group\"", // fallback from en
  "k_0czn78v": "Callback after friend deletion", // fallback from en
  "k_0czyigg": "Callback after friend adding", // fallback from en
  "k_0d10vm6": "Enter MasterSecret", // fallback from en
  "k_0d39e3u": "FAQs", // fallback from en
  "k_0d9zdy2": "Maximum Concurrently Online Users", // fallback from en
  "k_0db342z": "ChannelID", // fallback from en
  "k_0dhg6fm": "Open webpage", // fallback from en
  "k_0dyq4ue": "One-to-One Message Senders", // fallback from en
  "k_0dyqdpz": "Group Chat Message Senders", // fallback from en
  "k_0e0w944": "Callback after full group", // fallback from en
  "k_0e64fpv": "Callback after group name change", // fallback from en
  "k_0eakdba": "Development Tools - Console", // fallback from en
  "k_0ehyh6s": "Please complete info before submitting", // fallback from en
  "k_0ej8v0j": "Verify", // fallback from en
  "k_0ep7l2d": "Enter app name", // fallback from en
  "k_0eqm9sp": "答复：UserSig 是用户登录即时通信 IM 的密码，其本质是对 UserID 等信息加密后得到的密文，您可通过<1>生成Usersig文档</1>详细了解如何生成Usersig。", // fallback from en
  "k_0evat01": "Demo Experience and Resource Download", // fallback from en
  "k_0ezlilc": "Writable by All", // fallback from en
  "k_0f0d45o": "Writable by Admin", // fallback from en
  "k_0f0g5y7": "Writable by Member", // fallback from en
  "k_0f4mi0c": "No content change, no need to modify", // fallback from en
  "k_0f7d7a3": "Failed to upload", // fallback from en
  "k_0f7esp6": "Failed to replicate", // fallback from en
  "k_0f7ezje": "Failed to add", // fallback from en
  "k_0f7kl7l": "Readable and Writable", // fallback from en
  "k_0f7m97l": "Failed to set", // fallback from en
  "k_0f87k6q": "Writable by App", // fallback from en
  "k_0f8drqm": "Failed to remove", // fallback from en
  "k_0f8fs9o": "Verification failed", // fallback from en
  "k_0fcuhgv": "An error occurred while accessing the console. Please contact the platform for help.", // fallback from en
  "k_0foqcla": "Compile and run", // fallback from en
  "k_0fpert0": "One-to-One Chat Statistics", // fallback from en
  "k_0fpexf9": "Group Chat Statistics", // fallback from en
  "k_0fqui1b": "Upgrade", // fallback from en
  "k_0fqwwj7": "Add Group Custom Field", // fallback from en
  "k_0frlabe": "Modify", // fallback from en
  "k_0frrpes": "UserSig Verification Tool", // fallback from en
  "k_0fwj6js": "Data Overview", // fallback from en
  "k_0fwskgw": "Group Member Custom Field", // fallback from en
  "k_0g2zotm": "Upper limit of Members per Group", // fallback from en
  "k_0g5ncdy": "Click to Notify", // fallback from en
  "k_0gomzru": "Unmute", // fallback from en
  "k_0gqh9la": "Readable by Group Owner", // fallback from en
  "k_0gwl0ap": "90 days (1,000 CNY/month)", // fallback from en
  "k_0gwzgd7": "Enterprise Verification", // fallback from en
  "k_0gwzimo": "Individual Verification", // fallback from en
  "k_0gx29al": "Verify Now", // fallback from en
  "k_0gz6yzt": "Blacklist Check", // fallback from en
  "k_0h1g636": "Add Member", // fallback from en
  "k_0h1svv1": "Delete Member", // fallback from en
  "k_0h28e9s": "Enter correct callback address", // fallback from en
  "k_0horrdz": "Group type is required", // fallback from en
  "k_0hpurix": "Use Instant Messaging (IM) directly", // fallback from en
  "k_0hv7q9t": "AVChatRoom", // fallback from en
  "k_0hvlcfu": "Upstream Group Messages", // fallback from en
  "k_0hvljnv": "Upstream One-to-One Messages", // fallback from en
  "k_0i3vs0c": "Enter the earlier server key", // fallback from en
  "k_0i7bhrg": "如何查看被禁言的成员及禁言时间？", // fallback from en
  "k_0ij0zsx": "Last Modified", // fallback from en
  "k_0ik8gre": "How to generate a certificate", // fallback from en
  "k_0ikgn2m": "7 days (free)", // fallback from en
  "k_0iqmus8": "Custom Webpage", // fallback from en
  "k_0iwxj4t": "How to generate {{name}} certificate", // fallback from en
  "k_0jaaaq4": "Google", // fallback from en
  "k_0jbrqfj": "Specified In-app Page", // fallback from en
  "k_0jcvbdj": "Enter the specified page", // fallback from en
  "k_0jfvqx8": "Custom webpages must start with http:// or https://", // fallback from en
  "k_0ji38p2": "Extend message retention duration", // fallback from en
  "k_0jyp2mt": "Select certificate", // fallback from en
  "k_0jzl26z": "Offline Push Problem Locator", // fallback from en
  "k_0k0nvke": "Cancellation takes effect immediately.", // fallback from en
  "k_0k1wgqn": "Upload certificate", // fallback from en
  "k_0k5h0ed": "Remove Admin", // fallback from en
  "k_0k5u42l": "Account Admin", // fallback from en
  "k_0k5u935": "Add Admin", // fallback from en
  "k_0k60yv8": "Cancel Admin", // fallback from en
  "k_0k7ghc2": "Add Android Certificate", // fallback from en
  "k_0k81ebk": "Relationship Check for One-to-One Messages", // fallback from en
  "k_0kt52zp": "Sensitive word adding completed; successful: {{successNum}}, failed: {{failNum}}", // fallback from en
  "k_0kwamzi": "Check Relationship for One-to-One Messages", // fallback from en
  "k_0kxmxew": "Please note that up to 10 custom fields can be set. <1>These fields cannot be deleted. The field name and field type cannot be modified</1>.", // fallback from en
  "k_0liav9j": "10,000 per group\"", // fallback from en
  "k_0lkc4st": "Failed to generate UserSig. Please retry later.", // fallback from en
  "k_0ll3ono": "Run Through Demo in 1 Minute", // fallback from en
  "k_0lnouly": "Download SDK and Demo", // fallback from en
  "k_0lvcwon": "After TRTC is activated, we will create a TRTC app with the same SDKAppID as the current IM app in the <1>TRTC Console</1> for you.", // fallback from en
  "k_0lx0fmk": "Open specified in-app page", // fallback from en
  "k_0m2kfu2": "Enter Your Admin Name", // fallback from en
  "k_0m8vmxv": "<0>Single-device login</0> allows single-device login across web, Windows, Android, or iOS devices.", // fallback from en
  "k_0mde1dj": "App created successfully", // fallback from en
  "k_0mhkzcr": "UserSig Tool", // fallback from en
  "k_0mjvh8p": "Key Change Note", // fallback from en
  "k_0myc6d5": "Push Message Tool", // fallback from en
  "k_0mz00y8": "string", // fallback from en
  "k_0n43wxx": "Does not exceed 30 bytes", // fallback from en
  "k_0na3t08": "What should I do if I cannot receive offline push?", // fallback from en
  "k_0nd8yaw": "Enter full UserSig info", // fallback from en
  "k_0nej0n2": "答复：单聊/群聊消息默认保存7天，您可在此页面的自助延长历史消息存储时长，最大支持延长12个月。延长历史消息存储时长属于增值服务，具体计费说明请参考<1>价格说明</1>。", // fallback from en
  "k_0nfazsw": "Maximum Concurrently Online Users", // fallback from en
  "k_0ntk04j": "Message Sending Groups", // fallback from en
  "k_0nway9f": "答复：音视频聊天室（包含广播大群）体验版可创建10个，专业版可创建50个，专业版用户可购买扩展音视频聊天室群功能包开放创建数量限制； 普通群组类型（私有群、公开群、聊天室）不设创建数量上限，请您注意如果您的峰值群组数量超过10万个（包含普通群与音视频聊天室），则需要支付一定资源费用。 具体费用为峰值群组数累计超出10万个群组，每10万（不足10万按10万计算）个群组收费1000元/月，可参考<1>价格文档</1>说明。", // fallback from en
  "k_0o0b8eh": "Enter the correct package name", // fallback from en
  "k_0o13ckd": "Copied to clipboard", // fallback from en
  "k_0o9yzuv": "500 (default)", // fallback from en
  "k_0oi4qg5": "Web Client Experience", // fallback from en
  "k_0okeisj": "Build custom IM applications quickly with the UI component library", // fallback from en
  "k_0ovn9tx": "Total Registrants", // fallback from en
  "k_0owo4i8": "To facilitate TRTC integration into the current IM app, we will create a TRTC app with the same SDKAppID as the current IM app in the TRTC Console for you. After that Accounts and authentication for both can be reused.", // fallback from en
  "k_0owp2dh": "New Registrants", // fallback from en
  "k_0p5guxe": "Enter the callback URL", // fallback from en
  "k_0p68vub": "Info modified successfully", // fallback from en
  "k_0pksdno": "Public", // fallback from en
  "k_0ppza5v": "Failed to get app account info. Please retry later.", // fallback from en
  "k_0pqnvlr": "<0>答复：</0><1><0>对于该问题，可以从以下方面排查：</0><1>1.通过控制台查看自定义字段的配置是否正确。<1></1>2.确认查询请求中：请求用户是否有读取权限，群组类型是否支持该自定义字段。<3></3>3.确认该自定义字段的设置请求是否成功。<5></5>4.对于群维度的自定义字段：<7>iOS：需要在登录 IM SDK 之前，通过 TIMManager > setUserConfig > TIMUserConfig > TIMGroupInfoOption > groupCustom 进行相应的配置。<1></1>Android：需要在登录 IM SDK 之前，通过 TIMManager > setUserConfig > TIMUserConfig > TIMGroupSettings > groupInfoOptions > setCustomTags 进行相应的配置。</7>5.对于群成员维度的自定义字段:<9>iOS：需要在登录 IM SDK 之前，通过 TIMManager > setUserConfig > TIMUserConfig > TIMGroupMemberInfoOption > memberCustom 进行相应的配置。<1></1>Android：需要在登录 IM SDK 之前，通过 TIMManager > setUserConfig > TIMUserConfig > TIMGroupSettings > memberInfoOptions > setCustomTags 进行相应的配置。</9></1></1>", // fallback from en
  "k_0q37h9s": "Set as Admin", // fallback from en
  "k_0q8ogai": "Enter a registered username (optional)", // fallback from en
  "k_0rmh3sg": "Enter a maximum of 16 characters", // fallback from en
  "k_0rtjlnu": "Enter correct password", // fallback from en
  "k_0rv98a2": "App Platform Settings", // fallback from en
  "k_0rxuxos": "Can contain a maximum of 16 characters, including digits, Chinese and English characters, and underscores (_)", // fallback from en
  "k_0rykr1n": "Enter UserID", // fallback from en
  "k_0sat8je": "To facilitate TRTC integration in the current IM app, we have automatically created a TRTC app with the same SDKAppID as the current IM app in the <1>TRTC Console</1>. Accounts and authentication for both can be reused.", // fallback from en
  "k_0shdd7s": "Message Recall Settings", // fallback from en
  "k_0smmwlq": "如何实现群@消息与红包消息？", // fallback from en
  "k_0spoqb1": "Are you sure you want to delete the app?", // fallback from en
  "k_0suvnfc": "Modify Group Info", // fallback from en
  "k_0t0fhko": "A field name may contain a maximum of 16 characters, including letters, digits, and underscores (_), and cannot start with a digit.", // fallback from en
  "k_0t2i5a2": "<0>答复：</0><1><0>APNs</0><1><0>参考<1>离线推送说明文档</1>进行以下确认：</0><1>确认是否正确上传证书到腾讯云控制台。<1></1>确认在登录成功后，是否成功上传 token 到腾讯云。<3></3>确认在上报 token 时，是否上报了正确的证书 ID。<5></5>确认是否正确上报了切前后台事件。<7></7>确认消息是否只有 TIMCustomElem，且其中的 desc属性是空的。<9></9>MsgRandom 等去重标记设为一样，导致被去重无法推送。<11></11>如果是群消息，是否设置了消息不提醒选项。</1></1><2>Android</2><3><0>参考<1>离线推送说明文档</1>进行以下确认：</0><1>确认是否正确上传了推送证书。<1></1>确认是否成功上报 token。<3></3>如果不是第三方离线推送（华为，小米，魅族），确认一下 QALService 进程是否存活，不存活的情况下确实会收不到离线推送，需要依赖系统的自启动权限。<5></5>存在多进程的情况下，是否只在主进程进行了 IM SDK 的初始化，如果不是，需要修改为只在主进程初始化。<7></7>如果是第三方离线推送，例如小米、华为，魅族等，可以先通过对应的第三方控制台直接推送消息，确认手机是否可以收到，如果收不到可能存在两种原因：<9></9><10>1）用户集成第三方离线推送有问题，请按照文档操作。<1></1>2）手机兼容问题，该手机本身不能很好的兼容该离线推送，例如部分华为手机无法接收到华为的离线推送。</10>如果是 OPPO 离线推送，请确认在即时通信控制台的 Android 推送证书处填入的是 MasterSecret 而不是 AppSecret。</1></3></1>", // fallback from en
  "k_0t9vjl1": "Redirect to custom webpage after opening notifications", // fallback from en
  "k_0tstteu": "为什么获取不到群/群成员维度自定义字段值？", // fallback from en
  "k_0tzq4c7": "Add Custom Text", // fallback from en
  "k_0u0bsnq": "Certificate added successfully", // fallback from en
  "k_0u0dri2": "Certificate modified successfully", // fallback from en
  "k_0u534ua": "After disbanding a group, all group information is deleted and cannot be recovered.", // fallback from en
  "k_0ua2uym": "Select App type", // fallback from en
  "k_0uc8oa4": "Show \"Sent successfully\" After Sending Messages", // fallback from en
  "k_0ulhxdu": "Note that a maximum of 20 custom fields can be set. After a field is created, <1>the field cannot be deleted and the field name and type cannot be changed</1>.", // fallback from en
  "k_0uvd8j6": "Search the downloaded demo source code for the GenerateTestUserSig file, then paste the SDKAppID and the key to the following position.", // fallback from en
  "k_0v1v3rg": "Select login type", // fallback from en
  "k_0v2rkem": "Multi-device Login Type", // fallback from en
  "k_0v94mv1": "<0>Dual-device login</0> allows single-device login across Windows, Android, or iOS, plus simultaneous online on a web browser.", // fallback from en
  "k_0ve0f9v": "AVChatRoom", // fallback from en
  "k_0vjof87": "Callback before sending one-to-one messages", // fallback from en
  "k_0vjue1k": "Allows single-device login across web, Windows, Android, or iOS.", // fallback from en
  "k_0vnlc9r": "Key information is sensitive. Keep it confidential and do not disclose it.", // fallback from en
  "k_0w46awx": "Package Name", // fallback from en
  "k_0w6g07x": "Multi-device online", // fallback from en
  "k_0wdj2ai": "Edit Basic Info", // fallback from en
  "k_0wdv1hd": "Customize the most professional video solutions for industries such as live gaming, new media, OTT, e-Learning, Camgirl LVB, and vertical SNS. These solutions include VOD, LVB, ILVB, and IM. You must apply for and activate LVB and ILVB before use.", // fallback from en
  "k_0we6uwo": "Apply for ILVB", // fallback from en
  "k_0wpj6gr": "Select notification mode", // fallback from en
  "k_0wqtlf0": "Add iOS Certificate", // fallback from en
  "k_0wqv9za": "Enter admin name", // fallback from en
  "k_0wqwtx1": "Time Limit for Message Recall", // fallback from en
  "k_0xptaha": "Tencent Real-Time Communication (TRTC)", // fallback from en
  "k_0y162ka": "Auto Generation After App Selection", // fallback from en
  "k_0y8z7gf": "Instant Messaging", // fallback from en
  "k_0ycrm52": "Send Group Message", // fallback from en
  "k_0ycsqdj": "Callback URL is required", // fallback from en
  "k_0yex3qp": "You are using the Asymmetric Encryption mode. You can also switch to <1>HMAC-SHA256</1>.", // fallback from en
  "k_0yj3ghv": "Vivo", // fallback from en
  "k_0yj8u7w": "答复：请检查公私钥是否匹配，请不要使用即时通信 IM Demo 的私钥来生成 UserSig。", // fallback from en
  "k_0yjrdoj": "VIVO", // fallback from en
  "k_0yjrf51": "OPPO", // fallback from en
  "k_0yjtyy7": "Certificate ID", // fallback from en
  "k_0yjv33r": "Group Owner ID", // fallback from en
  "k_0yll8aq": "Login and Message", // fallback from en
  "k_0yno0cw": "Select read and write permissions for at least one form", // fallback from en
  "k_0yp6rfi": "Once deleted, the selected member is removed from the group.", // fallback from en
  "k_0ywhc4o": "Your qualification verification is under review. Try again later…", // fallback from en
  "k_0yx05qm": "Info Relationship Chain", // fallback from en
  "k_0z4mglb": "Certificate deletion will cause message pushes to be undeliverable. Are you sure you want to delete the certificate?", // fallback from en
  "k_0zbbk60": "Select push platform", // fallback from en
  "k_0zsef44": "About to Stop", // fallback from en
  "k_0zul64i": "Group Management", // fallback from en
  "k_0zuwja0": "Create App", // fallback from en
  "k_0zuwjoq": "Open App", // fallback from en
  "k_10hlrpt": "After the encryption mode is changed for IM, the TRTC app with the same SDKAppID will be accordingly modified. The switchover cannot be rolled back.", // fallback from en
  "k_10nmupn": "SDK Download", // fallback from en
  "k_10yf98m": "View", // fallback from en
  "k_111vbbn": "AppKey", // fallback from en
  "k_118tfeb": "Content Filtering - Basic Edition", // fallback from en
  "k_11c9m89": "IM allows users to block others and they will not receive one-to-one messages from people blocked by them. When this feature is enabled, the blocked people will be prompted that the messages are sent successfully. When this feature is disabled, the blocked people will be prompted that message sending fails.", // fallback from en
  "k_11k3v7e": "Field Name", // fallback from en
  "k_11k6jic": "App Name", // fallback from en
  "k_11qr9gr": "Enter group info", // fallback from en
  "k_11vsjy6": "Add Group", // fallback from en
  "k_11vvszp": "Disband Group", // fallback from en
  "k_11wzasg": "According to relevant laws, regulations, and policies, an individual user or enterprise must be qualified before using the IM service.", // fallback from en
  "k_124edr5": "Feature Introduction", // fallback from en
  "k_129whha": "Enter user name", // fallback from en
  "k_12a5xe7": "Permanently effective", // fallback from en
  "k_12bb311": "答复：音视频聊天室为广播大群方式，需要用户主动拉取在线消息，普通聊天室为消息队列方式，您可具体参考<1>群组功能介绍模块</1>详细了解不同群组的差异", // fallback from en
  "k_12cdtxf": "TRTC Trial Edition", // fallback from en
  "k_12dodgb": "Login settings", // fallback from en
  "k_12dpgk1": "Modify Configuration", // fallback from en
  "k_12dpvow": "Basic Configuration", // fallback from en
  "k_12dt0f3": "Configure Tag", // fallback from en
  "k_12evy1n": "Callback configuration", // fallback from en
  "k_12fz81s": "Quickly Run Through Your Exclusive IM Demo", // fallback from en
  "k_12gpvfr": "Add tag", // fallback from en
  "k_12kl2bp": "Certificate password", // fallback from en
  "k_12m0jql": "Interface verification error, try later", // fallback from en
  "k_12mrn1e": "Download source code", // fallback from en
  "k_12n4tgi": "Message roaming time extension is a paid project, which takes effect upon being enabled. The expense will be included in your current month's bill (billing on the first day of the next month).", // fallback from en
  "k_12s1012": "Group Details", // fallback from en
  "k_12s2mfc": "View Details", // fallback from en
  "k_12zpbtl": "PrivateKey or Key", // fallback from en
  "k_130f0ne": "Read by Group Admin", // fallback from en
  "k_132eebx": "Statistics and Analytics", // fallback from en
  "k_132hh35": "Date", // fallback from en
  "k_133ch1h": "Add Sample", // fallback from en
  "k_134jb22": "Version", // fallback from en
  "k_138acrr": "Verification Result", // fallback from en
  "k_138asca": "Sending Result", // fallback from en
  "k_13aibxp": "Smart Customer Service", // fallback from en
  "k_13apqtb": "Near expiry", // fallback from en
  "k_13k6cgk": "Callback after sending one-to-one messages", // fallback from en
  "k_13mcc8q": "Fee Info", // fallback from en
  "k_13mdpfz": "Edition Info", // fallback from en
  "k_13mdvb1": "Basic Info", // fallback from en
  "k_13mucsu": "答复：即时通信 IM 提供覆盖全球的高连通、高可靠、强安全的网络连接通道，自研多重最优寻址算法，具有全网调度能力，终端在海外登录时，IM SDK 会访问就近接入点或加速点。全球接入加速点包含：<1><0>中国：</0><1>华南、华北、华东、香港等。</1><2>海外：</2><3><0>亚洲：新加坡、印度尼西亚、阿联酋、泰国、日本、越南、印度等。</0><1>欧洲：英国、荷兰、德国、意大利、挪威、法国、俄罗斯等。</1><2>南美洲：巴西等。</2><3>北美洲：美国、加拿大、墨西哥等。</3><4>大洋洲：澳大利亚等。</4></3></1>", // fallback from en
  "k_13n8iho": "Description", // fallback from en
  "k_13pc7mu": "Group Type", // fallback from en
  "k_13qd28t": "Export Data", // fallback from en
  "k_13qhqgp": "No data", // fallback from en
  "k_13qkzcr": "Detailed data", // fallback from en
  "k_13sjmkn": "You need to activate TRTC to implement features such as voice call, video call, and interactive live streaming in the current IM app.", // fallback from en
  "k_13spdki": "Send Message", // fallback from en
  "k_13st3vh": "One-to-One Message", // fallback from en
  "k_13uvnuw": "My IM Apps", // fallback from en
  "k_13vfixb": "MasterSecret", // fallback from en
  "k_13wcyed": "Online Status", // fallback from en
  "k_14exniz": "360 days (2,500 CNY/month)", // fallback from en
  "k_14loa2i": "PrivateKey", // fallback from en
  "k_14v2syf": "Up to 300 characters", // fallback from en
  "k_14w5bjq": "Increase the upper limit of members per group", // fallback from en
  "k_14wr88k": "180 days (1,500 CNY/month)", // fallback from en
  "k_1510klj": "Product Documentation", // fallback from en
  "k_151cxan": "Unreadable by All", // fallback from en
  "k_1530tfo": "Solution", // fallback from en
  "k_1565kfg": "Content Filtering", // fallback from en
  "k_1588xfy": "Failure Reason", // fallback from en
  "k_15aedsq": "Go to Console", // fallback from en
  "k_15cmkoc": "Writable by Group Owner", // fallback from en
  "k_15djzvx": "Callback after speaking in a group", // fallback from en
  "k_15f00d2": "Add Member", // fallback from en
  "k_15huy9o": "Enter sender ID", // fallback from en
  "k_15ibtrm": "Certificate Type", // fallback from en
  "k_15iix93": "Field Type", // fallback from en
  "k_15inep5": "App Type", // fallback from en
  "k_15th1mo": "Enter package name", // fallback from en
  "k_15w8k4q": "Upgrade successful", // fallback from en
  "k_15w8nfi": "Stopped successfully", // fallback from en
  "k_15w8p2y": "Set successfully", // fallback from en
  "k_15w8xcw": "Downgraded successfully", // fallback from en
  "k_15wa1tg": "Cancelled successfully", // fallback from en
  "k_15wbdai": "Disbanded successfully", // fallback from en
  "k_15wbkm5": "Modified successfully", // fallback from en
  "k_15wbxe7": "Switched successfully", // fallback from en
  "k_15wdikr": "Created successfully", // fallback from en
  "k_15wedv5": "Added successfully", // fallback from en
  "k_15wgczk": "Uploaded successfully", // fallback from en
  "k_15wpcjr": "Verified successfully", // fallback from en
  "k_15wpfw0": "Activated successfully", // fallback from en
  "k_15wpiai": "Delivered successfully", // fallback from en
  "k_15wra7p": "Removed successfully", // fallback from en
  "k_15wrir2": "Deleted successfully", // fallback from en
  "k_15wrzor": "Muted successfully", // fallback from en
  "k_15xggbl": "Required for Android 8.0 or later. Otherwise, no message is pushed.", // fallback from en
  "k_161zzkm": "Please enter the user name", // fallback from en
  "k_1633q59": "Your must use the same SDKAppID when integrating IM SDK and TRTC SDK. Only in this case the accounts and authentication for both can be reused.", // fallback from en
  "k_166f6a7": "Push Platform", // fallback from en
  "k_16bl9ui": "Can an account be deleted?", // fallback from en
  "k_16f7z37": "Getting Started", // fallback from en
  "k_16fy8s6": "Start Now", // fallback from en
  "k_16ipfgt": "Mute for one day", // fallback from en
  "k_16kxf0o": "Dual-device login", // fallback from en
  "k_16kxf4h": "Single-device login", // fallback from en
  "k_16kxj7x": "Triple-device login", // fallback from en
  "k_16vnybu": "Last 7 Days", // fallback from en
  "k_179x553": "This tool is used for self-service query when offline pushes cannot be received.", // fallback from en
  "k_17aq8im": "Message content", // fallback from en
  "k_17bw8lt": "Production environment", // fallback from en
  "k_17bz1ol": "Official environment", // fallback from en
  "k_17bzlx4": "Development Environment", // fallback from en
  "k_17ik0gj": "Enter AppSecret", // fallback from en
  "k_17lz73v": "Modification can be performed only once per month.", // fallback from en
  "k_17n2xhp": "Enter group ID", // fallback from en
  "k_17zuuug": "Callback after modifying group notice", // fallback from en
  "k_181t3rs": "Last Speaking Time", // fallback from en
  "k_18ni1o4": "Select File", // fallback from en
  "k_18pq8wi": "App Intro", // fallback from en
  "k_18r3uii": "Group Custom Field", // fallback from en
  "k_18xcp69": "Redirect to the specified page after opening the app", // fallback from en
  "k_190wipy": "Select App", // fallback from en
  "k_1998vsg": "Up to 50 sensitive words can be added at a time. Please separate each word by pressing the Enter key.", // fallback from en
  "k_19iks8q": "Enter correct AppSecret", // fallback from en
  "k_19nkkdz": "Your IM {{name}} configuration will be modified and take effect within 5 minutes. Are you sure you want to modify it?", // fallback from en
  "k_19q2buj": "Member Role", // fallback from en
  "k_19t2tkp": "Add Certificate", // fallback from en
  "k_19u2xvi": "Callback after group portrait URL change", // fallback from en
  "k_1a3zo7y": "Android", // fallback from en
  "k_1a7ncq4": "Can contain a maximum of 16 characters, including digits, letters, and underscores (_)", // fallback from en
  "k_1acb5am": "Manage Group Member", // fallback from en
  "k_1ag2ot4": "Callback URL not configured", // fallback from en
  "k_1ahi3sb": "Enter webpage URL", // fallback from en
  "k_1ajjtcx": "Unreadable and Unwritable", // fallback from en
  "k_1akqoqa": "Add App", // fallback from en
  "k_1azeo33": "Note: The content filtering feature takes effect in 10 minutes after being enabled.", // fallback from en
  "k_1bgb5hg": "Android Platform Push Settings", // fallback from en
  "k_1bv3w3q": "Pro Edition - Pay-as-you-go", // fallback from en
  "k_1bw2je0": "Flagship Edition - Pay-as-you-go", // fallback from en
  "k_1bztri9": "I understand that after adding a custom field and group type, the read-write permissions of the group type can be modified, but the custom field cannot be deleted.", // fallback from en
  "k_1ce1yxz": "A field name must be composed of English characters, and may contain a maximum of 8 characters.", // fallback from en
  "k_1cis4nd": "App Name", // fallback from en
  "k_1csba3s": "Callback after group info modification", // fallback from en
  "k_1czk771": "Downloaded, next", // fallback from en
  "k_1dkdsue": "UserSig Generator", // fallback from en
  "k_1dn3wjo": "Callback after member entering a group", // fallback from en
  "k_1doqpxh": "10,000 per group", // fallback from en
  "k_1dsvgmi": "Callback after member leaving a group", // fallback from en
  "k_1e1ut1f": "Your IM service will be unavailable after disabling. Are you sure you want to disable the app?", // fallback from en
  "k_1egmp5c": "Integrate SDK in One Day", // fallback from en
  "k_1eq67wd": "Add Group Type", // fallback from en
  "k_1etix1u": "1. No charge for the first month enabled. For example, if you enable AVChatRoom on February 15, billing will begin from March 1, and the bill will be sent to you on April 1.", // fallback from en
  "k_1f0rg23": "Note that a maximum of 20 custom fields can be set. <1>After a field is created, the field cannot be deleted and the field name and type cannot be changed</1>.", // fallback from en
  "k_1fbyksd": "IM is not activated for the sub-account. To use a sub-account to access the IM console, contact the primary account to activate it.", // fallback from en
  "k_1fu2p4e": "Total Times of Joining Groups", // fallback from en
  "k_1fu31l7": "Total Times of Quitting Groups", // fallback from en
  "k_1fy5cec": "ChatRoom", // fallback from en
  "k_1giu2vo": "Upstream Messages", // fallback from en
  "k_1go3fhp": "答复：即时通信 IM 分为体验版与专业版，体验版永久免费，专业版包括套餐内资费与套餐外资费，具体可参考<1>价格文档</1>说明。", // fallback from en
  "k_1guwcc2": "Active Users", // fallback from en
  "k_1hy1b68": "Callback URL Configuration", // fallback from en
  "k_1i2wc30": "Offline Push Certificate Configuration", // fallback from en
  "k_1i5c257": "UserSig", // fallback from en
  "k_1iiplaz": "Total Groups", // fallback from en
  "k_1ij5fmj": "New Groups", // fallback from en
  "k_1ip3dvq": "Enter correct URL", // fallback from en
  "k_1irf0i8": "Readable by App", // fallback from en
  "k_1izrj72": "Readable by Admin", // fallback from en
  "k_1izvy2l": "Readable by Member", // fallback from en
  "k_1j1wwea": "Readable by All", // fallback from en
  "k_1j3lc7f": "BChatRoom", // fallback from en
  "k_1jr7vzx": "This tool is used to verify the validity of the UserSig you use.", // fallback from en
  "k_1jwpuwu": "Generate Bill Parameter", // fallback from en
  "k_1jyb2vw": "Note that a maximum of 5 custom fields can be set. <1>After a field is created, the field cannot be deleted and the field name and type cannot be changed</1>.", // fallback from en
  "k_1k7ax42": "Name Your App", // fallback from en
  "k_1k832iw": "Friend Custom Field", // fallback from en
  "k_1k8cli9": "User Custom Fields", // fallback from en
  "k_1k8see3": "Beginner Guide - Console", // fallback from en
  "k_1krkx08": "Learn the IM product and access process", // fallback from en
  "k_1kvto57": "Disbanded Groups", // fallback from en
  "k_1kvwgtd": "Please select \"increase the upper limit of groups a user can join\"", // fallback from en
  "k_1kydlll": "Message Senders", // fallback from en
  "k_1l1m1t6": "No Android certificates added", // fallback from en
  "k_1l2ksy6": "Callback before speaking in a group", // fallback from en
  "k_1l35gpm": "A field name may contain a maximum of 16 characters, including letters, digits, and underscores (_), and cannot start with a digit.", // fallback from en
  "k_1l4hy9h": "即时通信 IM 最多可创建多少个群组？", // fallback from en
  "k_1lay0ac": "如何在APP中实现类似微信语音的音视频通话功能？", // fallback from en
  "k_1lay3i8": "Content Filtering - Console", // fallback from en
  "k_1lmv9qz": "This tool can quickly generate a UserSig, which can be used to run through demos and to debug features.", // fallback from en
  "k_1lsbays": "Enter the certificate password", // fallback from en
  "k_1lz8tbk": "APNS Messages", // fallback from en
  "k_1m708u3": "Loading image…", // fallback from en
  "k_1m9ynu3": "Enter registered user name", // fallback from en
  "k_1mtjapp": "There are {{num}} duplicate words in the added list. Click Confirm to add.", // fallback from en
  "k_1muhdio": "iOS Client Demo (Password: 123)", // fallback from en
  "k_1mv1qo6": "Are you sure you want to disband the group?", // fallback from en
  "k_1n2qp1e": "Online Web Instances", // fallback from en
  "k_1n5ow0a": "Message content is required", // fallback from en
  "k_1ndi8dd": "Statistics and Analytics - Console", // fallback from en
  "k_1nkkhxr": "What is offline push", // fallback from en
  "k_1o4ckjl": "The generated UserSig is ", // fallback from en
  "k_1obc8vj": "My Own Readable and Writable", // fallback from en
  "k_1oc2v9g": "消息没有收到或消息丢失如何处理？", // fallback from en
  "k_1oe1ub3": "Callback after adding to blacklist", // fallback from en
  "k_1of0mk0": "Callback after deletion from blacklist", // fallback from en
  "k_1olko0s": "Basic Configuration - Console", // fallback from en
  "k_1omn1l4": "Feature Configuration- Console", // fallback from en
  "k_1omq3qv": "Callback Configuration - Console", // fallback from en
  "k_1pgqu7i": "Group Management - Console", // fallback from en
  "k_1pjtvgx": "What is the difference between AVChatRoom and ChatRoom?", // fallback from en
  "k_1pmghzm": "Enter AppKey", // fallback from en
  "k_1pmp2f1": "Enter group name", // fallback from en
  "k_1pna5kb": "Custom Page", // fallback from en
  "k_1pnj8ob": "In-app page", // fallback from en
  "k_1ppyuij": "IM allows users to recall a one-to-one or group message within a certain period of time after sending. Users can set the time limit.", // fallback from en
  "k_1puoqkr": "Select tag", // fallback from en
  "k_1pwloog": "Key", // fallback from en
  "k_1q5puqd": "*Note: The Xiaomi onNotificationMessageClicked method is called back. Apps can be opened using this method.", // fallback from en
  "k_1r42pfi": "Paste SDKAppID and the key to the specified position", // fallback from en
  "k_1rfen21": "<0>Multi-device online</0> allows users to stay online simultaneously on web, Windows, Android, and iOS devices.", // fallback from en
  "k_1rq8zxa": "Group Chat APNS Messages", // fallback from en
  "k_1rq93qn": "One-to-One Chat APNS Messages", // fallback from en
  "k_1s39st2": "Increase the upper limit of groups a user can join", // fallback from en
  "k_1s794cj": "User name is required", // fallback from en
  "k_1sb93c2": "答复：专业版中的账号不允许删除，如果您无需继续使用某个账号，您可以通过 Rest API 调用<1>账号登录态失效接口</1>使该账号所有者的登录状态失效。 体验版中的账号支持删除，您可以通过 Rest API 调用<3>账号删除接口</3>删除不再使用的账号，删除后该用户的数据将无法恢复，请谨慎处理。", // fallback from en
  "k_1sbfcg6": "Custom Sensitive Word Management", // fallback from en
  "k_1scwrbc": "Group name is required", // fallback from en
  "k_1sef2cf": "Callback before group creation", // fallback from en
  "k_1sfsrak": "Callback before adding a member to a group", // fallback from en
  "k_1sg4abf": "Callback before application to enter a group", // fallback from en
  "k_1sj1eyt": "Select certificate type", // fallback from en
  "k_1sonb3f": "The added dictionary is empty. Please enter words before submitting.", // fallback from en
  "k_1ssodq6": "Last 30 Days", // fallback from en
  "k_1t2z9v3": "Are you sure you want to mute selected members?", // fallback from en
  "k_1tjwic2": "Android Client Demo", // fallback from en
  "k_1tlm1wb": "PublicKey", // fallback from en
  "k_1tp5oig": "AVChatRooms", // fallback from en
  "k_1tpmvws": "App platform modified successfully", // fallback from en
  "k_1u5t0o6": "Event Callback Configuration", // fallback from en
  "k_1u99986": "Enter group owner ID", // fallback from en
  "k_1ucopc2": "No app platform set", // fallback from en
  "k_1uh8mfm": "Allows single-device login across Android and iOS, plus simultaneously online on Windows devices and web browsers.", // fallback from en
  "k_1uhkwe3": "Add Group Member Custom Field", // fallback from en
  "k_1v1qfyf": "提示70398错误码如何处理？", // fallback from en
  "k_1vjo7tj": "答复：如果您需要在APP中实现音视频通话功能，您需要接入实时音视频产品的SDK，可参考<1>实时音视频产品介绍</1>与<3>实时音视频接入方案</3>说明了解如何实现音视频通话功能", // fallback from en
  "k_1vq88lq": "即时通信 IM 音视频聊天室与普通聊天室有什么区别？", // fallback from en
  "k_1vt5zl6": "Enter channel ID", // fallback from en
  "k_1vudlxx": "答复：音视频聊天室为广播大群方式，需要用户主动拉取在线消息，普通聊天室为消息队列方式，您可具体参考<1>群组功能介绍</1>模块详细了解不同群组的差异", // fallback from en
  "k_1w0jwmm": "Activate TRTC", // fallback from en
  "k_1w4ws8c": "Readable but Unwritable", // fallback from en
  "k_1wfun59": "Unwritable by All", // fallback from en
  "k_1wl8w73": "答复：您好，即时通信 IM 体验版最大仅支持导入100个账号，如超过100个账号，将会提示70398问题，您可购买专业版，或调用<1>账号删除接口</1>删除已经导入的账号。", // fallback from en
  "k_1wnj5dr": "Please enter the message content", // fallback from en
  "k_1wp8ll9": "Earlier Server Key", // fallback from en
  "k_1wz795r": "Allows single-device login across Windows, Android, and iOS, plus simultaneously online on web browsers.", // fallback from en
  "k_1x488sq": "5,000", // fallback from en
  "k_1x488ss": "3,000", // fallback from en
  "k_1x488st": "2,000", // fallback from en
  "k_1x488su": "1,000", // fallback from en
  "k_1x5cj1k": "Writable by Group Admin", // fallback from en
  "k_1x7t9sd": "Mini Program Demo", // fallback from en
  "k_1x8dlq5": "即时通信 IM 服务是否支持海外使用？", // fallback from en
  "k_1x929et": "Except Meizu, specified in-app pages must start with intent://", // fallback from en
  "k_1xgkbdx": "Upper Limit of Groups A User Can Join", // fallback from en
  "k_1xnw925": "Edit App Platform", // fallback from en
  "k_1xor779": "Enter app name", // fallback from en
  "k_1y785li": "It takes only three steps to integrate IM SDK into your app", // fallback from en
  "k_1y9xdln": "200 per group (default)", // fallback from en
  "k_1yakobu": "iOS Platform Push Setting", // fallback from en
  "k_1yg0fr2": "Relationship Check", // fallback from en
  "k_1yhqub7": "{{wordNum}} custom words entered, max 50 allowed.", // fallback from en
  "k_1yspedq": "Make sure to remove this admin?", // fallback from en
  "k_1yzbd8z": "Add Friend Custom Field", // fallback from en
};

module.exports = { translation: translation };
