upstream test_inner {
  polaris service_namespace=Test service_name=inner-api-csms-supplier timeout=1.5 fail_report=502,405;
  server *************;
}

server {
  listen 80;
  listen [::]:80;
  server_name _;

  location /api-inner/ {
    proxy_set_header Host test-inner-api-csms-supplier.polaris;
    add_header X-Proxy-Pass active;
    add_header X-Upstream-Addr $upstream_addr;
    add_header X-Upstream-Location $upstream_http_location;
    # proxy_pass http://************;
    proxy_pass http://test_inner/;
    # proxy_pass http://*************;
    rewrite /api-inner/(.*) /$1 break;
  }

  # location /file/ {
  #   alias /usr/local/services/tools_download_files/;

  #   # 下载文件类型限制，我们这里允许常见的文档，压缩包下载
  #   if ($request_filename ~* ^.*?\.(txt|log|pdf|doc|csv|docx|xls|xlsx|ppt|pptx|rar|zip|tar.gz|tar.xz|bz2|iso|js|html)$ ) {
  #     add_header Content-Disposition attachment;
  #     expires 0;
  #     add_header Cache-control no-catch;
  #   }
  # }
  location / {
    root /usr/share/nginx/html2;
    try_files $uri $uri/ /index.html;
    if ($request_filename ~* .*\.(?:htm|html)$) {
      add_header Cache-Control 'no-store, no-cache';
    }
    if ($request_filename ~* .*\.(?:js|css)$) {
      expires 7d;
    }
    if ($request_filename ~* .*\.(?:jpg|jpeg|gif|png|ico|cur|gz|svg|svgz|mp4|ogg|ogv|webm)$) {
      expires 7d;
    }
    index index.html index.htm;
  }

  # error_page  404              /404.html;

  # redirect server error pages to the static page /50x.html
  #
  error_page 500 502 503 504 /50x.html;
  location = /50x.html {
    root /usr/share/nginx/html;
  }
}
# server {
#   listen 80 default_server;
#   server_name _;
#   return 403;
# }
