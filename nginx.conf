user nobody;
worker_processes 8;

pid log/nginx.pid;


events {
  use epoll;
  worker_connections 100000;
}
worker_rlimit_nofile 100000;

http {
  include mime.types;
  default_type application/octet-stream;
  server_tokens off;

  server_names_hash_bucket_size 128;
  client_header_buffer_size 32k;
  large_client_header_buffers 4 32k;
  client_max_body_size 8m;

  sendfile on;
  tcp_nopush on;
  tcp_nodelay on;

  keepalive_timeout 0;

  fastcgi_connect_timeout 30;
  fastcgi_send_timeout 30;
  fastcgi_read_timeout 30;
  fastcgi_buffer_size 64k;
  fastcgi_buffers 4 64k;
  fastcgi_busy_buffers_size 128k;
  fastcgi_temp_file_write_size 128k;

  gzip on;
  gzip_min_length 1k;
  gzip_buffers 4 16k;
  gzip_http_version 1.0;
  gzip_comp_level 2;
  gzip_types text/plain application/x-javascript text/css application/xml text/javascript;
  gzip_vary on;

  charset utf-8;

  access_log off;
  log_not_found off;

  error_page 400 403 405 408 /40x.html ;
  error_page 500 502 503 504 /50x.html ;
  #INCLUDE_APP
  include ./nginx-1.conf;
}
