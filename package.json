{"name": "csms-supplier-platform", "private": true, "version": "0.1.0", "scripts": {"dev": "tsc && vite --mode development --port 8082", "dev2": "tsc && vite --mode development2 --port 8083", "build": "tsc && vite build --mode production --outDir build", "build2": "tsc && vite build --mode production2 --outDir dist2", "preview": "vite preview --outDir build --open"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "dependencies": {"@tencent/aegis-web-sdk": "^1.43.25", "@tencent/tea-app": "^2.1.15", "@tencent/tea-component": "^2.8.0", "@tencent/tea-material-pro-form": "^0.1.0", "@tencent/tea-material-pro-table": "^1.0.0", "aegis-web-sdk": "^1.36.6", "axios": "^0.21.1", "classnames": "^2.3.1", "cos-js-sdk-v5": "^1.8.2", "dayjs": "^1.10.1", "exceljs": "^4.4.0", "final-form": "^4.20.1", "jose": "^4.14.4", "js-cookie": "^2.2.1", "libphonenumber-js": "^1.10.19", "lodash": "^4.17.21", "lodash-es": "^4.17.21", "path-to-regexp": "^6.2.0", "qs": "^6.8.0", "query-string": "^7.0.1", "react": "^16.14.0", "react-dom": "^16.14.0", "react-final-form": "^6.5.9", "react-final-form-hooks": "^2.0.2", "react-router-dom": "^5.2.0", "react-scripts": "^5.0.1", "react-use": "^17.2.4", "typescript": "^4.2.4", "xlsx": "^0.18.5"}, "devDependencies": {"@tencent/eslint-config-tencent": "^1.0.0-beta.5", "@tencent/eslint-plugin-tea-i18n": "^0.1.14", "@types/lodash": "^4.14.191", "@types/react": "^16.14.35", "@types/react-dom": "^16.9.17", "@types/react-router-dom": "^5.1.7", "@vitejs/plugin-react": "^3.1.0", "cross-env": "^7.0.3", "eslint-config-prettier": "^8.3.0", "eslint-plugin-prettier": "^3.4.1", "eslint-plugin-react-hooks": "^4.6.0", "less": "^4.2.0", "less-loader": "^7.3.0", "prettier": "^2.7.1", "rollup-plugin-visualizer": "^5.4.1", "typescript": "^4.9.3", "vite": "^4.1.4"}}