import { Route, BrowserRouter, Switch, Redirect } from 'react-router-dom';
import { LayoutA } from '@src/global-components/layout/LayoutA';
import {
  getHasPermission,
  PermissionController,
  useUserInfo,
} from './PermissionController';
import aegis from '@src/utils/aegis';
import { TCTG } from './TCTG';
import { menuV1 } from './app-config';
import { menuV2 } from './app-configV2';

import { ForbiddenPage } from './view/ForbiddenPage';
import _ from 'lodash';
import { IpChangeModal } from './global-components/ip-change-modal/IpChangeModal';
import { useEffect, lazy, Suspense } from 'react';
import ReportFailureResonManage from './view/sign-report/ReportFailureResonManage';
import AsyncTaskList from './view/supplier-sign-report/AsyncTaskList';
import GuidanceManual from './view/supplier-sign-report/GuidanceManual';

const HomePage = lazy(() => import('./view/HomePage'));

const Login = lazy(() => import('./view/Login'));

const SupplierSignReportManage = lazy(
  () => import('./view/supplier-sign-report/SupplierSignReportManage'),
);

const SignReportMange = lazy(
  () => import('./view/sign-report/SignApplyManage'),
);

const SignReportMangeInner = lazy(
  () => import('./view/sign-report/SignReportManage'),
);

const SignReportMakeUpMange = lazy(
  () => import('./view/sign-report/SignApplyMakeUpManage'),
);

const SignReportMakeUpCreate = lazy(
  () => import('./view/sign-report/SignApplyMakeUpCreate'),
);

const SignApplyCreateIndex = lazy(
  () => import('./view/sign-report/SignApplyCreate/Index'),
);

const SignReportSubmit = lazy(
  () => import('./view/sign-report/SignApplySubmit'),
);

const SupplierManage = lazy(() => import('./view/supplier/SupplierManage'));

const CreateSupplier = lazy(() => import('./view/supplier/CreateSupplier'));

const UserConfig = lazy(() => import('./view/system/UserConfig'));
const IPBlock = lazy(() =>
  import('./view/system/IPBlock').then((module) => ({
    default: module.IPBlock,
  })),
);

const helper = (arr, userInfo) => {
  return arr.map((opt) => {
    if (opt.children && opt.children.length > 0) {
      // 储存下过滤之前数组的长度
      opt._children = helper(opt.children, userInfo);

      opt.children = opt._children.filter((v) => {
        return v.hasPermission && !v.hidden;
      });
      if (typeof opt.hidden === 'boolean') {
        return opt;
      }
      if (opt._children.some((v) => v.hasPermission)) {
        // children 至少有一个有权限
        opt.hasPermission = true;
        opt.hidden = false;
      } else {
        opt.hasPermission = false;
        opt.hidden = true;
      }
    } else {
      const hasPermission = getHasPermission(opt.need, userInfo);
      opt.hasPermission = !!hasPermission;
      opt.hidden = !!opt.hidden;
    }

    return opt;
  });
};

// 供应商-对外
export const AppV1 = () => {
  const userInfo = useUserInfo();
  useEffect(() => {
    const isOutProduction = window.location.host === 'isms-purchase.qcloud.com';
    if (!userInfo.supplier_name || !isOutProduction) return;
    // @ts-ignore
    aegis.config.api.reqParamHandler = (data) => {
      const params = {
        ...JSON.parse(data),
        // 上报供应商名称，便于快速搜索日志
        supplier_name: userInfo.supplier_name,
      };
      return JSON.stringify(params);
    };
  }, [userInfo]);

  return (
    <TCTG>
      <IpChangeModal />
      <BrowserRouter basename={''} forceRefresh={false}>
        <Switch>
          <Route
            path="/login"
            render={() => {
              return (
                <Suspense fallback={<div>Loading...</div>}>
                  <Login />
                </Suspense>
              );
            }}
          ></Route>

          {/* 供应商签名报备管理 */}
          <Route
            path="/sign"
            render={() => {
              return (
                <Switch>
                  <Route
                    path="/sign/report"
                    exact
                    render={() => {
                      return (
                        <LayoutA menu={menuV1}>
                          <PermissionController>
                            <SupplierSignReportManage></SupplierSignReportManage>
                          </PermissionController>
                        </LayoutA>
                      );
                    }}
                  ></Route>
                  <Redirect
                    to={{
                      pathname: '/sign/report',
                    }}
                  ></Redirect>
                </Switch>
              );
            }}
          ></Route>

          <Route
            path="/task/list"
            exact
            render={() => {
              return (
                <LayoutA menu={menuV1}>
                  <PermissionController>
                    <AsyncTaskList></AsyncTaskList>
                  </PermissionController>
                </LayoutA>
              );
            }}
          ></Route>

          <Route
            path="/system/guidance"
            exact
            render={() => {
              return (
                <LayoutA menu={menuV1}>
                  <PermissionController>
                    <GuidanceManual></GuidanceManual>
                  </PermissionController>
                </LayoutA>
              );
            }}
          ></Route>

          <Route
            path="/"
            exact
            render={() => {
              return (
                <Redirect
                  to={{
                    pathname: '/sign/report',
                  }}
                ></Redirect>
              );
            }}
          ></Route>

          <Route
            path="*"
            render={() => {
              return (
                <Suspense fallback={<div>Loading...</div>}>
                  <Login />
                </Suspense>
              );
            }}
          ></Route>
        </Switch>
      </BrowserRouter>
    </TCTG>
  );
};

// 对内
export const AppV2 = () => {
  const userInfo = useUserInfo();
  const menu = helper(
    [
      {
        children: _.cloneDeep(menuV2),
      },
    ],
    userInfo,
  )[0].children;

  return (
    <TCTG>
      <BrowserRouter basename={''} forceRefresh={false}>
        <Switch>
          <Route
            path="/sign"
            render={() => {
              return (
                <Switch>
                  <Route
                    path="/sign/report/:reportId?"
                    render={() => {
                      return (
                        <LayoutA menu={menu}>
                          <PermissionController
                            need={['/sign/report/get-list']}
                          >
                            <SignReportMangeInner></SignReportMangeInner>
                          </PermissionController>
                        </LayoutA>
                      );
                    }}
                  ></Route>
                  <Route
                    path="/sign/apply/create"
                    render={() => {
                      return (
                        <LayoutA menu={menu}>
                          <PermissionController
                            need={['/sign/apply/add', '/sign/apply/get-list']}
                          >
                            <SignApplyCreateIndex></SignApplyCreateIndex>
                          </PermissionController>
                        </LayoutA>
                      );
                    }}
                  ></Route>
                  <Route
                    path="/sign/apply/modify"
                    render={() => {
                      return (
                        <LayoutA menu={menu}>
                          <PermissionController
                            need={['/sign/apply/edit', '/sign/apply/get-list']}
                          >
                            <SignApplyCreateIndex></SignApplyCreateIndex>
                          </PermissionController>
                        </LayoutA>
                      );
                    }}
                  ></Route>
                  <Route
                    path="/sign/apply/submit/:applyId?"
                    render={() => {
                      return (
                        <LayoutA menu={menu}>
                          <PermissionController
                            need={['/sign/report/add', '/sign/report/get-list']}
                          >
                            <SignReportSubmit></SignReportSubmit>
                          </PermissionController>
                        </LayoutA>
                      );
                    }}
                  ></Route>
                  <Route
                    path="/sign/apply/:applyId?"
                    render={() => {
                      return (
                        <LayoutA menu={menu}>
                          <PermissionController need={['/sign/apply/get-list']}>
                            <SignReportMange></SignReportMange>
                          </PermissionController>
                        </LayoutA>
                      );
                    }}
                  ></Route>
                  <Route
                    path="/sign/additional-report/create"
                    render={() => {
                      return (
                        <LayoutA menu={menu}>
                          <PermissionController need={['/sign/apply/get-list']}>
                            <SignReportMakeUpCreate></SignReportMakeUpCreate>
                          </PermissionController>
                        </LayoutA>
                      );
                    }}
                  ></Route>
                  <Route
                    path="/sign/additional-report"
                    render={() => {
                      return (
                        <LayoutA menu={menu}>
                          <PermissionController need={['/sign/apply/get-list']}>
                            <SignReportMakeUpMange></SignReportMakeUpMange>
                          </PermissionController>
                        </LayoutA>
                      );
                    }}
                  ></Route>
                  <Route
                    path="/sign/failure-reason-report"
                    render={() => {
                      return (
                        <LayoutA menu={menu}>
                          <PermissionController
                            need={['/sign/report-failure-reason/add']}
                          >
                            <ReportFailureResonManage></ReportFailureResonManage>
                          </PermissionController>
                        </LayoutA>
                      );
                    }}
                  ></Route>
                  <Redirect
                    to={{
                      pathname: '/sign/apply',
                    }}
                  ></Redirect>
                </Switch>
              );
            }}
          ></Route>

          {/* 供应商管理 */}
          <Route
            path="/supplier"
            render={() => {
              return (
                <Switch>
                  <Route
                    path="/supplier/manage/create"
                    exact
                    render={() => {
                      return (
                        <LayoutA menu={menu}>
                          <PermissionController need={['/admin/supplier/add']}>
                            <CreateSupplier></CreateSupplier>
                          </PermissionController>
                        </LayoutA>
                      );
                    }}
                  ></Route>
                  <Route
                    path="/supplier/manage/modify"
                    exact
                    render={() => {
                      return (
                        <LayoutA menu={menu}>
                          <PermissionController need={['/admin/supplier/edit']}>
                            <CreateSupplier></CreateSupplier>
                          </PermissionController>
                        </LayoutA>
                      );
                    }}
                  ></Route>
                  <Route
                    path="/supplier/manage"
                    render={() => {
                      return (
                        <LayoutA menu={menu}>
                          <PermissionController
                            need={['/admin/supplier/get-list']}
                          >
                            <SupplierManage></SupplierManage>
                          </PermissionController>
                        </LayoutA>
                      );
                    }}
                  ></Route>

                  <Redirect
                    to={{
                      pathname: '/supplier/manage',
                    }}
                  ></Redirect>
                </Switch>
              );
            }}
          ></Route>

          <Route
            path="/system"
            render={() => {
              return (
                <Switch>
                  <Route
                    path="/system/user"
                    render={() => {
                      return (
                        <LayoutA menu={menu}>
                          <PermissionController
                            need={['/admin/user/get-user-list']}
                          >
                            <UserConfig></UserConfig>
                          </PermissionController>
                        </LayoutA>
                      );
                    }}
                  ></Route>
                  <Route
                    path="/system/ip-block"
                    render={() => {
                      return (
                        <LayoutA menu={menu}>
                          <PermissionController
                            need={['/admin/ip-blacklist/get-list']}
                          >
                            <IPBlock></IPBlock>
                          </PermissionController>
                        </LayoutA>
                      );
                    }}
                  ></Route>
                  <Redirect
                    to={{
                      pathname: '/system/user',
                    }}
                  ></Redirect>
                </Switch>
              );
            }}
          ></Route>

          <Route
            path="/index"
            exact
            render={() => {
              return (
                <PermissionController>
                  <LayoutA menu={menu}>
                    <HomePage />
                  </LayoutA>
                </PermissionController>
              );
            }}
          ></Route>

          <Route
            path="*"
            render={() => {
              return <ForbiddenPage />;
            }}
          ></Route>
        </Switch>
      </BrowserRouter>
    </TCTG>
  );
};
