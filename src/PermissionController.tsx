import { useMount, useLocation } from 'react-use';
import { useHistory } from 'react-router-dom';
import { UserInfo, _useUserState } from '@src/global-state';
import { GlobalLoading } from '@src/global-components/global-loading';
import { qsStringify } from '@src/utils/react-use/qsStringify';
import { loginCheck } from './apiV2/loginCheck';
import { Suspense, useEffect } from 'react';
import { isInside, isOutSide } from './const/envConst';
import { getUserInfo } from './api/loginCheck';

export const getHasPermission = (
  need: any[] | undefined,
  userInfo: UserInfo,
) => {
  const userPermission = userInfo?.permission_list ?? [];
  const isRoot = userInfo?.role === 'admin';
  const whitelist = ['/', 'index'];

  if (isRoot) return true;

  const hasPermission =
    need && need.length > 0
      ? need.every((v) => {
          return userPermission.includes(v);
        })
      : true;

  if (
    // 登录了 ，有权限
    (userInfo?.loginState && hasPermission) ||
    // 白名单路由
    whitelist.includes(window.location.pathname || '')
  ) {
    return true;
  }
  return false;
};

export const useUserInfo = () => {
  const [userInfo, setUserState] = _useUserState();
  const { loginState } = userInfo ?? {
    loginState: 'uncertain',
    permission_list: [],
    role: '',
    staff_name: '',
    user_id: 0,
  };

  async function _getUserInfo() {
    const fn = isOutSide ? getUserInfo : loginCheck;
    const userInfo = await fn();
    setUserState({
      loginState: true,
      ...userInfo.data,
    });
  }

  const cb = async () => {
    try {
      if (loginState === 'uncertain' && isInside) {
        _getUserInfo();
      }
      if (isOutSide && loginState === 'uncertain') {
        _getUserInfo();
      }
    } catch (error: any) {
      setUserState({
        loginState: false,
        permission_list: [],
      });
    }
  };

  useMount(() => {
    if (window.location.href.includes('/login')) return;
    cb();
  });
  return userInfo;
};

export const PermissionController = ({
  children,
  need,
}: {
  children: React.ReactNode;
  need?: string[];
}) => {
  const userInfo = useUserInfo();
  const history = useHistory();

  const loginState = userInfo.loginState;
  const location = useLocation();

  const hasPermission = getHasPermission(need, userInfo);
  const needPermission = need?.join(',');

  useEffect(() => {
    if (loginState === 'uncertain') return;

    if (!hasPermission && isInside) {
      history.replace({
        pathname: '/error-page',
        search: qsStringify({
          redirect: location.href,
          need: needPermission,
        }),
      });
    }
  }, [hasPermission, history, location.href, loginState, needPermission]);

  if (loginState === 'uncertain') {
    return <GlobalLoading></GlobalLoading>;
  }

  return <Suspense fallback={<div>Loading...</div>}>{children}</Suspense>;
};
