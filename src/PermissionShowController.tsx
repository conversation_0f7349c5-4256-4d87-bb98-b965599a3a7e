import { GlobalLoading } from '@src/global-components/global-loading';
import { getHasPermission, useUserInfo } from './PermissionController';

export const PermissionShowController = ({
  children,
  need,
}: {
  children: React.ReactNode;
  need?: string[];
}) => {
  const userInfo = useUserInfo();

  const loginState = userInfo.loginState;

  const hasPermission = getHasPermission(need, userInfo);

  if (loginState === 'uncertain') {
    return <GlobalLoading></GlobalLoading>;
  }

  if (hasPermission) {
    return <>{children}</>;
  }

  return null;
};
