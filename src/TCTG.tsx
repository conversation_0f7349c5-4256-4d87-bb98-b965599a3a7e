import { useGlobalLoadingCount } from './global-state/global-loading';
import { useGlobalHistory } from './global-state/global-history';
import { ConfigProvider } from '@tencent/tea-component';
import { lng } from '@tea/app/i18n';

export const TCTG = (props: { children: any }) => {
  useGlobalLoadingCount();
  useGlobalHistory();
  return (
    <ConfigProvider locale={lng as 'zh' | 'en'}>
      {props.children}
    </ConfigProvider>
  );
};
