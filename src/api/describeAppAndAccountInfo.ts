import { service } from '@src/utils/service/request';
import { ApiV3ReqType } from '@src/utils/service/cloud-api-request-type';

// import { AndroidIdentifyFormType } from '@src/view/config/im-detail-components/offline-push-components/identifier-operation/AddAndroidForm';
// import {
//   PlatName_google,
//   PlatName_huawei,
//   PlatName_oppo,
// } from '@src/view/config/im-detail-components/offline-push-components/identifier-operation/form-config';

// 后端返回时候的参数格式
// export type AndroidIdentifierApiType =
//   ApiV3ResType['DescribeAppAndAccountInfo']['andIdentifiers'][number];

// export const identifierApiTransSlot = (
//   params: AndroidIdentifierApiType,
// ): AndroidIdentifyFormType => {
//   const {
//     certUrl,
//     channelId,
//     extField,
//     id,
//     identifierId,
//     identifierKey,
//     intent,
//     notifyMode,
//     packageName,
//     platform,
//   } = params;
//   const getOpenPageUrl = () => {
//     if (notifyMode === 11) return intent;
//     return '';
//   };
//   const getNotifyMode = () => {
//     if (platform === PlatName_oppo && notifyMode === 13) return 12;
//     if ([10, 11, 12].includes(notifyMode)) return notifyMode;
//     return 10;
//   };
//   const getIntentType = () => {
//     if (platform === PlatName_oppo && notifyMode === 12) {
//       return 'intentActivity';
//     }
//     if (platform === PlatName_oppo && notifyMode === 13) {
//       return 'activity';
//     }
//     return '';
//   };

//   const getextField = () => {
//     if (platform === PlatName_huawei) {
//       return JSON.parse(extField).badge_class;
//     }
//     return '';
//   };

//   const getAddType = () => {
//     if (platform === PlatName_google) {
//       if (certUrl) {
//         return 'updateSecretFile';
//       }
//       return 'writeInformation';
//     }
//     return '';
//   };

//   const getIntent = () => {
//     if (notifyMode === 12) {
//       return intent;
//     }
//     if (platform === PlatName_oppo && notifyMode === 13) return intent;

//     return '';
//   };

//   return {
//     id,
//     packageName,
//     platform,
//     identifierId,
//     identifierKey,
//     notifyMode: getNotifyMode(),
//     openPageUrl: getOpenPageUrl(),
//     intent: getIntent(),
//     extField: getextField(),
//     channelId,
//     addType: getAddType(),
//     updateFile: certUrl,
//     intentType: getIntentType(),
//   };
// };

export const describeAppAndAccountInfo = async (
  params: ApiV3ReqType['DescribeAppAndAccountInfo'],
) => {
  return service<'DescribeAppAndAccountInfo'>(
    'DescribeAppAndAccountInfo',
    params,
  );
};
