import { ApiV3ReqType } from '@src/utils/service/cloud-api-request-type';
import { service } from '@src/utils/service/request';

export const getSignReportList = async (
  params: ApiV3ReqType['GetSignReportList'],
) => {
  return service<'GetSignReportList'>('GetSignReportList', params);
};

export const changeSignReportStatus = async (
  params: ApiV3ReqType['ChangeSignReportStatus'],
) => {
  return service<'ChangeSignReportStatus'>('ChangeSignReportStatus', params);
};

export const exportSignReport = async (
  params: ApiV3ReqType['ExportSignReport'],
) => {
  return service<'ExportSignReport'>('ExportSignReport', params);
};

export const getReportFailureReason = async (
  params: ApiV3ReqType['GetReportFailureReason'],
) => {
  return service<'GetReportFailureReason'>('GetReportFailureReason', params);
};

export const getSignReportIdList = async (
  params: ApiV3ReqType['GetSignReportIdList'],
) => {
  return service<'GetSignReportIdList'>('GetSignReportIdList', params);
};
