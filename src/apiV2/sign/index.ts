import { ApiV3ReqType } from '@src/utils/service/cloud-api-request-type';
import { serviceV2 } from '@src/utils/service/requestV2';
import qs from 'qs';
import _ from 'lodash';

export const addSignApply = async (params: ApiV3ReqType['AddSignApply']) => {
  return serviceV2<'AddSignApply'>('AddSignApply', params);
};

export const editSignApply = async (params: ApiV3ReqType['EditSignApply']) => {
  return serviceV2<'EditSignApply'>('EditSignApply', params);
};

export const deleteSignApply = async (
  params: ApiV3ReqType['DeleteSignApply'],
) => {
  return serviceV2<'DeleteSignApply'>('DeleteSignApply', params);
};

export const getSignApplyList = async (
  params: ApiV3ReqType['GetSignApplyList'],
) => {
  return serviceV2<'GetSignApplyList'>('GetSignApplyList', params);
};

export const changeSignApplyStatus = async (
  params: ApiV3ReqType['ChangeSignApplyStatus'],
) => {
  return serviceV2<'ChangeSignApplyStatus'>('ChangeSignApplyStatus', params);
};

export const submitSignApply = async (
  params: ApiV3ReqType['SubmitSignApply'],
) => {
  return serviceV2<'SubmitSignApply'>('SubmitSignApply', params);
};

export const getSignReportList = async (
  params: ApiV3ReqType['GetSignReportList'],
) => {
  console.log(params);
  return serviceV2<'GetSignReportList'>('GetSignReportList', params);
};

export const getNeedReportProvider = async (
  params: ApiV3ReqType['GetNeedReportProvider'],
) => {
  return serviceV2<'GetNeedReportProvider'>('GetNeedReportProvider', params, {
    paramsSerializer: (params) => {
      return qs.stringify(params, {
        arrayFormat: 'brackets',
      });
    },
  });
};

export const changeSignReportStatus = async (
  params: ApiV3ReqType['ChangeSignReportStatus'],
) => {
  return serviceV2<'ChangeSignReportStatus'>('ChangeSignReportStatus', params);
};

export const changeReportShowKeys = async (
  params: ApiV3ReqType['ChangeReportShowKeys'],
) => {
  return serviceV2<'ChangeReportShowKeys'>('ChangeReportShowKeys', params);
};

export const getProviderList = async (
  params: ApiV3ReqType['GetProviderList'],
) => {
  return serviceV2<'GetProviderList'>('GetProviderList', params);
};

export const getSubCode = async (params: ApiV3ReqType['GetSubCode']) => {
  return serviceV2<'GetSubCode'>('GetSubCode', params);
};

export const getSignAdditionalReport = async (
  params: ApiV3ReqType['GetSignAdditionalReport'],
) => {
  const result = await serviceV2<'GetSignAdditionalReport'>(
    'GetSignAdditionalReport',
    params,
  );
  const list = result?.data?.list;
  const { data: provider } = await getProviderList({
    page_index: 1,
    page_size: 1000,
    provider_ids: _.uniq(_.map(list, (el) => el.provider_id)),
  });
  return {
    result,
    data: {
      ...result.data,
      list: list?.map((el) => ({
        ...el,
        provider_name: _.find(
          provider?.list,
          (p) => p.provider_id === el.provider_id,
        )?.provider_name,
      })),
    },
  };
};

export const AddSignAdditionalReportByCustomer = async (
  params: ApiV3ReqType['AddSignAdditionalReportByCustomer'],
) => {
  return serviceV2<'AddSignAdditionalReportByCustomer'>(
    'AddSignAdditionalReportByCustomer',
    params,
  );
};

export const AddSignAdditionalReportByProvider = async (
  params: ApiV3ReqType['AddSignAdditionalReportByProvider'],
) => {
  return serviceV2<'AddSignAdditionalReportByProvider'>(
    'AddSignAdditionalReportByProvider',
    params,
  );
};

export const getSignAdditionalReportNeedReport = async (
  params: ApiV3ReqType['GetSignAdditionalReportNeedReport'],
) => {
  return serviceV2<'GetSignAdditionalReportNeedReport'>(
    'GetSignAdditionalReportNeedReport',
    params,
  );
};

export const ChangeSignAdditionalReportStatus = async (
  params: ApiV3ReqType['ChangeSignAdditionalReportStatus'],
) => {
  return serviceV2<'ChangeSignAdditionalReportStatus'>(
    'ChangeSignAdditionalReportStatus',
    params,
  );
};

export const DeleteSignAdditionalReport = async (
  params: ApiV3ReqType['DeleteSignAdditionalReport'],
) => {
  return serviceV2<'DeleteSignAdditionalReport'>(
    'DeleteSignAdditionalReport',
    params,
  );
};

export const getReportFailureReason = async (
  params: ApiV3ReqType['GetReportFailureReason'],
) => {
  return serviceV2<'GetReportFailureReason'>('GetReportFailureReason', params);
};

export const addReportFailureReason = async (
  params: ApiV3ReqType['AddReportFailureReason'],
) => {
  return serviceV2<'AddReportFailureReason'>('AddReportFailureReason', params);
};

export const editReportFailureReason = async (
  params: ApiV3ReqType['EditReportFailureReason'],
) => {
  return serviceV2<'EditReportFailureReason'>(
    'EditReportFailureReason',
    params,
  );
};

export const deleteReportFailureReason = async (
  params: ApiV3ReqType['DeleteReportFailureReason'],
) => {
  return serviceV2<'DeleteReportFailureReason'>(
    'DeleteReportFailureReason',
    params,
  );
};

export const sendReportEmail = async (
  params: ApiV3ReqType['SendReportEmail'],
) => {
  return serviceV2<'SendReportEmail'>('SendReportEmail', params);
};
