import { pathToRegexp } from 'path-to-regexp';
import { MenuOpt } from '@src/global-components/layout/layout-use';

// icon svg
import mainSvg from './assets/main.svg';
import mainLightSvg from './assets/main_light.svg';
import safeSettingSvg from './assets/callback-setting.svg';
import safeSettingLightSvg from './assets/callback-setting_light.svg';
import organizationSvg from './assets/organization.svg';
import organizationLightSvg from './assets/organization_light.svg';
import home from './assets/home.svg';
import homeLight from './assets/home-light.svg';

import { t } from '@src/utils/i18n';

export const menuV2: MenuOpt = [
  {
    title: t('概览'),
    pathname: '/index',
    icon: [home, homeLight],
  },
  {
    title: t('供应商信息管理'),
    pathname: '/supplier/manage',
    need: ['/admin/supplier-user/get-list'],
    icon: [organizationSvg, organizationLightSvg],
    pageTitleTemplate: (title, params, pathname, history?) => {
      const regexp1 = pathToRegexp('/supplier/manage/create');
      const regexp2 = pathToRegexp('/supplier/manage/modify');
      if (regexp1.test(pathname)) {
        return t('供应商信息管理 / 创建供应商账号');
      }
      if (regexp2.test(pathname)) {
        return t('供应商信息管理 / 编辑供应商账号（{{attr0}}）', {
          attr0: history?.location?.state?.supplier_id,
        });
      }
      return title;
    },
  },
  {
    title: t('签名报备管理'),
    icon: [mainSvg, mainLightSvg],
    children: [
      {
        title: t('需求单管理'),
        pathname: '/sign/apply',
        need: ['/sign/apply/get-list'],
        pageTitleTemplate: (title, params, pathname, history?) => {
          const regexp = pathToRegexp('/sign/apply/:applyId');
          const regexp1 = pathToRegexp('/sign/apply/create');
          const regexp3 = pathToRegexp('/sign/apply/submit/:applyId');
          const regexp4 = pathToRegexp('/sign/apply/modify');
          if (regexp.test(pathname)) {
            return t('需求单管理');
          }
          if (regexp1.test(pathname)) {
            return t('需求单管理 / 新建签名报备单');
          }
          if (regexp3.test(pathname)) {
            return t('需求单管理 / 提交签名报备单');
          }
          if (regexp4.test(pathname)) {
            return t('需求单管理 / 编辑签名报备单（{{attr0}}）', {
              attr0: history?.location?.state?.apply_id,
            });
          }
          return title;
        },
      },
      {
        title: t('报备单管理'),
        pathname: '/sign/report',
        need: ['/sign/report/get-list'],
        pageTitleTemplate: (title, params, pathname, history?) => {
          const regexp = pathToRegexp('/sign/report/:reportId');
          if (regexp.test(pathname)) {
            return t('报备单管理');
          }
          return title;
        },
      },
      {
        title: t('补报备管理'),
        pathname: '/sign/additional-report',
        need: ['/sign/apply/get-list'],
        pageTitleTemplate: (title, params, pathname, history?) => {
          const regexp = pathToRegexp('/sign/additional-report/:applyId');
          const regexp1 = pathToRegexp('/sign/additional-report/create');
          const regexp3 = pathToRegexp(
            '/sign/additional-report/submit/:applyId',
          );
          const regexp4 = pathToRegexp('/sign/additional-report/modify');
          if (regexp.test(pathname)) {
            return t('签名补报备管理');
          }
          if (regexp1.test(pathname)) {
            return t('签名补报备管理 / 新建签名补报备管理');
          }
          if (regexp3.test(pathname)) {
            return t('签名补报备管理 / 提交签名补报备管理');
          }
          if (regexp4.test(pathname)) {
            return t('签名补报备管理 / 编辑签名补报备管理（{{attr0}}）', {
              attr0: history?.location?.state?.apply_id,
            });
          }
          return title;
        },
      },
      {
        title: t('报备失败原因管理'),
        pathname: '/sign/failure-reason-report',
        need: ['/sign/report-failure-reason/add'],
        pageTitleTemplate: (title, params, pathname, history?) => {
          const regexp = pathToRegexp('/sign/additional-report/:applyId');
          if (regexp.test(pathname)) {
            return t('报备失败原因管理');
          }
          return title;
        },
      },
    ],
  },
  {
    title: t('系统配置'),
    icon: [safeSettingSvg, safeSettingLightSvg],
    children: [
      {
        title: t('用户配置'),
        pathname: '/system/user',
        need: ['/admin/user/get-user-list'],
      },
      {
        title: t('IP黑名单'),
        pathname: '/system/ip-block',
        need: ['/admin/ip-blacklist/get-list'],
      },
    ],
  },
];
