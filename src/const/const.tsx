import { t } from '@src/utils/i18n';
import { TableColumn, Text } from '@tencent/tea-component';
import _ from 'lodash';
import { isOutSide } from './envConst';

export interface colsType extends TableColumn {
  disabled?: boolean;
  show?: boolean;
  render?: (record: any) => React.ReactNode;
  exportRender?: (row: any) => any;
  options?: any[];
  onChange?: (value: any) => void;
}

export const SIGN_TYPE_APP = 'APP';
export const SIGN_TYPE_TRADEMARK = t('商标');
export const SIGN_TYPE_COMPANY = t('公司名');
export const SIGN_TYPE_OTHER = t('政府/机关事业单位/其他机构');

export const SIGN_REPORT_APPLY_SOURCE_INNER = 1;

export const SIGN_ADDITIONAL_REPORT_TYPE_CUSTOMER = 1;
export const SIGN_ADDITIONAL_REPORT_TYPE_PROVIDER = 0;

export const SIGN_REPORT_APPLY_STATUS_WAIT_REPORT = 0;
export const SIGN_REPORT_APPLY_STATUS_REPORTING = 1;
export const SIGN_REPORT_APPLY_STATUS_REPORT_SUCC = 2;
export const SIGN_REPORT_APPLY_STATUS_REPORT_FAIL = 3;
export const SIGN_REPORT_APPLY_STATUS_WAIT_ADD_INFO = 4;
export const SIGN_REPORT_APPLY_STATUS_STOP_USE = 5;
export const SIGN_REPORT_APPLY_STATUS_INNER_STOP_USE = 6;

export const SIGN_REPORT_STATUS_WAIT_REPORT = 0;
export const SIGN_REPORT_STATUS_REPORTING = 1;
export const SIGN_REPORT_STATUS_REPORT_SUCC = 2;
export const SIGN_REPORT_STATUS_REPORT_FAIL = 3;
export const SIGN_REPORT_STATUS_WAIT_ADD_INFO = 6;
export const SIGN_REPORT_STATUS_STOP_USE = 4;
export const SIGN_REPORT_STATUS_INNER_STOP_USE = 5;

export const SIGN_ADDITIONAL_REPORT_STATUS_WAIT_ERPORT = 0;
export const SIGN_ADDITIONAL_REPORT_STATUS_REPORTING = 1;
export const SIGN_ADDITIONAL_REPORT_STATUS_REPORT_SUCC = 2;
export const SIGN_ADDITIONAL_REPORT_STATUS_REPORT_FAIL = 3;

export const signApplyStatusOptions = [
  {
    value: SIGN_REPORT_APPLY_STATUS_WAIT_REPORT,
    text: t('待报备'),
    theme: 'warning',
  },
  {
    value: SIGN_REPORT_APPLY_STATUS_REPORTING,
    text: t('报备中'),
    theme: 'label',
  },
  {
    value: SIGN_REPORT_APPLY_STATUS_REPORT_SUCC,
    text: t('报备成功'),
    theme: 'success',
  },
  {
    value: SIGN_REPORT_APPLY_STATUS_REPORT_FAIL,
    text: t('报备失败'),
    theme: 'danger',
  },
  {
    value: SIGN_REPORT_APPLY_STATUS_WAIT_ADD_INFO,
    text: t('资料异常'),
    theme: 'warning',
  },
  {
    value: SIGN_REPORT_APPLY_STATUS_STOP_USE,
    text: t('供应商停用'),
    theme: 'danger',
  },
  {
    value: SIGN_REPORT_APPLY_STATUS_INNER_STOP_USE,
    text: t('客户停用'),
    theme: 'danger',
  },
];

export const signReportStatus = [
  { value: SIGN_REPORT_STATUS_WAIT_REPORT, text: t('待报备'), theme: 'label' },
  { value: SIGN_REPORT_STATUS_REPORTING, text: t('报备中'), theme: 'label' },
  {
    value: SIGN_REPORT_STATUS_REPORT_SUCC,
    text: t('报备成功'),
    theme: 'success',
  },
  {
    value: SIGN_REPORT_STATUS_REPORT_FAIL,
    text: t('报备失败'),
    theme: 'danger',
  },
  {
    value: SIGN_REPORT_STATUS_STOP_USE,
    text: isOutSide ? t('停用') : t('供应商停用'),
    theme: 'danger',
  },
  {
    value: SIGN_REPORT_STATUS_INNER_STOP_USE,
    text: t('客户停用'),
    theme: 'danger',
  },
  {
    value: SIGN_REPORT_STATUS_WAIT_ADD_INFO,
    text: t('资料异常'),
    theme: 'warning',
  },
];

export const signAdditionalReportStatus = [
  {
    value: SIGN_ADDITIONAL_REPORT_STATUS_WAIT_ERPORT,
    text: t('待报备'),
    theme: 'warning',
  },
  {
    value: SIGN_ADDITIONAL_REPORT_STATUS_REPORTING,
    text: t('报备中'),
    theme: 'label',
  },
  {
    value: SIGN_ADDITIONAL_REPORT_STATUS_REPORT_SUCC,
    text: t('报备成功'),
    theme: 'success',
  },
  {
    value: SIGN_ADDITIONAL_REPORT_STATUS_REPORT_FAIL,
    text: t('报备失败'),
    theme: 'danger',
  },
];

export const remarkTypeOptions = [
  { text: t('公司'), value: SIGN_TYPE_COMPANY },
  { text: t('APP'), value: SIGN_TYPE_APP },
  { text: t('商标'), value: SIGN_TYPE_TRADEMARK },
  {
    text: t('政府/机关事业单位/其他机构'),
    value: SIGN_TYPE_OTHER,
  },
];

export const smsTypeOptions = [
  { text: t('行业'), value: '0' },
  { text: t('营销'), value: '1' },
];

// 可选展示项
export const signShowKeys = [
  'registration_num',
  'registration_url',
  'attach_url',
  'app_resource_url',
  'company_name',
  'unisc_id',
  'company_file_url',
  'corp_cr_type',
  'corp_name',
  'corp_cr_num',
  'corp_idcard_url',
  'corp_idcard_back_url',
  'transactor_cr_type',
  'transactor_name',
  'transactor_cr_num',
  'transactor_idcard_url',
  'transactor_idcard_back_url',
  'transactor_photo_idcard_url',
  'transactor_phone',
  'commitment_letter_url',
  'authorization_letter_url',
  'other_attach_url',
];

// 默认选中项
export const defaultSignShowKeys = [
  'registration_num',
  'company_name',
  'unisc_id',
  'corp_name',
  'corp_cr_type',
  'corp_cr_num',
  'transactor_name',
  'transactor_cr_type',
  'transactor_cr_num',
  'transactor_phone',
  'company_file_url', // 企业资质证件
  'registration_url', // ICP备案截图/商标备案截图/商标备案截图
  'attach_url', // APP应用商店截图/商标注册书截图
];

export const IDCARD = '1';
export const PASSPORT = '2';

export const idCardType = [
  {
    value: IDCARD,
    text: t('身份证'),
  },
  {
    value: PASSPORT,
    text: t('其他证件类型'),
  },
];

export const signApplyInitValues = {
  uin: '',
  qappid: '',
  sign: '',
  sms_type: '',
  remark_type: '',
  registration_num: '',
  registration_url: '',
  attach_url: '',
  app_resource_url: '',

  /* 企业信息 */
  company_name: '',
  company_file_url: '',
  unisc_id: '',

  /* 法人信息 */
  corp_cr_type: '1',
  corp_name: '',
  corp_cr_num: '',
  corp_idcard_url: '',
  corp_idcard_back_url: '',

  /* 短信业务管理员信息 */
  transactor_cr_type: '1',
  transactor_name: '',
  transactor_cr_num: '',
  transactor_idcard_url: '',
  transactor_idcard_back_url: '',
  transactor_phone: '',
  transactor_photo_idcard_url: '',

  /* 承诺书、其他附件、授权书*/
  commitment_letter_url: '',
  other_attach_url: '',
  authorization_letter_url: '',
  // 备注
  remark: '',
};

export const versions = [
  { value: '', text: t('全部') },
  { value: '3.3' },
  { value: '3.4' },
];

export function findText(options: { value: any; text: string }[], keyValue) {
  return (
    _.find(
      options,
      (item: any) => item?.value?.toString() === keyValue?.toString(),
    )?.text || '-'
  );
}

export function findValue(options: any[], text: string) {
  return _.find(options, (opt) => opt.text === text)?.value;
}

export const roleOptions = [
  // eslint-disable-next-line @tencent/tea-i18n/no-bare-zh-in-js
  { value: 'product_manager', text: '产品' },
  // eslint-disable-next-line @tencent/tea-i18n/no-bare-zh-in-js
  { value: 'developer', text: '研发' },
  // eslint-disable-next-line @tencent/tea-i18n/no-bare-zh-in-js
  // { value: 'buyer', text: '采购' },
  { value: 'operational', text: t('运营') },

  // eslint-disable-next-line @tencent/tea-i18n/no-bare-zh-in-js
  { value: 'business', text: '商务' },
];

/* eslint-disable @tencent/tea-i18n/no-bare-zh-in-js */
export const channelType = [
  { value: '1', text: '通道组' },
  { value: '2', text: '通道策略' },
];
/* eslint-enable @tencent/tea-i18n/no-bare-zh-in-js */
export const getStatusText = function (options, status: number) {
  const item =
    options.find((el) => el.value.toString() === status.toString()) ?? {};
  return <Text theme={item?.theme}>{item?.text}</Text>;
};

export const taskStatusOptions = [
  { value: 0, text: t('待启动'), theme: 'primary' },
  { value: 1, text: t('执行中'), theme: 'warning' },
  { value: 2, text: t('已完成'), theme: 'success' },
];
