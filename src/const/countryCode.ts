import { lang } from '@tea/app/i18n';
/* eslint-disable @tencent/tea-i18n/no-bare-zh-in-js */

import _ from 'lodash';

// @i18n-noscan
export const countryCode = [
  ['阿森松岛', 'AC', 'Ascension Island'],
  ['安道尔', 'AD', 'Andorra'],
  ['阿拉伯联合酋长国', 'AE', 'United Arab Emirates'],
  ['阿富汗', 'AF', 'Afghanistan'],
  ['安提瓜和巴布达', 'AG', 'Antigua and Barbuda'],
  ['安圭拉', 'AI', 'Anguilla'],
  ['阿尔巴尼亚', 'AL', 'Albania'],
  ['亚美尼亚', 'AM', 'Armenia'],
  ['荷属安的列斯', 'AN', 'Netherlands Antilles'],
  ['安哥拉', 'AO', 'Angola'],
  ['阿根廷', 'AR', 'Argentina'],
  ['美属萨摩亚', 'AS', 'American Samoa'],
  ['奥地利', 'AT', 'Austria'],
  ['澳大利亚', 'AU', 'Australia'],
  ['阿鲁巴', 'AW', 'Aruba'],
  ['阿塞拜疆', 'AZ', 'Azerbaijan'],
  ['波斯尼亚和黑塞哥维那', 'BA', 'Bosnia and Herzegovina'],
  ['巴巴多斯', 'BB', 'Barbados'],
  ['孟加拉国', 'BD', 'Bangladesh'],
  ['比利时', 'BE', 'Belgium'],
  ['布基纳法索', 'BF', 'Burkina Faso'],
  ['保加利亚', 'BG', 'Bulgaria'],
  ['巴林', 'BH', 'Bahrain'],
  ['布隆迪', 'BI', 'Burundi'],
  ['贝宁', 'BJ', 'Benin'],
  ['百慕大群岛', 'BM', 'Bermuda'],
  ['文莱', 'BN', 'Brunei'],
  ['玻利维亚', 'BO', 'Bolivia'],
  ['荷兰加勒比', 'BQ', 'Caribisch Nederland'],
  ['巴西', 'BR', 'Brazil'],
  ['巴哈马', 'BS', 'Bahamas'],
  ['不丹', 'BT', 'Bhutan'],
  ['博茨瓦纳', 'BW', 'Botswana'],
  ['白俄罗斯', 'BY', 'Belarus'],
  ['伯利兹', 'BZ', 'Belize'],
  ['加拿大', 'CA', 'Canada'],
  ['刚果民主共和国', 'CD', 'Democratic Republic of the Congo'],
  ['中非共和国', 'CF', 'Central African Republic'],
  ['刚果共和国', 'CG', 'Republic Of The Congo'],
  ['瑞士', 'CH', 'Switzerland'],
  ['象牙海岸', 'CI', 'Ivory Coast'],
  ['库克群岛', 'CK', 'Cook Islands'],
  ['智利', 'CL', 'Chile'],
  ['喀麦隆', 'CM', 'Cameroon'],
  ['哥伦比亚', 'CO', 'Colombia'],
  ['哥斯达黎加', 'CR', 'Costa Rica'],
  ['佛得角', 'CV', 'Cape Verde'],
  ['库拉索', 'CW', 'Curacao'],
  ['塞浦路斯', 'CY', 'Cyprus'],
  ['捷克共和国', 'CZ', 'Czech'],
  ['德国', 'DE', 'Germany'],
  ['吉布提', 'DJ', 'Djibouti'],
  ['丹麦', 'DK', 'Denmark'],
  ['多米尼加', 'DM', 'Dominica'],
  ['多米尼加共和国', 'DO', 'Dominican Republic'],
  ['阿尔及利亚', 'DZ', 'Algeria'],
  ['厄瓜多尔', 'EC', 'Ecuador'],
  ['爱沙尼亚', 'EE', 'Estonia'],
  ['埃及', 'EG', 'Egypt'],
  ['西撒哈拉', 'EH', 'Western Sahara'],
  ['厄立特里亚', 'ER', 'Eritrea'],
  ['西班牙', 'ES', 'Spain'],
  ['埃塞俄比亚', 'ET', 'Ethiopia'],
  ['芬兰', 'FI', 'Finland'],
  ['斐济', 'FJ', 'Fiji'],
  ['密克罗尼西亚', 'FM', 'Micronesia'],
  ['法罗群岛', 'FO', 'Faroe Islands'],
  ['法国', 'FR', 'France'],
  ['加蓬', 'GA', 'Gabon'],
  ['英国', 'GB', 'United Kingdom'],
  ['格林纳达', 'GD', 'Grenada'],
  ['格鲁吉亚', 'GE', 'Georgia'],
  ['法属圭亚那', 'GF', 'French Guiana'],
  ['根西岛', 'GG', 'Guernsey'],
  ['加纳', 'GH', 'Ghana'],
  ['直布罗陀', 'GI', 'Gibraltar'],
  ['格陵兰岛', 'GL', 'Greenland'],
  ['冈比亚', 'GM', 'Gambia'],
  ['几内亚', 'GN', 'Guinea'],
  ['瓜德罗普岛', 'GP', 'Guadeloupe'],
  ['赤道几内亚', 'GQ', 'Equatorial Guinea'],
  ['希腊', 'GR', 'Greece'],
  ['瓜地马拉', 'GT', 'Guatemala'],
  ['关岛', 'GU', 'Guam'],
  ['几内亚比绍共和国', 'GW', 'Guinea-Bissau'],
  ['圭亚那', 'GY', 'Guyana'],
  ['中国香港', 'HK', 'Hong Kong, China'],
  ['洪都拉斯', 'HN', 'Honduras'],
  ['克罗地亚', 'HR', 'Croatia'],
  ['海地', 'HT', 'Haiti'],
  ['匈牙利', 'HU', 'Hungary'],
  ['印度尼西亚', 'ID', 'Indonesia'],
  ['爱尔兰', 'IE', 'Ireland'],
  ['以色列', 'IL', 'Israel'],
  ['马恩岛', 'IM', 'Isle of Man'],
  ['印度', 'IN', 'India'],
  ['伊拉克', 'IQ', 'Iraq'],
  ['冰岛', 'IS', 'Iceland'],
  ['意大利', 'IT', 'Italy'],
  ['泽西岛', 'JE', 'Jersey'],
  ['牙买加', 'JM', 'Jamaica'],
  ['约旦', 'JO', 'Jordan'],
  ['日本', 'JP', 'Japan'],
  ['肯尼亚', 'KE', 'Kenya'],
  ['吉尔吉斯斯坦', 'KG', 'Kyrgyzstan'],
  ['柬埔寨', 'KH', 'Cambodia'],
  ['基里巴斯', 'KI', 'Kiribati'],
  ['科摩罗', 'KM', 'Comoros'],
  ['圣基茨和尼维斯', 'KN', 'Saint Kitts and Nevis'],
  ['韩国', 'KR', 'South Korea'],
  ['科威特', 'KW', 'Kuwait'],
  ['开曼群岛', 'KY', 'Cayman Islands'],
  ['哈萨克斯坦', 'KZ', 'Kazakhstan'],
  ['老挝', 'LA', 'Laos'],
  ['黎巴嫩', 'LB', 'Lebanon'],
  ['圣露西亚', 'LC', 'Saint Lucia'],
  ['列支敦士登', 'LI', 'Liechtenstein'],
  ['斯里兰卡', 'LK', 'Sri Lanka'],
  ['利比里亚', 'LR', 'Liberia'],
  ['莱索托', 'LS', 'Lesotho'],
  ['立陶宛', 'LT', 'Lithuania'],
  ['卢森堡', 'LU', 'Luxembourg'],
  ['拉脱维亚', 'LV', 'Latvia'],
  ['利比亚', 'LY', 'Libya'],
  ['摩洛哥', 'MA', 'Morocco'],
  ['摩纳哥', 'MC', 'Monaco'],
  ['摩尔多瓦', 'MD', 'Moldova'],
  ['黑山', 'ME', 'Montenegro'],
  ['马达加斯加', 'MG', 'Madagascar'],
  ['马绍尔群岛', 'MH', 'Marshall Islands'],
  ['马其顿', 'MK', 'Macedonia'],
  ['马里', 'ML', 'Mali'],
  ['缅甸', 'MM', 'Myanmar'],
  ['蒙古', 'MN', 'Mongolia'],
  ['中国澳门', 'MO', 'Macau, China'],
  ['北马利安纳群岛', 'MP', 'Northern Mariana Islands'],
  ['马丁尼克', 'MQ', 'Martinique'],
  ['毛里塔尼亚', 'MR', 'Mauritania'],
  ['蒙特塞拉特岛', 'MS', 'Montserrat'],
  ['马耳他', 'MT', 'Malta'],
  ['毛里求斯', 'MU', 'Mauritius'],
  ['马尔代夫', 'MV', 'Maldives'],
  ['马拉维', 'MW', 'Malawi'],
  ['墨西哥', 'MX', 'Mexico'],
  ['马来西亚', 'MY', 'Malaysia'],
  ['莫桑比克', 'MZ', 'Mozambique'],
  ['纳米比亚', 'NA', 'Namibia'],
  ['新喀里多尼亚', 'NC', 'New Caledonia'],
  ['尼日尔', 'NE', 'Niger'],
  ['诺福克岛', 'NF', 'Norfolk Island'],
  ['尼日利亚', 'NG', 'Nigeria'],
  ['尼加拉瓜', 'NI', 'Nicaragua'],
  ['荷兰', 'NL', 'Netherlands'],
  ['挪威', 'NO', 'Norway'],
  ['尼泊尔', 'NP', 'Nepal'],
  ['瑙鲁', 'NR', 'Nauru'],
  ['纽埃', 'NU', 'Niue'],
  ['新西兰', 'NZ', 'New Zealand'],
  ['阿曼', 'OM', 'Oman'],
  ['巴拿马', 'PA', 'Panama'],
  ['秘鲁', 'PE', 'Peru'],
  ['法属波利尼西亚', 'PF', 'French Polynesia'],
  ['巴布亚新几内亚', 'PG', 'Papua New Guinea'],
  ['菲律宾', 'PH', 'Philippines'],
  ['巴基斯坦', 'PK', 'Pakistan'],
  ['波兰', 'PL', 'Poland'],
  ['圣彼埃尔和密克隆岛', 'PM', 'Saint Pierre and Miquelon'],
  ['波多黎各', 'PR', 'Puerto Rico'],
  ['巴勒斯坦', 'PS', 'Palestinian Territory'],
  ['葡萄牙', 'PT', 'Portugal'],
  ['帕劳', 'PW', 'Palau'],
  ['巴拉圭', 'PY', 'Paraguay'],
  ['卡塔尔', 'QA', 'Qatar'],
  ['留尼汪', 'RE', 'Réunion Island'],
  ['罗马尼亚', 'RO', 'Romania'],
  ['塞尔维亚', 'RS', 'Serbia'],
  ['俄罗斯', 'RU', 'Russia'],
  ['卢旺达', 'RW', 'Rwanda'],
  ['沙特阿拉伯', 'SA', 'Saudi Arabia'],
  ['所罗门群岛', 'SB', 'Solomon Islands'],
  ['塞舌尔', 'SC', 'Seychelles'],
  ['苏丹', 'SD', 'Sudan'],
  ['瑞典', 'SE', 'Sweden'],
  ['新加坡', 'SG', 'Singapore'],
  ['斯洛文尼亚', 'SI', 'Slovenia'],
  ['斯洛伐克', 'SK', 'Slovakia'],
  ['塞拉利昂', 'SL', 'Sierra Leone'],
  ['圣马力诺', 'SM', 'San Marino'],
  ['塞内加尔', 'SN', 'Senegal'],
  ['索马里', 'SO', 'Somalia'],
  ['苏里南', 'SR', 'Suriname'],
  ['南苏丹', 'SS', 'South Sudan'],
  ['圣多美和普林西比', 'ST', 'Sao Tome and Principe'],
  ['萨尔瓦多', 'SV', 'El Salvador'],
  ['荷属圣马丁', 'SX', 'Sint Maarten (Dutch Part)'],
  ['斯威士兰', 'SZ', 'Swaziland'],
  ['特克斯和凯科斯群岛', 'TC', 'Turks and Caicos Islands'],
  ['乍得', 'TD', 'Chad'],
  ['多哥', 'TG', 'Togo'],
  ['泰国', 'TH', 'Thailand'],
  ['塔吉克斯坦', 'TJ', 'Tajikistan'],
  ['托克劳', 'TK', 'Tokelau'],
  ['东帝汶', 'TL', 'East Timor'],
  ['土库曼斯坦', 'TM', 'Turkmenistan'],
  ['突尼斯', 'TN', 'Tunisia'],
  ['汤加', 'TO', 'Tonga'],
  ['土耳其', 'TR', 'Türkiye'],
  ['特立尼达和多巴哥', 'TT', 'Trinidad and Tobago'],
  ['图瓦卢', 'TV', 'Tuvalu'],
  ['中国台湾', 'TW', 'Taiwan, China'],
  ['坦桑尼亚', 'TZ', 'Tanzania'],
  ['乌克兰', 'UA', 'Ukraine'],
  ['乌干达', 'UG', 'Uganda'],
  ['美国', 'US', 'United States'],
  ['乌拉圭', 'UY', 'Uruguay'],
  ['乌兹别克斯坦', 'UZ', 'Uzbekistan'],
  ['圣文森特和格林纳丁斯', 'VC', 'Saint Vincent and The Grenadines'],
  ['委内瑞拉', 'VE', 'Venezuela'],
  ['英属维尔京群岛', 'VG', 'Virgin Islands, British'],
  ['美属维尔京群岛', 'VI', 'Virgin Islands, US'],
  ['越南', 'VN', 'Vietnam'],
  ['瓦努阿图', 'VU', 'Vanuatu'],
  ['萨摩亚', 'WS', 'Samoa'],
  ['科索沃共和国', 'XK', 'Kosovo'],
  ['也门', 'YE', 'Yemen'],
  ['马约特', 'YT', 'Mayotte'],
  ['南非', 'ZA', 'South Africa'],
  ['赞比亚', 'ZM', 'Zambia'],
  ['津巴布韦', 'ZW', 'Zimbabwe'],
];

export const regionOptions = _.reduce(
  countryCode,
  (res: { text: string; value: string }[], item) => {
    res.push({
      text:
        lang === 'zh'
          ? item[0] + '(' + item[1] + ')'
          : item[2] + '(' + item[1] + ')',
      value: item[1],
    });
    return res;
  },
  [],
);

export const subjectLocation = regionOptions.concat({
  text: '中国大陆(CN)',
  value: 'CN',
});

export const regionOptionsEnName = _.reduce(
  countryCode,
  (res: { text: string; value: string }[], item) => {
    res.push({
      text: item[2],
      value: item[2],
    });
    return res;
  },
  [],
);

export const regionOptionsByName = _.reduce(
  countryCode,
  (res: { text: string; value: string }[], item) => {
    res.push({
      text:
        lang === 'zh'
          ? item[0] + '(' + item[1] + ')'
          : item[2] + '(' + item[1] + ')',
      value: lang === 'zh' ? item[0] : item[2],
    });
    return res;
  },
  [],
);
