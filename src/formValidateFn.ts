import { t } from '@tea/app/i18n';
import { trim } from 'lodash';

export const validateBizName = async (_value?: string) => {
  const value = trim(_value);

  if (value.length === 0) {
    return t('应用名称不能为空');
  }

  if (value.length > 16) {
    return t('最多输入16个字符');
  }
};

export const validateUrl = async (_value?: string) => {
  const value = trim(_value);
  if (value.length === 0) {
    return t('必须填写');
  }

  // ip 形式的 url
  const ipUrlReg = /^http(s)?:\/\/(\d+\.){3}(\d+)([-a-zA-Z0-9@:%_+.~#?&/=]*)$/;

  // 非 ip 形式的 url
  const expression =
    /^http(s)?:\/\/(www\.)?[-a-zA-Z0-9@:%._+~#=]{2,256}\.[a-z]{2,4}([-a-zA-Z0-9@:%_+.~#?&/=]*)$/;

  if (!expression.test(value) && !ipUrlReg.test(value)) {
    return t('请填写完整协议加链接');
  }
};

export const validateIdentifier = async (_value: string) => {
  const value = trim(_value);
  if (!value || value.length < 1) {
    return t('必须填写，长度大于 1');
  }
};

export const validateUserSig = async (_value: string) => {
  const value = trim(_value);
  if (!value || value.length < 1) {
    return t('必须填写');
  }
};

export const validateFlagMultiInstanceOnline = async (_value: number) => {
  if (_value === undefined) {
    return t('多端登录类型必须选择');
  }
};

export const validateMaxWebInstanceSupport = async (_value: number) => {
  if (_value === undefined) {
    return t('必须选择');
  }
};

export const validateUserId = async (_value?: string) => {
  const value = trim(_value);
  if (!value || value.length < 1 || value.length > 30) {
    return t('用户 ID 格式错误');
  }
};

export const validateFaceUrl = async (_value?: string) => {
  const value = trim(_value);
  if (value.length === 0) {
    return;
  }
  const expression =
    /^http(s)?:\/\/(www\.)?[-a-zA-Z0-9@:%._+~#=]{2,256}\.[a-z]{2,4}\b([-a-zA-Z0-9@:%_+.~#?&//=]*)$/;

  if (!expression.test(value)) {
    return t('图片地址格式错误，请填写完整协议加链接');
  }
};
