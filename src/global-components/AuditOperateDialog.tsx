import React, { useEffect, useState } from 'react';
import { useDialog, DialogRef } from '@src/utils/react-use/useDialog';
import {
  Modal,
  Button,
  message,
  Form,
  Input,
  TextArea,
} from '@tencent/tea-component';
import { Form as FinalForm, Field } from 'react-final-form';
import { getStatus } from '@src/utils/form/getStatus';
import { t } from '@src/utils/i18n';
import _ from 'lodash';

interface DialogProps {
  dialogRef: DialogRef;
  onSubmit: ({
    id,
    status,
    msg,
  }: {
    status: number;
    id: number;
    msg: string;
  }) => Promise<any>;
}

const validateRequired = async (value: string) => {
  if (value === undefined || value === '') {
    return t('必填');
  }
};

export const AuditOperateDialog = (props: DialogProps) => {
  const { dialogRef, onSubmit } = props;
  const [visible, setShowState, defaultVal] = useDialog<{
    status: number;
    id: number;
    caption: string;
    country_code: string;
  }>(dialogRef);
  const { caption } = defaultVal;

  const _handlerSubmit = async (vals: any) => {
    const res = await onSubmit({
      ...defaultVal,
      ...vals,
    });
    if (res?.code === 0) {
      setShowState(false);
    }
  };

  return (
    <>
      <Modal
        visible={visible}
        size="s"
        caption={caption}
        onClose={() => setShowState(false)}
      >
        <Modal.Body>
          <FinalForm
            onSubmit={_handlerSubmit}
            initialValuesEqual={(val, oldVal) => {
              return _.isEqual(val, oldVal);
            }}
          >
            {({ handleSubmit, validating, submitting }) => {
              return (
                <form onSubmit={handleSubmit}>
                  <Form layout="default">
                    <Field name="msg">
                      {({ input, meta }) => (
                        <Form.Item
                          showStatusIcon={false}
                          label={t('备注')}
                          status={getStatus(meta, validating)}
                          message={
                            getStatus(meta, validating) === 'error' &&
                            meta.error
                          }
                        >
                          <TextArea
                            {...(input as any)}
                            placeholder={t('请输入')}
                            size="m"
                            disabled={submitting}
                          />
                        </Form.Item>
                      )}
                    </Field>
                  </Form>
                  <Form.Action style={{ textAlign: 'center' }}>
                    <Button
                      type="primary"
                      htmlType="submit"
                      loading={submitting}
                    >
                      {t('提交')}
                    </Button>
                  </Form.Action>
                </form>
              );
            }}
          </FinalForm>
        </Modal.Body>
      </Modal>
    </>
  );
};
