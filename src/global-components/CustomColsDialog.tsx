import React, { useEffect, useState } from 'react';
import { useDialog, DialogRef } from '@src/utils/react-use/useDialog';
import {
  Modal,
  Button,
  Checkbox,
  message,
  TableColumn,
} from '@tencent/tea-component';
import { t } from '@src/utils/i18n';
import _ from 'lodash';
import { colsType } from '@src/const/const';

type BoxType = {
  key: string;
  disabled?: boolean;
  show?: boolean;
  width?: number | string;
  header: React.ReactNode | ((column: TableColumn) => React.ReactNode);
  render?: (record: any) => React.ReactNode;
}[];

interface DialogProps {
  onSuccess: (parmas: string[]) => void;
  current: colsType[];
  dialogRef: DialogRef;
  boxes: BoxType;
}

function reverseColsToArr(columns) {
  try {
    const result = _.reduce(
      columns,
      (res: string[], item: { key: string }) => {
        if (item.key && item.key !== 'operation') {
          res.push(item.key);
        }
        return res;
      },
      [],
    );
    return result;
  } catch (error) {
    return [];
  }
}

export function reverseArrToCols(
  boxes: BoxType,
  param: string[],
): Array<colsType> {
  const newArr = _.map(boxes, (item) => {
    return {
      ...item,
      show: param.includes(item.key),
    };
  });
  return _.filter(newArr, (v) => v.show);
}

export const CustomColsDialog = (props: DialogProps) => {
  const { dialogRef, current, onSuccess, boxes } = props;

  const [visible, setShowState] = useDialog(dialogRef);
  const [cols, setCols] = useState(reverseColsToArr(current));

  useEffect(() => {
    setCols(reverseColsToArr(current));
  }, [current, visible]);

  const handlerSubmit = () => {
    onSuccess(cols);
    setShowState(false);
  };

  return (
    <>
      <Modal
        visible={visible}
        size="s"
        caption={t('列自定义')}
        onClose={() => setShowState(false)}
      >
        <Modal.Body>
          <section>
            <Checkbox.Group value={cols} onChange={(value) => setCols(value)}>
              {boxes.map((box) => {
                return (
                  <Checkbox
                    name={box.key}
                    disabled={box.disabled}
                    key={box.key}
                  >
                    {box.header}
                  </Checkbox>
                );
              })}
            </Checkbox.Group>
          </section>
        </Modal.Body>
        <Modal.Footer>
          <Button type="primary" onClick={handlerSubmit}>
            {t('确定')}
          </Button>
          <Button
            type="weak"
            onClick={() => {
              setShowState(false);
            }}
          >
            {t('取消')}
          </Button>
        </Modal.Footer>
      </Modal>
    </>
  );
};
