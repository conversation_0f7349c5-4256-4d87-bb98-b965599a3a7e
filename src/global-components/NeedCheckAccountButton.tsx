import React, { useState } from 'react';
import { t } from '@src/utils/i18n';
import { DialogRef, useDialog } from '@src/utils/react-use/useDialog';
import {
  Button,
  Form,
  Input,
  InputAdornment,
  InputNumber,
  Modal,
} from '@tea/component';
import { Form as FinalForm, Field } from 'react-final-form';
import { getStatus } from '@src/utils/form/getStatus';
import { validateRequired } from '@src/utils/validateFn';
import { sendVerifyCode } from '@src/api/sendVerifyCode';
import _ from 'lodash';
import { useBoolean, useInterval } from 'react-use';
import { app } from '@src/utils/tea/app';

export const NeedCheckAccountButton = (props: {
  dialogRef: DialogRef;
  onSubmit: (params: any) => Promise<any>;
  children: React.ReactNode;
  route: string;
  [key: string]: any;
}) => {
  const { dialogRef, onSubmit, ...restProps } = props;
  const [visible, setShowState] = useDialog<boolean>(dialogRef);
  const [count, setCount] = useState(60);
  const [isRunning, toggleIsRunning] = useBoolean(false);

  useInterval(
    () => {
      setCount(count - 1);
      if (count <= 0) {
        toggleIsRunning(false);
        setCount(60);
      }
    },
    isRunning ? 1000 : null,
  );

  async function handleSendVerifyCode() {
    try {
      toggleIsRunning(true);
      const res = await sendVerifyCode({
        route: props.route,
      });
      console.log(res);
      if (res?.code === 0) {
        app.tips.success(t('发送成功'));
      }
    } catch (error) {
      console.log(error);
      toggleIsRunning(false);
    }
  }

  return (
    <>
      <Button
        htmlType="button"
        onClick={() => setShowState(true)}
        {...restProps}
      >
        {props.children}
      </Button>
      <Modal
        visible={visible}
        size="m"
        caption={t('敏感操作身份验证')}
        onClose={() => setShowState(false)}
      >
        <Modal.Body>
          <FinalForm
            onSubmit={onSubmit}
            initialValuesEqual={(val, oldVal) => {
              return _.isEqual(val, oldVal);
            }}
          >
            {({ handleSubmit, validating, submitting, form }) => {
              return (
                <form onSubmit={handleSubmit}>
                  <Form layout="default">
                    <Field
                      name="verify_code"
                      validateFields={[]}
                      validate={validateRequired}
                    >
                      {({ input, meta }) => (
                        <Form.Item
                          required
                          showStatusIcon={false}
                          label={t('验证码')}
                          status={getStatus(meta, validating)}
                          message={
                            getStatus(meta, validating) === 'error' &&
                            meta.error
                          }
                        >
                          <InputAdornment
                            after={
                              <Button
                                style={{ width: 130 }}
                                onClick={(e) => {
                                  handleSendVerifyCode();
                                }}
                                htmlType="button"
                                disabled={isRunning}
                                type="primary"
                              >
                                {isRunning
                                  ? t('重新获取（{{count}}s）', { count })
                                  : t('获取验证码')}
                              </Button>
                            }
                          >
                            <Input
                              autoComplete="off"
                              {...(input as any)}
                              placeholder={t('请输入邮件验证码')}
                              size="m"
                              disabled={submitting}
                            />
                          </InputAdornment>
                        </Form.Item>
                      )}
                    </Field>
                  </Form>
                  <Form.Action style={{ textAlign: 'center' }}>
                    <Button
                      type="primary"
                      htmlType="submit"
                      loading={submitting}
                    >
                      {t('提交')}
                    </Button>
                  </Form.Action>
                </form>
              );
            }}
          </FinalForm>
        </Modal.Body>
      </Modal>
    </>
  );
};
