import React, { useEffect, useMemo } from 'react';
import { Cascader } from '@tencent/tea-component';
import useAsyncRetryFunc from '@src/utils/react-use/useAsyncFunc';
import { getReportFailureReason } from '@src/api/sign';
import { getReportFailureReason as getReportFailureReason2 } from '@src/apiV2/sign';

import _ from 'lodash';
import { isOutSide } from '@src/const/envConst';
import { SIGN_REPORT_STATUS_WAIT_ADD_INFO } from '@src/const/const';

const _getReportFailureReason = isOutSide
  ? getReportFailureReason
  : getReportFailureReason2;

export default function ReportFailReasonCascader(props: {
  value: any;
  onChange: any;
  status?: number;
  initVals?: string[] | number[];
  form?: any;
}) {
  const { value, onChange, status, initVals, form } = props;
  const { value: state } = useAsyncRetryFunc(async () => {
    const result = await _getReportFailureReason({
      page_index: 1,
      page_size: 1000,
      report_status: status,
    });
    return result?.data;
  }, [status]);
  const list = useMemo(() => {
    return state?.list ?? [];
  }, [state]);

  const parentCategoryOptions = useMemo(() => {
    const result = _.groupBy(list, 'parent_category');
    return _.map(result, (items, key) => ({
      label: key,
      value: key,
      children: _.map(items, (item) => ({
        label: item.son_category,
        value: `${item.id}`,
      })),
    }));
  }, [list]);

  useEffect(() => {
    if (initVals) {
      const v = _.map(initVals, (id) => {
        const f = _.find(list, (item) => item.id === +id);
        return [f?.parent_category, +id];
      });
      onChange(v);
    }
  }, [initVals, list, onChange]);

  function handleChange(v: any) {
    if (isOutSide && status === SIGN_REPORT_STATUS_WAIT_ADD_INFO) {
      const failureIds = _.flatMap(v, (item) => item[1]);
      const signKeys = _.flatMap(failureIds, (id) => {
        return list.find((item) => item.id === +id)?.wrong_keys?.split(',');
      });
      const addWrongKeys = _.uniq(signKeys)?.filter((s) => !!s);
      const wrongKeysVal = form?.getFieldState('wrong_keys')?.value ?? [];
      wrongKeysVal.push(...addWrongKeys);
      form.change('wrong_keys', _.uniq(wrongKeysVal));
    }
    onChange(v);
  }

  return (
    <Cascader
      multiple
      clearable
      type="menu"
      value={value}
      // value={Array.isArray(value) ? value : []}
      data={parentCategoryOptions}
      onChange={handleChange}
    />
  );
}
