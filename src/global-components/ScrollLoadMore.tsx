import React, { useState, useEffect, useRef, useCallback } from 'react';
import { Select } from '@tencent/tea-component';
import { LoadingTip } from '@tencent/tea-component';
import { t } from '@src/utils/i18n';
import { useUpdateEffect } from 'react-use';

export type SelectOptionWithGroup = {
  value: string;
  text?: React.ReactNode;
  disabled?: boolean;
  groupKey?: string;
};

interface Props {
  onChange: (value, context?) => any;
  value: any;
  loadFn: (params: any) => Promise<{
    options: Array<{
      text?: string;
      value: string | number;
    }>;
    total: number;
  }>;
  reloadOn?: string;
  placeholder?: string;
  disabled?: boolean;
  defaultSearchValue?: string;
  pageSize?: number;
  [key: string]: any;
}

const ScrollLoadMore = (props: Props) => {
  const {
    onChange,
    value,
    loadFn,
    reloadOn,
    placeholder = t('请选择'),
    disabled = false,
    defaultSearchValue = '',
    pageSize = 100,
  } = props;
  const [pageNo, setPageNo] = useState(1);
  const [isFirst, setIsFirst] = useState(true);
  const [loading, setLoading] = useState(true);
  const [firstOpen, setFirstOpen] = useState(true);
  const [totalPage, setTotalPage] = useState(1);
  const [searchValue, setSearchValue] = useState<any>(defaultSearchValue || '');
  const [options, setOptions] = useState<any[]>([]);
  const timerRef = useRef<any>(null);
  const callbackRef = useRef<Props['loadFn']>();

  useEffect(() => {
    callbackRef.current = loadFn;
  }, [loadFn]);

  const fn = useCallback(
    async (isUnmount?: boolean) => {
      try {
        const res = await callbackRef.current?.({
          page_index: pageNo,
          page_size: pageSize,
          search_key: searchValue,
        });
        if (isUnmount) return;
        if (res) {
          if (isFirst) {
            setOptions(res.options);
          } else {
            setOptions((options) => {
              return [...options, ...res.options];
            });
          }
          setTotalPage(Math.ceil(Number(res.total) / 100));
        }
        setLoading(false);
      } catch (error) {
        setOptions([]);
        setLoading(false);
      }
    },
    [isFirst, pageNo, pageSize, searchValue],
  );

  useEffect(() => {
    let isUnmount = false;
    if (timerRef.current) {
      clearTimeout(timerRef.current);
      timerRef.current = null;
    }

    timerRef.current = setTimeout(() => {
      fn(isUnmount);
    }, 600);

    return () => {
      isUnmount = true;
    };
  }, [fn]);

  useUpdateEffect(() => {
    if (!isFirst || pageNo !== 1 || searchValue) {
      setIsFirst(true);
      setPageNo(1);
      !defaultSearchValue && setSearchValue('');
      setFirstOpen(true);
      return;
    }
    fn();
  }, [reloadOn]);

  useEffect(() => {
    setPageNo(1);
    setIsFirst(true);
  }, [firstOpen]);

  return (
    <Select
      type="simulate"
      appearence="button"
      searchable
      clearable
      boxSizeSync={true}
      size="m"
      onScrollBottom={() => {
        if (!loading && pageNo < totalPage) {
          setIsFirst(false);
          setLoading(true);
          setPageNo((page) => page + 1);
        }
      }}
      filter={() => true}
      options={options}
      placeholder={placeholder}
      disabled={disabled}
      value={value}
      onChange={(value, context) => {
        onChange?.(value, context);
      }}
      autoClearSearchValue={false}
      bottomTips={loading && <LoadingTip />}
      onSearch={(keyword) => {
        setSearchValue(keyword);
        setPageNo(1);
        setIsFirst(true);
      }}
      onOpen={() => {
        setFirstOpen(false);
      }}
      defaultSearchValue={defaultSearchValue}
    />
  );
};
export default ScrollLoadMore;
