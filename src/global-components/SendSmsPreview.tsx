import React from 'react';
import { Text } from '@tencent/tea-component';
import { lang } from '@tea/app/i18n';
import { t } from '@src/utils/i18n';
// @ts-ignore
import mobileBG from '@src/assets/moblie-bg.svg';
// @ts-ignore
import mobileBGEn from '@src/assets/moblie-bg-en.svg';
import { convertFeeByContent } from '@src/utils/tools';

export const SendSmsPreview = (props) => {
  const bg = lang === 'en' ? mobileBGEn : mobileBG;
  return (
    <>
      <div
        style={{
          position: 'absolute',
          height: 220,
          width: 300,
          right: 10,
          top: 0,
          overflow: 'hidden',
        }}
      >
        <div
          style={{
            width: 300,
            height: 278,
            background: 'url(' + bg + ')',
            backgroundSize: '300px 278px',
            position: 'absolute',
            left: 0,
          }}
        >
          <div style={{ position: 'absolute', top: 76, left: 26, right: 26 }}>
            <div
              style={{
                backgroundColor: '#e5f1ff',
                minHeight: 50,
                borderRadius: 4,
                maxHeight: 80,
                padding: 0,
                overflowY: 'auto',
                whiteSpace: 'pre-wrap',
              }}
            >
              {props.showContent}
            </div>
            <div
              style={{
                paddingTop: 10,
                fontSize: 12,
                maxHeight: 75,
                overflowY: 'auto',
              }}
            >
              {t('当前发送内容')}
              <Text theme="danger">
                {props.showContent.length}
                {t('字')}
              </Text>
              ，{t('预计发送条数约为')}
              <Text theme="danger">
                {convertFeeByContent(
                  lang === 'en' ? 30 : 10,
                  props.showContent,
                )}
                {t('条')}
              </Text>
              {t('短信。（实际发送时，模板变量会影响计费条数，请特别关注！）')}
            </div>
          </div>
          <div
            style={{
              position: 'absolute',
              bottom: 0,
              left: 26,
              right: 26,
              paddingBottom: 4,
            }}
          >
            <Text theme="weak" style={{ fontSize: 12 }}>
              {t('当前为短信预览内容，具体内容以真实内容为准！')}
            </Text>
          </div>
        </div>
      </div>
    </>
  );
};
