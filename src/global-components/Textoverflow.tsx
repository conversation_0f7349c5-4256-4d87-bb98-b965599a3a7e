import React from 'react';
import { Bubble, Copy, Text } from '@tencent/tea-component';

export const Textoverflow = ({
  text,
  rows,
  copyable = false,
}: {
  text: string;
  rows: number;
  copyable?: boolean;
}) => {
  return (
    <div style={{ display: 'flex', alignItems: 'center' }}>
      <Bubble content={text}>
        <Text
          style={{
            display: '-webkit-box',
            overflow: 'hidden',
            textOverflow: 'ellipsis',
            wordBreak: 'break-all',
            WebkitBoxOrient: 'vertical',
            WebkitLineClamp: rows,
          }}
        >
          {text}
        </Text>
      </Bubble>
      {copyable && <Copy text={text} />}
    </div>
  );
};
