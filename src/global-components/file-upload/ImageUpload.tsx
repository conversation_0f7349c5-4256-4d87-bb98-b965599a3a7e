import React, { useState, useRef, useMemo } from 'react';
import { t, Trans, Slot } from '@tea/app/i18n';
import { Upload } from '@tencent/tea-component/lib/upload';
import { Text } from '@tencent/tea-component/lib/text';
import { Form } from '@tencent/tea-component/lib/form';
import { Button } from '@tencent/tea-component/lib/button';
// import { DescribeCosTempKey } from '@src/apis/smsv3-api';
import { app } from '@src/utils/tea/app';
import COS from 'cos-js-sdk-v5';
import parse from 'url-parse';
import _ from 'lodash';
import { Icon } from '@tencent/tea-component';
import { useCss } from 'react-use';
import { isURL } from '@src/utils/tools';
import { getCosKey } from '@src/apiV2/getCosKey';
import { useUserInfo } from '@src/PermissionController';

interface UploadProps {
  message?: string | React.ReactNode;
  exampleTips?: string | React.ReactNode;
  onUploadResult: Function;
  isExcelFile?: boolean;
  imagUrl?: string | any;
  fileName?: string;
  className?: string | any;
  needWaterMark?: boolean;
  extraTips?: string | React.ReactNode;
  style?: any;
  label?: string | React.ReactNode;
  readonly?: boolean;
  download?: React.ReactNode;
  isSupportDragging?: boolean;
  accept?: string[];
  [key: string]: any;
}

export default function ImageUpload(props: UploadProps) {
  const userInfo = useUserInfo();
  const [file, setFile] = useState<File | null>(null);
  const [image, setImage] = useState<string | ArrayBuffer | null>(null);
  const [signImageUrl, setSignImageUrl] = useState('');
  const [status, setStatus] = useState<
    'validating' | 'error' | 'success' | undefined
  >(undefined);
  const [percent, setPercent] = useState<number | undefined>();
  const xhrRef = useRef<XMLHttpRequest | null>(null);
  const maxSize = props.isExcelFile ? 1024 * 1024 * 30.1 : 1024 * 1024 * 5.1;
  const { download, isSupportDragging = true, ...restProps } = props;

  const innerLabelClass = useCss({
    textAlign: 'center',
    marginBottom: '10px',
    '.app-smsv2-icon': {
      verticalAlign: 'top',
    },
  });

  const downloadClass = useCss({
    whiteSpace: 'nowrap',
    cursor: 'pointer',
    display: 'block',
    marginBottom: '6px',
    svg: {
      verticalAlign: 'text-bottom',
      marginRight: '8px',
    },
  });

  async function handleStart(file /* , { xhr }*/) {
    if (file?.name?.length > 50) {
      app.tips.error(t('文件名长度不能超过50个字符，请修改后上传'));
      return;
    }
    // let surName=file.name.substr(file.name.lastIndexOf('.')+1);
    setFile(file);
    getBase64(file);
    setStatus('validating');
    // xhrRef.current = xhr;
    const dt = new Date();
    const month =
      String(dt.getMonth() + 1).length < 2
        ? ['0', dt.getMonth() + 1].join('')
        : dt.getMonth() + 1;
    let staffName = userInfo?.staff_name;
    const newFolder = [
      'sign-report',
      '/',
      staffName,
      '/',
      String(dt.getFullYear()),
      '/',
      month,
    ].join('');
    const tname = file.name.split('.');
    tname.splice(tname.length - 1, 0, Math.ceil(Math.random() * 100000000));
    tname.splice(tname.length - 1, 0, Math.ceil(Math.random() * 100000000));

    const newFileName = tname.join('.');
    const encodeRemotePath = `${newFolder}/${encodeURIComponent(newFileName)}`;
    setSignImageUrl('');
    setPercent(undefined);
    try {
      getCosKey({
        file_name: encodeRemotePath,
        bucket: 'maz-consolebucket-1258344699',
      })
        .then(({ data }) => {
          const cos = new COS({
            getAuthorization(options, callback) {
              callback({
                TmpSecretId: data.tmp_sid,
                TmpSecretKey: data.tmp_skey,
                XCosSecurityToken: data.session_token,
                ExpiredTime: new Date(data.expired_time).getTime(),
                StartTime: new Date(data.start_time).getTime(),
              });
            },
            FileParallelLimit: 8, // 控制文件上传并发数
            ChunkParallelLimit: 8, // 控制单个文件下分片上传并发数
            ProgressInterval: 500, // 控制上传的 onProgress 回调的间隔
          });
          cos.putObject(
            {
              Bucket: data.bucket /* 必须 */,
              Region: 'ap-guangzhou',
              Key: encodeRemotePath /* 必须 */,
              Body: file, // 上传文件对象
            },
            (err1, _data1) => {
              console.log('err1', err1);
              if (err1) {
                console.log('err1', err1);
                app.tips.error(
                  t('上传失败，请重新上传[{{code}}]', {
                    code: err1?.code ?? 'cors',
                  }),
                );
                setStatus('error');
                setFile(null);
                setPercent(undefined);
                props.onUploadResult('');
              } else {
                setStatus('success');
                // @ts-ignore
                setFile({
                  name: encodeRemotePath,
                });
                cos.getObjectUrl(
                  {
                    Method: 'get',
                    Bucket: data.bucket /* 必须 */,
                    Region: 'ap-guangzhou',
                    Key: encodeRemotePath,
                    Sign: true,
                  },
                  (err2, data2) => {
                    if (!err2) {
                      setSignImageUrl(data2.Url);
                      props.onUploadResult?.(encodeRemotePath);
                    }
                  },
                );
              }
            },
          );
        })
        .catch((err) => {
          console.log('err', err);
          setStatus('error');
          setFile(null);
          setPercent(undefined);
          props.onUploadResult('');
          app.tips.error(t('签名失败，请重新上传。'));
        });
    } catch (error) {
      setStatus('error');
      setFile(null);
      setPercent(undefined);
      props.onUploadResult('');
      app.tips.error(t('签名失败，请重新上传。'));
    }
  }

  function beforeUpload(file, fileList, isAccepted, reason) {
    const rejectFun = () => {
      console?.error?.('........Upload File fail, please copy file info.');
      console?.error?.(file);
      setStatus('error');
      return false;
    };
    if (!isAccepted) {
      reason[0].code === 'file-too-large' &&
        app.tips.error(t('文件大小超出限制'));
      reason[0].code === 'file-invalid-type' &&
        app.tips.error(t('文件格式错误'));
      return rejectFun();
    }
    if (file?.name?.endsWith?.('xls')) {
      app.tips.error(t('不支持xls文件格式，请下载标准模板修改后上传。'));
      return rejectFun();
    }
    setFile(null);
    setStatus(undefined);
    setPercent(undefined);
    handleStart(file);
    return false;
  }

  function handleAbort() {
    if (xhrRef?.current) {
      xhrRef.current.abort();
    }
    setFile(null);
    setSignImageUrl('');
    setStatus(undefined);
    setPercent(undefined);
    props.onUploadResult('');
    return false;
  }
  function getBase64(file) {
    try {
      if (window.FileReader && file) {
        const reader = new FileReader();
        if (reader?.readAsDataURL && reader?.onloadend) {
          reader.onloadend = () => {
            setImage(reader.result);
          };
          reader.readAsDataURL(file);
        }
      } else {
        console?.error?.('can not support FileReader');
      }
    } catch (ex) {
      console?.error?.(ex);
    }
  }

  const imageName = useMemo(() => {
    try {
      return _.last(
        decodeURIComponent(
          decodeURIComponent(
            decodeURIComponent(
              parse(file?.name || props.fileName || props.imagUrl, {})
                ?.pathname,
            ),
          ),
        ).split('/'),
      );
    } catch (error) {
      return '';
    }
  }, [file?.name, props.fileName, props.imagUrl]);

  const fileType = useMemo(() => {
    return imageName?.split('.')?.pop();
  }, [imageName]);

  function renderDraggerUpload() {
    return (
      <Upload
        accept={props.accept ?? ['image/png', 'image/jpg', 'image/jpeg']}
        maxSize={maxSize}
        beforeUpload={beforeUpload}
      >
        {({ open, isDragging }) => (
          <Upload.Dragger
            className={props.className}
            filename={imageName}
            image={
              props.isExcelFile ||
              !['png', 'jpg', 'jpeg'].includes(_.lowerCase(fileType))
                ? null
                : signImageUrl !== ''
                ? signImageUrl
                : image
                ? image
                : props.imagUrl
            }
            percent={percent}
            description={
              file?.size && (
                <>
                  <p>
                    <Trans>文件大小：</Trans>
                    {Math.floor(file.size / 1024)}K{' '}
                  </p>
                </>
              )
            }
            button={
              status === 'validating' ? (
                <Text reset style={{ fontSize: 12 }} theme="label">
                  <Trans>上传中......</Trans>
                </Text>
              ) : props.readonly ? null : (
                <>
                  <Button
                    type="link"
                    onClick={(e) => {
                      e?.stopPropagation?.();
                      e?.preventDefault?.();
                      open();
                    }}
                  >
                    <Trans>重新上传</Trans>
                  </Button>
                  <Button
                    type="link"
                    style={{ marginLeft: 8 }}
                    onClick={(e) => {
                      e?.stopPropagation?.();
                      e?.preventDefault?.();
                      handleAbort();
                    }}
                  >
                    <Trans>删除</Trans>
                  </Button>
                </>
              )
            }
          >
            {isDragging ? (
              t('释放鼠标')
            ) : (
              <>
                {props.label ? (
                  <p className={innerLabelClass}>
                    <Text theme="label">
                      <Trans>
                        <Icon type="plus" />
                        <Slot content={props.label} />
                      </Trans>
                    </Text>
                  </p>
                ) : null}
                <div className={downloadClass}>
                  <a
                    onClick={(e) => {
                      e?.stopPropagation?.();
                      e?.preventDefault?.();
                      open();
                    }}
                    href="##"
                  >
                    {t('点击上传')}
                  </a>
                  <Text theme="weak">{t('/拖拽到此区域')}</Text>
                </div>
              </>
            )}
          </Upload.Dragger>
        )}
      </Upload>
    );
  }

  function renderButtonUpload() {
    return (
      <>
        <Upload
          accept={
            props.accept ??
            (props.isExcelFile
              ? [
                  '.csv',
                  '.xlsx',
                  '.xls',
                  'application/vnd.ms-works',
                  'text/csv',
                  'application/vnd.ms-excel',
                  'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
                ]
              : ['image/png', 'image/jpg', 'image/jpeg'])
          }
          maxSize={maxSize}
          beforeUpload={beforeUpload}
          {...restProps}
        >
          {({ open }) => (
            <Button
              loading={status === 'validating'}
              onClick={(e) => {
                e?.stopPropagation?.();
                e?.preventDefault?.();
                open();
              }}
            >
              {imageName ? t('重新上传') : t('点击上传')}
            </Button>
          )}
        </Upload>
        <br />
        {imageName ? (
          <Text
            theme={status === 'success' ? 'success' : 'danger'}
            reset
            style={{ display: 'inline-block', verticalAlign: -20 }}
          >
            {isURL(props.imagUrl) ? (
              <a href={props.imagUrl} target="_blank" rel="noreferrer">
                {imageName}
              </a>
            ) : (
              imageName
            )}
            <Button
              type="text"
              style={{ marginLeft: 8 }}
              onClick={(e) => {
                e?.stopPropagation?.();
                e?.preventDefault?.();
                handleAbort();
              }}
            >
              <Text theme="label">
                <Trans>
                  <Icon type="delete" style={{ verticalAlign: 'top' }} />
                  删除
                </Trans>
              </Text>
            </Button>
          </Text>
        ) : null}
      </>
    );
  }

  return (
    <Form.Control
      status={status}
      message={
        props.message
          ? props.message
          : t('请上传 png、jpg、jpeg 格式文件，大小 5MB 以内')
      }
      style={props?.style}
      className={props?.className}
    >
      {isSupportDragging ? renderDraggerUpload() : renderButtonUpload()}
      <div
        style={{
          display: 'inline-block',
          position: 'absolute',
          marginLeft: 35,
        }}
      >
        {props.exampleTips ? props.exampleTips : null}
      </div>
    </Form.Control>
  );
}
