import { t } from '@src/utils/i18n';
import {
  useDialog,
  DialogRef,
  useDialogRef,
} from '@src/utils/react-use/useDialog';
import { app } from '@src/utils/tea/app';
import {
  Modal,
  Button,
  Upload,
  TableColumn,
  TableAddon,
} from '@tencent/tea-component';
import _ from 'lodash';
import * as XLSX from 'xlsx';
import excel_icon from '@src/global-components/layout/img/excel.png';
import style from './index.module.less';
import { ImportResultDialog } from './ImportResultDialog';
import { ImportValidateDialog } from './ImportValidateDialog';
import moment from 'moment';
import { LoadingContainer } from '../loading-container';
import { useState } from 'react';

interface DialogProps<T> {
  dialogRef: DialogRef;
  columns: TableColumn[];
  save?: boolean;
  onSubmit: (params: any) => Promise<any>;
  onSuccess?: () => void;
  downloadFile: () => void;
  handleData: (data: Array<T>) => Array<T>;
  header: string[];
  goBack?: () => void;
  submitApiType?: string;
  tableAddons?: TableAddon<any>[];
  handleStart?: (params: any) => Promise<any>;
  range?: number;
  realRange?: number;
  assignStep1?: any;
  resultDialogSize?: 's' | 'm' | 'l' | 'xl' | number;
}

// 将excel的日期格式转成Date()对象;
export function getFormatDate_XLSX(serial) {
  var utc_days = Math.floor(serial - 25569);
  var utc_value = utc_days * 86400;
  var date_info = new Date(utc_value * 1000);
  var fractional_day = serial - Math.floor(serial) + 0.0000001;
  var total_seconds = Math.floor(86400 * fractional_day);
  var seconds = total_seconds % 60;
  total_seconds -= seconds;
  var hours = Math.floor(total_seconds / (60 * 60));
  var minutes = Math.floor(total_seconds / 60) % 60;
  var d = new Date(
    date_info.getFullYear(),
    date_info.getMonth(),
    date_info.getDate(),
    hours,
    minutes,
    seconds,
  );
  return moment(d).format('YYYY-MM-DD');
}

export function ImportExcelDialog<T>(props: DialogProps<T>) {
  const {
    dialogRef,
    columns,
    downloadFile,
    onSubmit,
    save,
    onSuccess,
    handleData,
    header,
    goBack,
    submitApiType,
    tableAddons,
    handleStart,
    range,
    realRange,
    assignStep1,
    resultDialogSize,
  } = props;
  const [visible, setShowState] = useDialog(dialogRef);
  const importResultRef = useDialogRef();
  const importValidateRef = useDialogRef();
  const [loading, setLoading] = useState(false);

  async function beforeUpload(
    file: File,
    fileList: File[],
    isAccepted: boolean,
  ) {
    if (!isAccepted) {
      app.tips.error(t('请检查文件类型，上传 .xlsx 文件'));
      throw new Error();
    }
    if (handleStart) {
      try {
        setLoading(true);
        await handleStart(file);
        setLoading(false);
      } catch (error) {
        setLoading(false);
        throw new Error();
      }
    }
    const render = new FileReader();

    render.readAsArrayBuffer(file);
    await new Promise((_resolve, reject) => {
      render.addEventListener('load', (e) => {
        const ab = e.target?.result;
        const wb = XLSX.read(ab, { type: 'array' });
        const wsname = wb.SheetNames[0];
        const ws = wb.Sheets[wsname];
        const data: any = XLSX.utils.sheet_to_json(ws, {
          header,
          defval: '',
          dateNF: 'string',
          range: range ?? 2,
        });
        if (!data?.length) {
          app.tips.error(t('导入数据为空'));
          reject(e);
          return;
        }
        const newArr = handleData(data);
        importResultRef.current.open({ list: newArr, submitApiType });
        setShowState(false);
        reject(1);
      });
    });
  }

  return (
    <>
      <Modal
        visible={visible}
        size="m"
        caption={t('导入数据')}
        onClose={() => setShowState(false)}
      >
        <Modal.Body>
          <LoadingContainer loading={loading}>
            <div className={style.container}>
              <div className={style.border}>
                {assignStep1 ? (
                  assignStep1
                ) : (
                  <>
                    <div>{t('1、下载excel数据模版，填写数据')}</div>
                    <img
                      src={excel_icon}
                      className={style.icon}
                      alt="excel_icon"
                    />
                    <div className={style.operate}>
                      <Button onClick={downloadFile}>{t('下载模版')}</Button>
                    </div>
                  </>
                )}
              </div>
              <div>
                <div>{t('2、上传填写好的excel文件')}</div>
                <img src={excel_icon} className={style.icon} alt="excel_icon" />
                <div className={style.operate}>
                  <Upload
                    beforeUpload={beforeUpload}
                    accept={[
                      '.csv',
                      '.xlsx',
                      '.xls',
                      'application/vnd.ms-works',
                      'text/csv',
                      'application/vnd.ms-excel',
                      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
                    ]}
                  >
                    <Button type="primary">{t('导入数据')}</Button>
                  </Upload>
                </div>
              </div>
            </div>
          </LoadingContainer>
        </Modal.Body>
      </Modal>
      <ImportValidateDialog
        dialogRef={importValidateRef}
        reUpload={() => {
          setShowState(true);
        }}
        back={goBack}
        range={range}
        realRange={realRange}
      />
      <ImportResultDialog<T>
        dialogRef={importResultRef}
        columns={columns}
        save={save}
        onSubmit={onSubmit}
        tableAddons={tableAddons}
        onSuccess={onSuccess}
        onError={(list, index) => {
          importValidateRef.current.open({ list, index });
        }}
        resultDialogSize={resultDialogSize}
      />
    </>
  );
}
