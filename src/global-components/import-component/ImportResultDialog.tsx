import { useState } from 'react';
import {
  DialogRef,
  useDialog,
  useDialogRef,
} from '@src/utils/react-use/useDialog';
import { Modal } from '@tencent/tea-component/lib/modal/ModalMain';
import { Trans, t } from '@src/utils/i18n';
import { useTableTopTip } from '@src/global-components/table-top-tip/useTableTopTip';
import { Table, Button, TableColumn, TableAddon } from '@tencent/tea-component';
import { app } from '@src/utils/tea/app';
import _ from 'lodash';

const { scrollable } = Table.addons;

interface DialogProps {
  dialogRef: DialogRef;
  columns: TableColumn[];
  onSubmit: (params: any) => Promise<any>;
  onSuccess?: () => void;
  save?: boolean;
  onError: (params: any, index: number) => void;
  tableAddons?: TableAddon<any>[];
  resultDialogSize?: 'xs' | 's' | 'm' | 'l' | 'xl' | number;
}

export function ImportResultDialog<T>(props: DialogProps) {
  const {
    dialogRef,
    columns,
    onSubmit,
    onSuccess,
    save,
    onError,
    tableAddons = [
      scrollable({
        maxHeight: 192,
        minWidth: 2600,
        virtualizedOptions: {
          height: 310,
          itemHeight: 30,
        },
      }),
    ],
    resultDialogSize,
  } = props;
  const [loading, setLoading] = useState(false);
  const [visible, setShowState, defaultVal] = useDialog<{
    list: Array<T>;
    submitApiType?: string;
  }>(dialogRef);
  const { list, submitApiType } = defaultVal;
  const taskInfoRef = useDialogRef();

  const TableTopTip = useTableTopTip({
    record: defaultVal?.list ?? [],
    loading: loading,
  });

  async function handlerSubmit(status) {
    setLoading(true);
    const times = Math.ceil(list.length / 100);
    try {
      for (let i = 0; i < times; i++) {
        const res = await onSubmit({
          status,
          params: list.slice(i * 100, i * 100 + 100) as any,
        });
        if (res?.data?.errors?.length) {
          setShowState(false);
          onError(res.data.errors, i * 100);
          setLoading(false);
          return;
        }
      }
      setLoading(false);
      app.tips.success(t('提交成功'));
      setShowState(false);
      onSuccess?.();
    } catch (error) {
      setLoading(false);
    }
  }

  async function handerTaskSubmit(status) {
    setLoading(true);
    try {
      const res = await onSubmit({
        status,
        params: list,
      });
      setLoading(false);
      setShowState(false);
      if (res?.data?.errors?.length) {
        onError(res.data.errors, 0);
        setLoading(false);
        return;
      }
      if (res.code === 0 && res.data?.task_id) {
        taskInfoRef.current.open({
          task_id: res.data?.task_id,
        });
      }
    } catch (error) {
      setLoading(false);
    }
  }

  return (
    <>
      <Modal
        visible={visible}
        size={resultDialogSize ?? 'l'}
        caption={t('导入成功')}
        onClose={() => setShowState(false)}
        disableEscape
      >
        <Modal.Body>
          <Table
            compact
            bordered
            topTip={TableTopTip}
            records={list}
            columns={_.concat(
              {
                header: t('序号'),
                key: 'id',
                width: 80,
                render: (record: any, rowKey: string, recordIndex: number) =>
                  recordIndex + 1,
              },
              columns,
            )}
            addons={[...tableAddons]}
          />
        </Modal.Body>
        <Modal.Footer>
          {save && (
            <Button
              type="primary"
              onClick={() => {
                submitApiType === 'async'
                  ? handerTaskSubmit(0)
                  : handlerSubmit(0);
              }}
              loading={loading}
            >
              {t('保存')}
            </Button>
          )}
          <Button
            type="primary"
            onClick={() => {
              submitApiType === 'async'
                ? handerTaskSubmit(1)
                : handlerSubmit(1);
            }}
            loading={loading}
          >
            {t('提交')}
          </Button>
          <Button
            type="weak"
            onClick={() => {
              setShowState(false);
            }}
            loading={loading}
          >
            {t('取消')}
          </Button>
        </Modal.Footer>
      </Modal>
      {/* <TaskInfoDialog dialogRef={taskInfoRef} /> */}
    </>
  );
}
