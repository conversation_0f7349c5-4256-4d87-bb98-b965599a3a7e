import { DialogRef, useDialog } from '@src/utils/react-use/useDialog';
import { Modal } from '@tencent/tea-component/lib/modal/ModalMain';
import { t, Trans, Slot } from '@src/utils/i18n';
import { Button, Text, Icon } from '@tencent/tea-component';
import _ from 'lodash';
import { useMemo } from 'react';

interface DialogProps {
  dialogRef: DialogRef;
  reUpload: () => void;
  back?: () => void;
  range?: number;
  realRange?: number;
}

export const ImportValidateDialog = (props: DialogProps) => {
  const { dialogRef, reUpload, back, realRange, range } = props;
  const [visible, setShowState, defaultVal] = useDialog<{
    list: any[];
    index: number;
  }>(dialogRef);
  const { list, index } = defaultVal;

  function goBack() {
    setShowState(false);
    back?.();
  }

  const count = useMemo(() => {
    return list?.length || 0;
  }, [list]);

  return (
    <>
      <Modal
        visible={visible}
        size="l"
        caption={
          <>
            <Icon
              type="error"
              size="l"
              style={{ margin: '0 10px 10px 0', cursor: 'pointer' }}
            />
            <Text>{t('操作失败')}</Text>
          </>
        }
        onClose={() => setShowState(false)}
        disableEscape
      >
        <Modal.Body>
          <div style={{ marginBottom: 15 }}>
            <Trans count={count}>
              <Text theme="danger">
                {{ count }} 条数据错误，请修改后导入重新上传
              </Text>
            </Trans>
          </div>
          <div style={{ maxHeight: 300, overflow: 'auto' }}>
            {(list ?? []).map((info, _index) => {
              return (
                <div key={_index}>
                  <Trans>
                    错误信息：导入表格第 <Slot content={info.line + index} />{' '}
                    行错误，对应模版文件第{' '}
                    <Slot
                      content={info.line + index + (realRange ?? range ?? 2)}
                    />{' '}
                    行， <Slot content={JSON.stringify(info.msg)} />
                  </Trans>
                </div>
              );
            })}
          </div>
        </Modal.Body>
        <Modal.Footer>
          <Button
            type="primary"
            onClick={() => {
              setShowState(false);
              reUpload();
            }}
          >
            {t('重新上传')}
          </Button>
          <Button type="weak" onClick={goBack}>
            {t('取消')}
          </Button>
        </Modal.Footer>
      </Modal>
    </>
  );
};
