import _ from 'lodash';

export function getExportData(boxes, list) {
  const cBoxes = _.filter(boxes, (box) => box.key !== 'operation');
  let head = _.map(cBoxes, (box) => box.header);
  let keys = _.map(cBoxes, (box) => {
    return {
      key: box.key,
      render: box.exportRender ?? box.render,
    };
  });
  let data = _.map(list, (item) => {
    return _.map(keys, (key) => {
      const info = key?.render ? key.render(item) : item[key.key];
      return info || '';
    });
  });

  return {
    head,
    data,
  };
}

export const sleep = (time) =>
  new Promise((resolve) => setTimeout(resolve, time));

export function getPrivacyInfo(value) {
  return value?.replace(
    /^(.{1})(.+)(.{1})$/g,
    (match, p1, p2, p3) => p1 + '*'.repeat(p2.length) + p3,
  );
}
