import { getLoginIp } from '@src/api/getLoginIp';
import { t, Trans, Slot } from '@src/utils/i18n';
import useAsyncRetryFunc from '@src/utils/react-use/useAsyncFunc';
import { checkRedirectToLogin } from '@src/utils/service/cloud-api-v3-request';
import { Button, Icon, Modal, Text } from '@tencent/tea-component';
import { useModalMemo } from './modalMemo';

export const IpChangeModal = () => {
  const [visible, setShowState] = useModalMemo();

  function close() {
    setShowState(false);
  }

  const {
    value: state,
    loading,
    retry,
  } = useAsyncRetryFunc(async () => {
    if (!visible) return;
    const response = await getLoginIp();
    return response?.data?.ip || '';
  }, [visible]);

  return (
    <Modal visible={visible} caption={t('IP不在白名单')} onClose={close}>
      <Modal.Body>
        <p>
          <Trans>
            您的IP：
            <Slot
              content={
                loading ? (
                  <Icon type="loading" />
                ) : (
                  <>
                    <Text copyable>{state}</Text>
                  </>
                )
              }
            />
            不在白名单，请联系管理员添加。或请
            <Button
              type="link"
              style={{ position: 'relative', top: -1 }}
              onClick={() => {
                checkRedirectToLogin();
              }}
            >
              重新登录
            </Button>
            。
          </Trans>
        </p>
      </Modal.Body>
      <Modal.Footer>
        <Button type="primary" onClick={close}>
          {t('确定')}
        </Button>
      </Modal.Footer>
    </Modal>
  );
};
