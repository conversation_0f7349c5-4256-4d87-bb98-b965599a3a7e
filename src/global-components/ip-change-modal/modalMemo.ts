import { useEffect, useState } from 'react';

interface ModalMemo {
  changeVisible: (visible: boolean) => void;
}

export const modalMemo: ModalMemo = {
  changeVisible: () => {},
};

export const useModalMemo = () => {
  const [visible, setShowState] = useState(false);

  useEffect(() => {
    modalMemo.changeVisible = setShowState;
  }, [setShowState]);

  return [visible, setShowState] as const;
};
