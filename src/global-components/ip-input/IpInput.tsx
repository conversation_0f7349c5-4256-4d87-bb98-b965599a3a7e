import React, { useState, useEffect } from 'react';
import { InputNumber, Tooltip } from '@tencent/tea-component';
import style from './ip-input.module.less';
import { t } from '@src/utils/i18n';
import { getIpString, reverseStringToIp } from './ipTurn';

interface PropType {
  onChange: (param) => void;
  value: string | undefined;
  lastUnit?: string;
}

function IpInput({ onChange, value, lastUnit }: PropType) {
  const initValue = reverseStringToIp(value);

  function handleNumberChange(value, type) {
    // 确保最小值为0；
    const number = parseInt(value || 0, 10);
    if (isNaN(number)) {
      return;
    }
    let Obj = {};
    Obj[`${type}`] = number;
    triggerChange(Obj);
  }

  function triggerChange(changedValue) {
    const obj = { ...initValue, ...changedValue };
    onChange(getIpString(obj));
  }

  return (
    <div className={style.input_group}>
      <Tooltip title={t('范围：0-255')}>
        <InputNumber
          value={initValue.ip_0}
          min={0}
          max={255}
          hideButton
          onChange={(value) => {
            handleNumberChange(value, 'ip_0');
          }}
        />
      </Tooltip>
      <span className={style.ip_dot}>.</span>
      <Tooltip title={t('范围：0-255')}>
        <InputNumber
          value={initValue.ip_1}
          min={0}
          max={255}
          hideButton
          onChange={(value) => {
            handleNumberChange(value, 'ip_1');
          }}
        />
      </Tooltip>
      <span className={style.ip_dot}>.</span>
      <Tooltip title={t('范围：0-255')}>
        <InputNumber
          value={initValue.ip_2}
          min={0}
          max={255}
          hideButton
          onChange={(value) => {
            handleNumberChange(value, 'ip_2');
          }}
        />
      </Tooltip>
      <span className={style.ip_dot}>.</span>
      <Tooltip title={t('范围：0-255')}>
        <InputNumber
          value={initValue.ip_3}
          min={0}
          max={255}
          hideButton
          onChange={(value) => {
            handleNumberChange(value, 'ip_3');
          }}
          unit={lastUnit || ''}
        />
      </Tooltip>
    </div>
  );
}
export default IpInput;
