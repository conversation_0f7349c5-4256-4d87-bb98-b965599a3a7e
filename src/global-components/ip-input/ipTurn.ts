import _ from 'lodash';

export function getIpString(params: object | undefined) {
  if (params === undefined) return '';
  const ips = _.reduce(
    params,
    (res, val) => {
      res.push(val);
      return res;
    },
    [],
  );
  return ips.join('.');
}

export function reverseStringToIp(params: string | undefined) {
  if (!params)
    return {
      ip_0: undefined,
      ip_1: undefined,
      ip_2: undefined,
      ip_3: undefined,
    };
  try {
    const ips = params.split('.');
    return {
      ip_0: +ips[0],
      ip_1: +ips[1],
      ip_2: +ips[2],
      ip_3: +ips[3],
    };
  } catch (error) {
    return {
      ip_0: 0,
      ip_1: 0,
      ip_2: 0,
      ip_3: 0,
    };
  }
}
