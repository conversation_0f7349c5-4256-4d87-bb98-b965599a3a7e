import React from 'react';
import { Layout, Card, NavMenu } from '@tencent/tea-component';
import { useCss } from 'react-use';
import { useUserInfo } from '@src/PermissionController';
import { RightDropDown } from './layout-components/RightDropDown';
import iconPng from '../../assets/logo-bg-color.svg';

const { Header, Body, Content } = Layout;

export function BaseLayout(props: {
  children?:
    | React.ReactNode
    | React.ReactFragment
    | React.ReactPortal
    | boolean
    | null
    | undefined;
  title: string;
}) {
  const { title, children } = props;
  const userInfo = useUserInfo();

  const lauoutClass = useCss({
    height: '100vh',
  });

  return (
    <Layout className={lauoutClass}>
      <Header>
        <NavMenu
          left={
            <>
              <NavMenu.Item type="default">
                <img src={iconPng} alt="logo" />
              </NavMenu.Item>
              <NavMenu.Item>{title}</NavMenu.Item>
            </>
          }
          right={
            <>
              <NavMenu.Item
                type="dropdown"
                overlay={(close) => (
                  <RightDropDown close={close}></RightDropDown>
                )}
              >
                {userInfo?.user_name}
              </NavMenu.Item>
            </>
          }
        />
      </Header>
      <Body>
        <Content>
          <Content.Body>
            <Card>
              <Card.Body>{children}</Card.Body>
            </Card>
          </Content.Body>
          <Content.Footer>{''}</Content.Footer>
        </Content>
      </Body>
    </Layout>
  );
}
