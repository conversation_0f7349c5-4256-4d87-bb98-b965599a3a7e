import React from 'react';
import { usePathname } from '@src/utils/react-use/usePathname';
import { Layout, Card, Menu, NavMenu } from '@tencent/tea-component';
import { useCss } from 'react-use';
import { RightDropDown } from './layout-components/RightDropDown';
import { MenuList } from './layout-components/MenuList';
import iconPng from '../../assets/logo-bg-color.svg';
import iconPng32 from './img/32-1.png';
import { useMenuTitle } from './layout-use';
import { MenuOpt } from './layout-use';
import { t } from '@src/utils/i18n';
import { lng } from '@tea/app/i18n';
import { isOutSide } from '@src/const/envConst';
import { useUserInfo } from '@src/PermissionController';

const { Header, Body, Sider, Content } = Layout;

// 给 子账号用的 layout
export function LayoutA(props: { menu?: MenuOpt; children?: React.ReactNode }) {
  const { children, menu = [] } = props;
  const userInfo = useUserInfo();
  const pathname = usePathname();

  const lauoutClass = useCss({
    height: '100vh',
  });
  const title = useMenuTitle(menu, pathname);
  return (
    <Layout className={lauoutClass}>
      <Header>
        <NavMenu
          className="aaa"
          left={
            <>
              <NavMenu.Item type="default">
                <img src={iconPng} alt="logo" />
              </NavMenu.Item>
            </>
          }
          right={
            isOutSide ? (
              <>
                <span style={{ color: '#fff' }}>
                  {t('供应商名称：{{attr0}}', {
                    attr0: userInfo?.supplier_name,
                  })}
                </span>
                <NavMenu.Item
                  type="dropdown"
                  overlay={(close) => (
                    <RightDropDown close={close}></RightDropDown>
                  )}
                >
                  {lng === 'en' ? 'English' : t('中文')}
                </NavMenu.Item>
              </>
            ) : (
              <span style={{ color: '#fff' }}>
                {userInfo?.role + '：' + userInfo?.staff_name}
              </span>
            )
          }
        />
      </Header>
      <Body>
        <Sider>
          <Menu
            collapsable
            theme="light"
            title={t('国内短信管理后台')}
            icon={iconPng32}
          >
            <MenuList menu={menu} pathname={pathname}></MenuList>
          </Menu>
        </Sider>
        <Content>
          <Content.Header title={title} />
          <Content.Body full>
            <Card>
              <Card.Body>{children}</Card.Body>
            </Card>
          </Content.Body>
          <Content.Footer></Content.Footer>
        </Content>
      </Body>
    </Layout>
  );
}
