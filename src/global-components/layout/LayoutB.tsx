import React from 'react';
import { usePathname } from '@src/utils/react-use/usePathname';
import { Layout, Menu, NavMenu } from '@tencent/tea-component';
import { useCss } from 'react-use';
import { RightDropDown } from './layout-components/RightDropDown';
import { MenuList } from './layout-components/MenuList';
import iconPng from '../../assets/logo-bg-color.svg';
import iconPng32 from './img/32-1.png';
import { useMenuTitle } from './layout-use';
import { MenuOpt } from './layout-use';
import { t } from '@src/utils/i18n';
import { lng } from '@tea/app/i18n';
import { _useUserState } from '@src/global-state';
import { isOutSide } from '@src/const/envConst';

const { Header, Body, Sider, Content } = Layout;

// 给 子账号用的 layout
export function LayoutB(props: {
  menu?: MenuOpt;
  children?:
    | React.ReactNode
    | React.ReactFragment
    | React.ReactPortal
    | boolean
    | null
    | undefined;
}) {
  const { children, menu = [] } = props;
  const [userInfo] = _useUserState();
  const pathname = usePathname();

  const lauoutClass = useCss({
    height: '100vh',
  });
  const title = useMenuTitle(menu, pathname);
  return (
    <Layout className={lauoutClass}>
      <Header>
        <NavMenu
          left={
            <>
              <NavMenu.Item type="default">
                <img src={iconPng} alt="logo" />
              </NavMenu.Item>
            </>
          }
          right={
            isOutSide ? (
              <>
                <NavMenu.Item
                  type="dropdown"
                  overlay={(close) => (
                    <RightDropDown close={close}></RightDropDown>
                  )}
                >
                  {lng === 'en' ? 'English' : t('中文')}
                </NavMenu.Item>
              </>
            ) : (
              <span style={{ color: '#fff' }}>
                {userInfo?.role + '：' + userInfo?.staff_name}
              </span>
            )
          }
        />
      </Header>
      <Body>
        <Sider>
          <Menu
            collapsable
            theme="light"
            title={t('国内短信管理后台')}
            icon={iconPng32}
          >
            <MenuList menu={menu} pathname={pathname}></MenuList>
          </Menu>
        </Sider>
        <Content>
          <Content.Header title={title} />
          <Content.Body>{children}</Content.Body>
          <Content.Footer></Content.Footer>
        </Content>
      </Body>
    </Layout>
  );
}
