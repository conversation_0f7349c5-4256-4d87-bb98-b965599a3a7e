import { t } from '@src/utils/i18n';
import { List } from '@tencent/tea-component';
import Cookies from 'js-cookie';

export const LanguageChange = () => {
  return (
    <>
      <List.Item
        onClick={() => {
          localStorage.setItem('lang', 'zh');
          Cookies.set('lang', 'zh', { path: '/' });
          window.location.reload();
        }}
      >
        {t('中文')}
      </List.Item>
      <List.Item
        onClick={() => {
          localStorage.setItem('lang', 'en');
          Cookies.set('lang', 'en', { path: '/' });
          window.location.reload();
        }}
      >
        English
      </List.Item>
    </>
  );
};
