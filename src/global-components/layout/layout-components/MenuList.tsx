import { useHistory, useParams } from 'react-router-dom';
import { Menu } from '@tencent/tea-component';
import { MenuOpt } from '@src/global-components/layout/layout-use';

let getIsSelect = (opt: { pathname?: string }, pathname: string) => {
  return !!(opt.pathname && pathname.match(opt.pathname));
};

export const MenuList = (props: { menu: MenuOpt; pathname: string }) => {
  const history = useHistory();
  const { menu, pathname } = props;
  let params = useParams<Record<string, any>>();

  return (
    <>
      {menu.map((opt) => {
        const title = opt.menuTitleTemplate
          ? opt.menuTitleTemplate(opt.title, params, pathname, history)
          : opt.title;

        if (opt.children) {
          const defaultOpened = opt.children.some((opt) =>
            getIsSelect(opt, pathname),
          );
          return (
            <Menu.SubMenu
              defaultOpened={defaultOpened}
              key={`${title}_${opt.pathname}`}
              title={title}
              icon={opt.icon}
            >
              <MenuList menu={opt.children} pathname={pathname}></MenuList>
            </Menu.SubMenu>
          );
        } else {
          return (
            <Menu.Item
              key={`${opt.title}_${opt.pathname}`}
              title={title}
              icon={opt.icon}
              selected={getIsSelect(opt, pathname)}
              onClick={() => {
                history.push({ pathname: opt.pathname });
              }}
            />
          );
        }
      })}
    </>
  );
};
