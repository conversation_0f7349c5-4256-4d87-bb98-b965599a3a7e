import { List } from '@tencent/tea-component';
import { logout } from '@src/api/logout';
import { useHistory } from 'react-router-dom';
import { t } from '@src/utils/i18n';
import React from 'react';
import { useDialogRef } from '@src/utils/react-use/useDialog';
import { ChangePasswordDialog } from '@src/view/login-components/ChangePasswordDialog';
import { app } from '@src/utils/tea/app';
import { LanguageChange } from './LanguageChange';

export const RightDropDown = (props: { close: any }) => {
  const history = useHistory();
  const { close } = props;
  const changePasswordRef = useDialogRef();

  const handlerLogout = async (callBack: () => void) => {
    try {
      await logout();
    } catch (error: any) {
      console.log(error);
    }
    callBack();

    history.push({
      pathname: '/login',
    });
  };

  return (
    <>
      <List type="option">
        {/* <List.Item
        onClick={() => {
          handlerSubAccount(close);
        }}
      >
        子账号管理
      </List.Item> */}
        {/* <PermissionShowController need={['button::/user/main-account']}>
        <List.Item
          className="tea-nav__list-line"
          onClick={() => {
            handlerMainAccount(close);
          }}
        >
          {t('主账号密码修改')}
        </List.Item>
      </PermissionShowController> */}
        <LanguageChange />
        <List.Item
          onClick={() => {
            handlerLogout(close);
          }}
        >
          {t('退出登录')}
        </List.Item>
        <List.Item
          onClick={() => {
            changePasswordRef.current.open({
              onSuccess: () => {
                app.tips.success(t('密码修改成功，请重新登录'));
                history.push('/login');
              },
            });
          }}
        >
          {t('修改密码')}
        </List.Item>
      </List>
      <ChangePasswordDialog dialogRef={changePasswordRef} />
    </>
  );
};
