import { useHistory, useParams, useLocation } from 'react-router-dom';
import { useMemo } from 'react';
import { History } from 'history';

type MenuItem = {
  title: string;
  pageTitleTemplate?: (...args: any[]) => string;
  menuTitleTemplate?: (...args: any[]) => string;
  pathname?: string;
  need?: Array<string>;
  icon?: [string, string];
  children?: MenuOpt;
  handleBack?: (histor: History) => void;
};
export type MenuOpt = Array<MenuItem>;

export const titleHelper = (
  arr: MenuOpt,
  pathname: string,
): MenuItem | void => {
  for (let opt of arr) {
    if (!!(opt.pathname && pathname.match(opt.pathname))) return opt;
    if (opt.children) {
      let title = titleHelper(opt.children, pathname);
      if (title) return title;
    }
  }
  return;
};

export const useMenuTitle = (menu: MenuOpt, pathname: string) => {
  let params = useParams<Record<string, any>>();
  const history = useHistory();

  return useMemo(() => {
    const opt = titleHelper(menu, pathname);
    if (opt) {
      return opt.pageTitleTemplate
        ? opt.pageTitleTemplate(opt.title, params, pathname, history)
        : opt.title;
    } else {
      return '';
    }
  }, [history, menu, params, pathname]);
};

let bachHelper = (
  arr: MenuOpt,
  pathname: string,
): undefined | ((histor: History) => void) => {
  for (let opt of arr) {
    if (opt.pathname && opt.handleBack && pathname.match(opt.pathname)) {
      return opt.handleBack;
    }
    if (opt.children) {
      let handleBack = bachHelper(opt.children, pathname);
      if (handleBack) return handleBack;
    }
  }
  return;
};

export const useMenuBack = (menu: MenuOpt, pathname: string) => {
  const _history = useHistory();
  return useMemo(() => {
    return () => {
      const defaultHandleBack = (_history: History) => {
        _history.push({
          pathname: '/',
        });
      };
      const handleBacker = bachHelper(menu, pathname) || defaultHandleBack;
      handleBacker(_history);
    };
  }, [_history, menu, pathname]);
};
