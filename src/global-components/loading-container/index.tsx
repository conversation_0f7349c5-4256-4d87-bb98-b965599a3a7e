import React from 'react';
import style from './loading.module.less';
import { LoadingTip } from '@tencent/tea-component';
import classNames from 'classnames';

interface Props extends React.HTMLAttributes<HTMLElement> {
  loading?: boolean;
  fullWhite?: boolean;
  children:
    | React.ReactNode
    | React.ReactFragment
    | React.ReactPortal
    | boolean
    | null
    | undefined;
}
export const LoadingContainer = (props: Props) => {
  const { loading, className, fullWhite } = props;
  const _loading = !!loading;

  return (
    <div
      className={classNames([
        style.container,
        className,
        {
          [style.isLoading]: _loading,
          [style.fullWhite]: fullWhite,
        },
      ])}
    >
      <div className={style.modal}>
        <LoadingTip className={style.tip}></LoadingTip>
      </div>
      {props.children}
    </div>
  );
};
