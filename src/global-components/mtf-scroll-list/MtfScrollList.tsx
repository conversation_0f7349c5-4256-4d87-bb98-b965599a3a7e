import React, { useEffect, useLayoutEffect, useRef } from 'react';
import style from './MtfScrollList.module.less';
import classnames from 'classnames';
import { t } from '@src/utils/i18n';
import _ from 'lodash';
import { StatusTip } from '@tencent/tea-component';

interface Props<Record = any> extends React.HTMLAttributes<HTMLElement> {
  loading?: boolean;
  data: Record[];
  render: (arg: Record) => React.ReactNode;
  recordKey: Record extends {
    [key: string]: any;
  }
    ? keyof Record
    : string;

  onTop?: () => void;
  onPullDownEnd?: (arg: Record) => void;
}

export function MtfScrollList<T>(props: Props<T>) {
  const { data, recordKey, render, onTop, onPullDownEnd, className, loading } =
    props;
  const lastItemRef = useRef<any>(null);
  const ulRef = useRef<any>(null);

  const callBackRef = useRef((...args: any[]) => {});

  useEffect(() => {
    if (onPullDownEnd) {
      callBackRef.current = () => {
        const lastItem = data[data?.length - 1];
        if (lastItem) {
          onPullDownEnd(lastItem);
        }
      };
    }
  }, [data, onPullDownEnd]);
  useLayoutEffect(() => {
    var observer = new IntersectionObserver(
      (entries) => {
        // if (entries[0].intersectionRatio <= 0) return;
        console.log('Loaded new items');
        callBackRef.current?.();
      },
      {
        root: ulRef.current,
        rootMargin: '0px 0px 600px 0px',
        threshold: 1.0,
      },
    );

    const targetDom = lastItemRef.current;
    if (targetDom) {
      observer.observe(targetDom);
    }
    return () => {
      if (targetDom) observer.unobserve(targetDom);
    };
  }, []);

  return (
    <div ref={ulRef} className={classnames(style.list, className)}>
      {loading && (
        <div>
          <StatusTip status="loading" loadingText={t('加载中')} />
        </div>
      )}
      {data?.map((record, index) => {
        const key = record[recordKey.toString()] + '_' + index;
        return (
          <div className={style.item} data-key={key} key={key}>
            {render(record)}
          </div>
        );
      })}
      <div
        ref={lastItemRef}
        id="mtf-scroll-item"
        className={classnames(style.item, style.lastItem)}
      ></div>
    </div>
  );
}
