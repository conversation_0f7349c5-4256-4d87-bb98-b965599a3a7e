import { StatusTip } from '@tencent/tea-component';
import { useMemo } from 'react';

interface Props {
  record?: Array<any>;
  loading?: boolean;
}

export const useTableTopTip = (props: Props) => {
  // const [loading] = useGlobalLoadingCount();
  const { record, loading } = props;
  const isLoading = loading === undefined ? record === undefined : loading;

  const isEmpty = record && record.length === 0;

  const tableTopTip = useMemo(() => {
    if (isLoading) {
      return <StatusTip status="loading" />;
    }
    if (isEmpty) {
      return <StatusTip status="empty" />;
    }
    return false;
  }, [isLoading, isEmpty]);

  return tableTopTip;
};
