import { useEffect } from 'react';
import { _useFetchFailureReason } from '@src/global-state';
import _ from 'lodash';
import { useAsyncFn } from 'react-use';
import { getReportFailureReason as getReportFailureReasonV2 } from '@src/apiV2/sign';
import { getReportFailureReason } from '@src/api/sign';
import { isOutSide } from '@src/const/envConst';

const useFetchFailureReason = () => {
  const [list, setList] = _useFetchFailureReason();
  const [state, fetchList] = useAsyncFn(async (params?) => {
    const fn = isOutSide ? getReportFailureReason : getReportFailureReasonV2;
    const { data } = await fn({
      page_index: 1,
      page_size: 1000,
      ...params,
    });
    setList(data?.list ?? []);
    return data;
  }, []);

  useEffect(() => {
    if (list?.length !== 0) return;
    fetchList();
  }, [fetchList, list.length]);

  return {
    list,
    count: state?.value?.count ?? 0,
    loading: state?.loading,
    fetchList,
  } as const;
};
export default useFetchFailureReason;
