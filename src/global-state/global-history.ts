import { useEffect } from 'react';
import { useHistory } from 'react-router-dom';
import { History } from 'history';

interface HistoryMemo {
  history: History<any>;
}

export const historyMemo: HistoryMemo = {
  // @ts-ignore
  history: null,
};

export const useGlobalHistory = () => {
  const history = useHistory();

  useEffect(() => {
    historyMemo.history = history;
  }, [history]);

  return [history];
};
