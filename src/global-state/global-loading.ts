import { createGlobalState } from 'react-use';
import { useEffect } from 'react';

interface LoadingMemo {
  main: any;
  val: number;
  setGlobalLoading: (fn: (count: number) => number) => any;
}

export const loadingMemo: LoadingMemo = {
  main: null,
  val: 0,
  setGlobalLoading(fn) {
    const main = loadingMemo.main;
    const nextVal = fn(loadingMemo.val);
    if (loadingMemo.main) {
      main(nextVal);
    }
    loadingMemo.val = nextVal;
  },
};

const _useGlobalLoadingCount = createGlobalState<number>(0);

export const useGlobalLoadingCount = () => {
  const [globalLoadingCount, setGlobalLoading] = _useGlobalLoadingCount();
  useEffect(() => {
    loadingMemo.main = setGlobalLoading;
  }, [setGlobalLoading]);

  return [globalLoadingCount, setGlobalLoading] as const;
};
