import { isInside } from '@src/const/envConst';
import { createGlobalState } from 'react-use';

export interface UserInfo {
  user_id?: number;
  permission_list?: string[];
  staff_name?: string;
  user_name?: string;
  role?: string;
  supplier_id?: number;
  loginState: boolean | 'uncertain';
  supplier_name?: string;
}

export interface PriceSubStatus {
  status: number;
  sub_status: number;
  sub_status_name: string;
  sub_status_name_en: string;
  is_required: number;
  display: number;
}

export const _useUserState = createGlobalState<UserInfo>({
  permission_list: [],
  loginState: 'uncertain',
});

export const _useFetchFailureReason = createGlobalState<any>([]);
