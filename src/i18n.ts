// 导入依赖
import { i18n } from '@tea/app';
import 'moment/locale/zh-cn';

// 导入词条
import { translation } from '@i18n/translation';
import { translation as transitionEn } from '@i18n/translation/en';
import Cookies from 'js-cookie';

// 初始化国际化词条
const v = localStorage.getItem('lang');
Cookies.set('lang', v === 'en' ? 'en' : 'zh', { path: '/' });

if (v !== 'en') {
  i18n.init({ translation });
} else {
  i18n.init({ translation: transitionEn });
}
