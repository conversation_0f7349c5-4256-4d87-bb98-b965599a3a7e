import React from 'react';
import ReactDOM from 'react-dom';
import '@tencent/tea-component/dist/tea.css';

import './getLang';

import './index.css';
import './App.less';

import './i18n';

import { AppV1, AppV2 } from './App';
import reportWebVitals from './reportWebVitals';
import { isInside, isOutSide } from './const/envConst';

// If you want to start measuring performance in your app, pass a function
// to log results (for example: reportWebVitals(console.log))
// or send to an analytics endpoint. Learn more: https://bit.ly/CRA-vitals
// 数据上报
reportWebVitals((reportData: any) => {
  if (import.meta.env.MODE === 'development') {
    // console.log(reportData);
  }
});

const main = async () => {
  // 对外
  if (isOutSide) {
    ReactDOM.render(
      // <React.StrictMode>
      <AppV1 />,
      // </React.StrictMode>,
      document.getElementById('root'),
    );
  }
  // 对内
  if (isInside) {
    ReactDOM.render(
      // <React.StrictMode>
      <AppV2 />,
      // </React.StrictMode>,
      document.getElementById('root'),
    );
  }
};

main();
