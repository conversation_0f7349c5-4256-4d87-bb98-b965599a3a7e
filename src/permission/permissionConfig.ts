import permissionMd from './permission.md';

export const permissionTree = [
  ...permissionMd.matchAll(
    /<hr>\s<p>([\s\S]*?)<\/p>\s<table>[\s\S]*?<tbody>([\s\S]*?)<\/tbody><\/table>\s<hr>/gi,
  ),
].map((v, index) => {
  return {
    description: v[1],
    type: `group::/${index}`,
    name: `group::/${index}`,
    children: [
      ...v[2].matchAll(
        /<tr>\s<td>([\s\S]*?)<\/td>\s<td>([\s\S]*?)<\/td>\s<td>([\s\S]*?)<\/td>\s<\/tr>/gim,
      ),
    ].map((v) => {
      // [v[1], v[2], v[3]]
      return {
        description: v[1],
        name: `${v[3]}::${v[2]}`,
        value: v[2],
        type: v[3],
      };
    }),
  };
});

export const allPermission = permissionTree.map((v) => v.children).flat();

export const getPermissionsByNames = (permissionNames: Array<string>) => {
  return allPermission.filter((item) => {
    return permissionNames.includes(item.name);
  });
};
