import Aegis from 'aegis-web-sdk';

const isOutProduction = window.location.host === 'isms-purchase.qcloud.com';

const aegis = isOutProduction
  ? new Aegis({
      iid: 'sLtTbbLcnOhhgLORCE', // 项目ID，即上报id
      reportApiSpeed: true, // 接口测速
      reportAssetSpeed: true, // 静态资源测速
      spa: true, // spa 页面开启
      hostUrl: 'https://rumt-zh.com',
      api: {
        apiDetail: true,
        retCodeHandler: (
          data: string,
          url: string,
          xhr: Object,
        ): { isErr: boolean; code: string } => {
          const res = JSON.parse(data);
          return {
            isErr: res.code !== 0 || res.data.errors.length !== 0,
            code: res.code,
          };
        },
      },
    })
  : null;

export default aegis;
