import { Row, Col } from '@tencent/tea-component';
import _ from 'lodash';

export function getShowCols(boxes) {
  let res: string[] = [];
  try {
    res = _.reduce(
      boxes,
      (res: string[], value) => {
        if (value.show) {
          res.push(value.key);
        }
        return res;
      },
      [],
    );
    return res;
  } catch (error) {
    return res;
  }
}

export const renderExpand = function (boxes, record): React.ReactNode {
  return (
    <div style={{ margin: '10px 0' }}>
      <Row>
        {_.filter(boxes, (v) => !v.show).map((item) => {
          return (
            <Col span={8} key={item.key}>
              {item.header + '：'}
              {item?.render ? item?.render(record) : record[item.key]}
            </Col>
          );
        })}
      </Row>
    </div>
  );
};
