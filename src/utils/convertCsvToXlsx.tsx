import { t } from '@src/utils/i18n';
import * as XLSX from 'xlsx';
import ExcelJS from 'exceljs';
import { getReportFailureReason } from '@src/apiV2/sign';
import _ from 'lodash';
import dayjs from 'dayjs';

export function convertCsvToXlsx(file) {
  if (!file) {
    alert(t('请先选择CSV文件'));
    return;
  }

  const reader = new FileReader();
  reader.onload = function (e: any) {
    const csvData = e.target.result;
    csvToXlsxWithFilters(csvData, file.name.replace('.csv', '.xlsx'));
  };
  reader.readAsText(file);
}

export async function csvToXlsxWithFilters(csvData, filename) {
  const { data } = await getReportFailureReason({
    page_index: 1,
    page_size: 10000,
  });
  const result = _.groupBy(data?.list, 'parent_category');
  const group = _.map(result, (items, key) => ({
    label: key,
    value: key,
    children: _.map(items, (item) => ({
      label: item.son_category,
      value: `${item.id}`,
    })),
  }));
  console.log(group);
  const failureReasonText = group?.reduce((acc, cur) => {
    const text = cur.children.map((el) => `${el.value}.${el.label}`);
    return (acc += `${cur.label}：${text}\n`);
  }, '');

  const workbook = XLSX.read(csvData, { type: 'string' });

  const exceljsWorkbook = new ExcelJS.Workbook();
  const worksheet = exceljsWorkbook.addWorksheet('Sheet1');

  // 3. 转换数据
  const jsonData: any = XLSX.utils.sheet_to_json(
    workbook.Sheets[workbook.SheetNames[0]],
  );

  // 2. 在第二行写入标题行
  const headers = Object.keys(jsonData[0]);
  worksheet.addRow(headers); // 标题行在第2行

  // worksheet.addRow(Object.keys(jsonData[0])); // 标题行

  jsonData.forEach((row) => {
    worksheet.addRow(Object.values(row));
  });

  // 添加两列
  // worksheet.spliceColumns(3, 0, [t('备注')]);
  // worksheet.spliceColumns(3, 0, [t('报备原因')]);

  worksheet.insertRow(1, ['']);
  // 合并单元格，设置格式
  worksheet.mergeCells('A1:T1');
  const headerCell = worksheet.getCell('A1');
  headerCell.value = {
    richText: [
      {
        text: t(
          `请选择报备状态后上传，如报备状态为“报备失败、资料异常、供应商停用”，必须选择报备原因。报备原因需从下述列表中选择，填写原因对应的序号，多个原因用英文逗号分隔。如有特殊原因请填写在备注列\n`,
        ),
        font: { color: { argb: 'FFFF0000' }, bold: true },
      }, // 红色 + 加粗
      { text: failureReasonText, font: { color: { argb: 'FF000000' } } }, // 黑色
    ],
  };
  worksheet.getRow(1).height = 100;
  headerCell.alignment = {
    wrapText: true,
    vertical: 'top', // 垂直顶部对齐
  };

  // 在第二列(B列)添加报备状态下拉
  worksheet.getColumn(2).eachCell((cell, rowNumber) => {
    cell.dataValidation = {
      type: 'list',
      allowBlank: true,
      formulae: [t('"报备失败,资料异常,供应商停用"')],
      showErrorMessage: true,
      error: t('请选择报备状态'),
    };
  });

  // 浏览器环境：使用 writeBuffer 并创建下载链接
  const buffer = await exceljsWorkbook.xlsx.writeBuffer();

  // 创建Blob并下载
  const blob = new Blob([buffer], {
    type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
  });

  const url = URL.createObjectURL(blob);
  const a = document.createElement('a');
  a.href = url;
  a.download = t('报备列表-{{attr0}}.xlsx', {
    attr0: dayjs().format('YYYYMMDD'),
  });
  a.click();

  // 释放内存
  setTimeout(() => URL.revokeObjectURL(url), 100);
}
