export const getStatus = (
  meta: { active: any; touched: any; error: any } | any,
  validating: any,
) => {
  if (meta.active && validating) {
    return 'validating';
  }
  if (!meta.touched) {
    return undefined;
  }
  return meta.error ? 'error' : 'success';
};

export function getFormStatusAndMessage(
  meta: {
    active?: boolean;
    touched?: boolean;
    error?: string;
  },
  validating: boolean,
):
  | {
      status: 'validating' | 'error' | 'success';
      error?: string;
      message?: string;
    }
  | undefined {
  if (meta.active && validating) {
    return { status: 'validating' };
  }
  if (!meta.touched) {
    return undefined;
  }

  return {
    status: meta.error ? 'error' : 'success',
    error: meta.error,
    message: meta.error,
  };
}
