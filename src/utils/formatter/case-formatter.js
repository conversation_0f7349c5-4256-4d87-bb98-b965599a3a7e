/* eslint-disable @typescript-eslint/naming-convention */
import _ from 'lodash';

// 无论任何格式转化为 小驼峰
// https://stackoverflow.com/questions/12931828/convert-returned-json-object-properties-to-lower-first-camelcase
const _cloneWithCase = (o, caseTransFn, excludeKey = []) => {
  let newO;
  let origKey;
  let newKey;
  let value;

  if (o instanceof Array) {
    return o.map(function (value) {
      if (typeof value === 'object') {
        value = _cloneWithCase(value, caseTransFn);
      }
      return value;
    });
  } else {
    if (o === null || o === undefined) {
      return o;
    }
    newO = {};
    for (origKey in o) {
      if (o.hasOwnProperty(origKey)) {
        if (excludeKey.includes(origKey)) {
          newKey = origKey;
        } else {
          newKey = caseTransFn(origKey);
        }
        value = o[origKey];
        if (Array.isArray(value) || _.isObject(value)) {
          value = _cloneWithCase(value, caseTransFn);
        }
        newO[newKey] = value;
      }
    }
  }
  return newO;
};
const _cloneWithCaseShallow = (o, caseTransFn) => {
  let newO;
  let origKey;
  let newKey;
  let value;

  if (o instanceof Array) {
    return o.map(function (value) {
      if (typeof value === 'object') {
        value = _cloneWithCaseShallow(value, caseTransFn);
      }
      return value;
    });
  } else {
    newO = {};
    for (origKey in o) {
      if (o.hasOwnProperty(origKey)) {
        newKey = caseTransFn(origKey);
        value = o[origKey];
        newO[newKey] = value;
      }
    }
  }
  return newO;
};

export const cloneWithCamelCase = (o, excludeKey = []) => {
  const result = _cloneWithCase(o, _.camelCase, excludeKey);
  // console.log('camelCase result: ', result)
  return result;
};

export const cloneWithLowerFirst = (o, excludeKey = []) => {
  const result = _cloneWithCase(o, _.lowerFirst, excludeKey);
  // console.log('camelCase result: ', result)
  return result;
};

export const cloneWithCamelCaseShallow = (o) => {
  const result = _cloneWithCaseShallow(o, _.camelCase);
  return result;
};

/**
 * @param o 要复制的对象
 * @param excludeKey 要排除的 key。 所有的此 key 都会被剔除。深度的 key 也一样会剔除。
 */
export const cloneWithUpperFirst = (o, excludeKey = []) => {
  const result = _cloneWithCase(o, _.upperFirst, excludeKey);
  // console.log(o, 'UpperCase result: ', result)
  return result;
};

export const formatPage = ([pageSize, pageIndex]) => {
  const offset = _.toInteger((pageIndex - 1) * pageSize);
  const limit = _.toInteger(pageSize);
  const pageQuery = offset >= 0 && limit >= 1 ? { offset, limit } : {};
  return pageQuery;
};
