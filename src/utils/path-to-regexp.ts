import { compile } from 'path-to-regexp';
import queryString from 'query-string';

export const regOptToPath = ({
  pathname,
  params,
  query,
}: {
  pathname: string;
  params?: Record<string, any>;
  query?: Record<string, any>;
}) => {
  const _toPath = compile(pathname, { encode: encodeURIComponent });
  const qsString = query ? `?${queryString.stringify(query)}` : '';
  return _toPath(params) + qsString;
};
