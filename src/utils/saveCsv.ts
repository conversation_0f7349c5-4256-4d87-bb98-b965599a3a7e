import { message } from '@tencent/tea-component';
import { dumpRecordWarning } from '../apiV2/dumpRecordWarning';
import { qsStringify } from './react-use/qsStringify';
import { t } from '@src/utils/i18n';
import { titleHelper } from '@src/global-components/layout/layout-use';
import { menuV2 } from '../app-configV2';
import _ from 'lodash';
import { isOutSide } from '@src/const/envConst';

function isMSbrowser() {
  const userAgent = window.navigator.userAgent;
  return (
    userAgent.indexOf('Edge') !== -1 || userAgent.indexOf('Trident') !== -1
  );
}

function format(data) {
  return String(data)
    .replace(/"/g, '""')
    .replace(/(^[\s\S]*$)/, '"$1"');
}

const getMenuTitle = () => {
  const opt = titleHelper(menuV2, window.location.pathname);
  if (opt) {
    return opt.pageTitleTemplate
      ? opt.pageTitleTemplate(opt.title, {}, window.location.pathname)
      : opt.title;
  } else {
    return '';
  }
};

async function dumpReport({ route, formValues }) {
  console.log(formValues);
  if (!route || isOutSide) return;
  // 导出告警
  const params = _.pickBy(formValues, (v: any) => v !== '' && !_.isNil(v));
  console.log(params, _.isEmpty(params));
  const route_name = getMenuTitle();
  const res = await dumpRecordWarning({
    route_name,
    route,
    param: _.isEmpty(params) ? {} : _.omit(params, ['page_index', 'page_size']),
  });
  if (res.code !== 0) {
    message.error(res.msg);
  }
}

export function saveCSV(
  title,
  head,
  data,
  { route = '', formValues = {} } = {},
): Promise<undefined> {
  let wordSeparator = ',';
  let lineSeparator = '\n';

  let reTitle = title + '.csv';
  let headBOM = '\ufeff';
  let headStr = head
    ? head.map((item) => format(item)).join(wordSeparator) + lineSeparator
    : '';
  let dataStr = data
    ? data
        .map((row) => row.map((item) => format(item)).join(wordSeparator))
        .join(lineSeparator)
    : '';
  let blob = new Blob([headBOM + headStr + dataStr], {
    type: 'text/plain;charset=utf-8',
  });

  return isMSbrowser()
    ? new Promise((resolve) => {
        // Edge、IE11
        (window.navigator as any).msSaveBlob(blob, reTitle);
        resolve(undefined);
      })
    : new Promise(async (resolve) => {
        // Chrome、Firefox

        let a = document.createElement('a');
        a.href = window.URL.createObjectURL(blob);
        a.download = reTitle;
        a.click();
        resolve(undefined);
        dumpReport({ route, formValues });
      });
}
