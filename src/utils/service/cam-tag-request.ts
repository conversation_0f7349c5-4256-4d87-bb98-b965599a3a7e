import _ from 'lodash';
import { app } from '../tea/app';
import { cloudApiMaps, CloudApiKey } from './cloud-api-name-maps';
import { Report } from './tdw-report';
import { cloudApiTips, CloudApiTipsType } from './cloud-api-error-tip-maps';

// 标签的请求
export const camTagRequest = async (name: CloudApiKey, params: any) => {
  const apiName = cloudApiMaps[name];

  try {
    const data = await app.capi.requestV3(
      {
        regionId: 1,
        serviceType: 'tag',
        cmd: apiName,
        data: {
          Version: '2018-08-13',
          ServiceType: 'im',
          ResourceRegion: '',
          ResourcePrefix: 'sdkappid',
          Action: apiName,
          ...params,
        },
      },
      {
        tipErr: false,
      },
    );

    // Report({
    //   tdw: {
    //     op_name: "apimonitor",
    //     op_type: "allapi",
    //     info: data.data.Response.RequestId,
    //     custom1: apiName
    //   }
    // });

    return _.get(data, 'data.Response');
  } catch (error: any) {
    const errorCode: CloudApiTipsType = _.get(
      error,
      'response.data.Response.Error.Code',
    );
    const errorMsg: keyof typeof cloudApiTips.InternalError = _.get(
      error,
      'response.data.Response.Error.Message',
    );
    let tips = _.get(cloudApiTips[errorCode], errorMsg);

    if (errorCode === 'AuthFailure.UnauthorizedOperation') {
      const cam = await app.sdk.use('cam-sdk');
      cam.showBreakModal({ message: errorMsg });
    } else if (errorCode === 'InternalError') {
      // @ts-ignore
      console.log(tips.errorMsg);
    }

    // Report({
    //   tdw: {
    //     op_name: "tips",
    //     op_type: "error",
    //     info: _.get(error, "data.data.Response.RequestId"),
    //     custom1: tips,
    //     custom2: apiName
    //   }
    // });

    throw error;
  }
};
