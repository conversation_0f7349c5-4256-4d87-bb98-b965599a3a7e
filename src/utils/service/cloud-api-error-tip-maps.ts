// 部分中文字，错误提示翻译
import { t } from '@src/utils/i18n';

export const errorTips = {};

export const cloudApiTips = {
  'AuthFailure.UnRealNameAuthenticated': {
    errorMsg: t('未完成实名认证，请完成使命认证后重试'),
  },
  'InternalError.AccessIMProxyError': {
    errorMsg: t('群组代理转发未响应，请稍候重试'),
  },
  'InternalError.ResourceCanNotRefund': {
    errorMsg: t('未查询到可退费资源'),
  },
  'InternalError.RequestTradeCheckRefundFailed': {
    errorMsg: t('请求计费检测退费情况失败'),
  },
  'InternalError.CheckRefundFailedByTrade': {
    errorMsg: t('资源不能退费'),
  },
  'InternalError.AccessOIDBPorxyError': {
    errorMsg: t('连接未响应，请稍候重试'),
  },
  'InternalError.BackendReturnErr': {
    errorMsg: t('系统暂时未响应，正在重试，请等待'),
  },
  'InternalError.BackendTimeOut': {
    errorMsg: t('系统暂时未响应，正在重试，请等待'),
  },
  'InternalError.CallbackUrlIllegal': {
    errorMsg: t('回调地址非法，请修改后重试'),
  },
  'InternalError.CantResolveCallbackUrlHostIp': {
    errorMsg: t('回调地址解析未响应，请修改后重试'),
  },
  'InternalError.CreateSecretUsersig': {
    errorMsg: t('密钥格式错误，请修改后重试'),
  },
  'InternalError.DBError': {
    errorMsg: t('系统暂时未响应，正在重试，请等待'),
  },
  'InternalError.ErrorPrivateKey': {
    errorMsg: t('私钥格式错误，请修改后重试'),
  },
  'InternalError.ErrorSecret': {
    errorMsg: t('密钥格式错误，请修改后重试'),
  },
  'InternalError.ErrorSecretFlag': {
    errorMsg: t('密钥格式错误，请修改后重试'),
  },
  'InternalError.ErrorSkey': {
    errorMsg: t('鉴权未响应，请稍候重试'),
  },
  'InternalError.GenSigError': {
    errorMsg: t('获取用户标识未响应，请稍候重试'),
  },
  'InternalError.ErrorAccountID': {
    errorMsg: t('系统暂时未响应，正在重试，请等待'),
  },
  'InternalError.IMConfigOverLimit': {
    errorMsg: t('变配操作每个自然月仅支持配置一次，请下个自然月再次尝试'),
  },
  'InternalError.IMTransPostpayCanNotModifyTwiceInOneMonth': {
    errorMsg: t('变配操作每个自然月仅支持配置一次，请下个自然月再次尝试'),
  },
  'InternalError.IMTransPostpayDoNotInWhiteList': {
    errorMsg: t('应用不在白名单，请联系客服处理'),
  },
  'InternalError.IMTransPostpayErrorCreateTime': {
    errorMsg: t('应用状态非老版本，不能转成后付费，请联系客服处理'),
  },
  'InternalError.IMTransPostpaySelectFeatureYouWant': {
    errorMsg: t('功能包参数格式错误，请修改后重试'),
  },
  'InternalError.IMTransPrepayAlreadyExistPrepay': {
    errorMsg: t('体验版状态重复，请修改后重试'),
  },
  'InternalError.IMTransPrepayErrorCreateTime': {
    errorMsg: t('应用状态为体验版，不能转成后付费，请联系客服处理'),
  },
  'InternalError.NewUserStepNotFound': {
    errorMsg: t('该引导步骤异常，请联系客服处理'),
  },
  'InternalError.SdkAppIdOverLimit': {
    errorMsg: t('应用数量超出限制，请删除应用后重试'),
  },
  'InternalError.SecretCanNotDegrade': {
    errorMsg: t('升级未响应，请稍候重试'),
  },
  'InternalError.SecretNotExist': {
    errorMsg: t('密钥状态异常，请稍候重试'),
  },
  'InternalError.VerifySecretUsersig': {
    errorMsg: t('密钥校验未响应，请稍候重试'),
  },
  'InternalError.VerifySigBaseDecodeError': {
    errorMsg: t('解码未响应，请稍候重试'),
  },
  'InternalError.VerifySigError': {
    errorMsg: t('用户标识校验失败，请稍候重试'),
  },
  InvalidParameter: {
    errorMsg: t('参数不在有效范围，请检查后重试'),
  },
  'InvalidParameter.ErrorAccountID': {
    errorMsg: t('账号格式异常，请修改后重试'),
  },
  'InvalidParameter.SdkAppId': {
    errorMsg: t('应用ID不匹配，请稍候再试'),
  },
  MissingParameter: {
    errorMsg: t('必要参数缺失，请修改后重试'),
  },
  'InternalError.FailedInAuthenticateMutually': {
    errorMsg: t('证书下载未响应，请稍候重试'),
  },
  'AuthFailure.UnauthorizedOperation': {
    errorMsg: t('无权限，请联系主账号进行授权'),
  },
  UnauthorizedOperation: {
    errorMsg: t('无权限，请联系主账号进行授权'),
  },
  InternalError: {
    '| add administrator failed. err_code:70398Exceeded accunt number limit. To create more than 100 accunts, please upgrade to pro-version.':
      {
        errorMsg: t('体验版账号数量限制，请先升级后再创建'),
      },
    'CallbackUrlIllegal ': {
      errorMsg: t('回调地址为错误，请修改后再试'),
    },
    'An internal error has occurred. Retry your request, but if the problem persists, contact us.':
      {
        errorMsg: t('服务未响应，请稍候重试'),
      },
    add_tag_name_exist: {
      errorMsg: t('添加的字段重复，请修改后重试'),
    },
    'already exist,no need add': {
      errorMsg: t('添加的字段重复，请修改后重试'),
    },
    'require Name': {
      errorMsg: t('群名称为空或重复，请检查后重试'),
    },
    'invalid owner id': {
      errorMsg: t('群主未注册或被删除，请检查后重试'),
    },
    'group name is too long': {
      errorMsg: t('群名称太长，请检查后重试'),
    },
    'group name can not be empty': {
      errorMsg: t('群名称不能为空，请检查后重试'),
    },
    'all identifiers you want to add are invalid': {
      errorMsg: t('该用户ID未注册或已被删除，请检查后重试'),
    },
    'this group does not exist': {
      errorMsg: t('群组未注册或已被删除，请检查后重试'),
    },
    'Service error. Please retry later.': {
      errorMsg: t('系统暂时未响应，正在重试，请等待'),
    },
    'sdkappid is blocked by prepaid blacklist': {
      errorMsg: t('已欠费停服，请充值后重试'),
    },
    'can not delete owner from group': {
      errorMsg: t('群主不可删除，请修改后重试'),
    },
    'syatem error': {
      errorMsg: t('系统暂时未响应，正在重试，请等待'),
    },
    "Fail to create AVchatroom. You've reached AVchatroom amount limit. Please upgrade your package on the console":
      {
        errorMsg: t('聊天室已达上限，请创建其他类型的群组'),
      },
    'require group name': {
      errorMsg: t('群名称为必填，请修改后重试'),
    },
    'prepay:need to open this service': {
      errorMsg: t('需开通安全打击-高级版，如已开通，请五分钟后重试'),
    },
    'this group disable kick member': {
      errorMsg: t('该群组踢人限制，请重新选择后重试'),
    },
    'GroupDefinedField and GroupMemberDefinedField can not use same name': {
      errorMsg: t('群自定义字段与群成员自定义字段不可重名，请修改后重试'),
    },
    'member list is empty': {
      errorMsg: t('列表为空，请重试'),
    },
    'this group disable direct invite member': {
      errorMsg: t('该群组邀请成员限制，请重新选择后重试'),
    },
    'the number of the dirty words exceeds the limit': {
      errorMsg: t('所添加的不雅词词数量超过限制，请删除后重试'),
    },
    'introduction is too long': {
      errorMsg: t('介绍太长，请修改后重试'),
    },
    'invalid group id': {
      errorMsg: t('群ID有误，请修改后重试'),
    },
    'system error': {
      errorMsg: t('服务未响应，请稍候重试'),
    },
    'service timeout or request format error,please check and try again': {
      errorMsg: t('服务未响应或格式错误，请稍候重试'),
    },
  },
};

export type CloudApiTipsType = keyof typeof cloudApiTips;
