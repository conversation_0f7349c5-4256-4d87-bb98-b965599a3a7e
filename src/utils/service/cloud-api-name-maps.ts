// 云 api 对应名称

interface CustomType {
  [key: string]: string;
}
export const cloudApiMaps: CustomType = {
  Login: 'post::/admin/supplier-user/login',
  GetUserInfo: '/admin/supplier-user/info',
  Logout: 'post::/admin/supplier-user/logout',
  ChangePassword: 'post::/admin/supplier-user/change-password',
  GetLoginIp: '/admin/supplier-user/get-ip',
  GetVerifyVCode: 'post::/admin/supplier-user/send-verifycode',
  GetCosKey: '/purchase/cos/get-temp-key',
  GetSignReportList: 'get::/sign/report/get-list',
  ChangeSignReportStatus: 'post::/sign/report/change-status',
  ExportSignReport: 'get::/sign/report/export',
  GetReportFailureReason: 'get::/sign/report-failure-reason/get-list',
  SendVerifyCode: '/utils/verify/send-code',
  GetAsyncTaskList: '/utils/async-process/get-task-list',
  AddAsyncTask: 'post::/utils/async-process/add',
  GetAsyncTaskInfo: 'get::/utils/async-process/get-task-info',
  GetSignReportIdList: 'get::/sign/report/get-id-list',
  GetExportProgessList: 'get::/sign/report/get-export-progress-list',
  GetExportProgessInfo: 'get::/sign/report/get-export-progress-info',
};

export const cloudApiMapsV2: CustomType = {
  LoginCheck: '/admin/user/get-config-list',
  GetPurchaseSupplier: '/admin/supplier/get-list',
  AddPurchaseSupplier: 'post::/admin/supplier/add',
  AddSupplierAccount: 'post::/admin/supplier-user/add',
  DeletePurchaseSupplier: 'post::/admin/supplier/delete',
  EditPurchaseSupplier: 'post::/admin/supplier/edit',
  GetSupplierLoginAccount: '/admin/supplier-user/get-list',
  EditSupplierLoginAccount: 'post::/admin/supplier-user/edit',
  ResetLoginPassword: 'post::/admin/supplier-user/change-user-password',
  GetCosKey: 'get::/utils/cos/get-temp-key',
  GetUserConfig: '/admin/user/get-user-list',
  AddUserConfig: 'post::/admin/user/add',
  EditUserConfig: 'post::/admin/user/edit',
  DeleteUserConfig: 'post::/admin/user/delete',
  RefreshResourcePool: 'post::/sales-price/inquiry/refresh',
  GetIPBlock: '/admin/ip-blacklist/get-list',
  DeleteIPBlock: 'post::/admin/ip-blacklist/delete',
  AddIPBlock: 'post::/admin/ip-blacklist/add',
  AddSignApply: 'post::/sign/apply/add',
  EditSignApply: 'post::/sign/apply/edit',
  DeleteSignApply: 'post::/sign/apply/delete',
  GetSignApplyList: 'get::/sign/apply/get-list',
  ChangeSignApplyStatus: 'post::/sign/apply/change-status',
  SubmitSignApply: 'post::/sign/report/add',
  GetSignReportList: 'get::/sign/report/get-list',
  ChangeSignReportStatus: 'post::/sign/report/change-status',
  GetNeedReportProvider: 'get::/sign/report/get-need-report-provider',
  ChangeReportShowKeys: 'post::/sign/report/edit-show-keys',
  GetProviderList: '/sign/report/get-provider-list',
  GetSubCode: '/sign/report/get-sub-code',
  ChangeSignAdditionalReportStatus:
    'post::/sign/additional-report/change-status',
  GetSignAdditionalReportNeedReport:
    'get::/sign/additional-report/get-need-report-sign',
  AddSignAdditionalReportByProvider:
    'post::/sign/additional-report/add-by-provider',
  AddSignAdditionalReportByCustomer:
    'post::/sign/additional-report/add-by-customer',
  GetSignAdditionalReport: 'get::/sign/additional-report/get-list',
  DeleteSignAdditionalReport: 'post::/sign/additional-report/delete',
  GetReportFailureReason: 'get::/sign/report-failure-reason/get-list',
  AddReportFailureReason: 'post::/sign/report-failure-reason/add',
  EditReportFailureReason: 'post::/sign/report-failure-reason/edit',
  DeleteReportFailureReason: 'post::/sign/report-failure-reason/delete',
  SendReportEmail: 'post::/sign/report/send-email',
};

export type CloudApiKey = keyof typeof cloudApiMaps;
