import { SignApplyForm<PERSON>ield } from '@src/view/sign-report/type';

interface Login {
  password: string;
  email: string;
  verifycode: string;
}

interface ReqOption {
  base?: any;
  needSkey?: any;
  needTinyId?: any;
  CamTag?: any;
  Language?: any;
  skey?: any;
  tinyId?: any;
}

/**
 * Action: "RegisterIMApp"
 * BizScheme: 0
 * BizType: 1
 * Description: ""
 * International: 0
 * Name: "test"
 * Region: "ap-guangzhou"
 * Skey: "P0beYACxudh2yTKZIdYz3YnrdHZ-uWjrga972-96STQ_"
 * TinyId: "144115212653182233"
 * Version: "2019-03-18"
 */

// 登录检查接口
// eslint-disable-next-line @typescript-eslint/no-empty-interface
interface LoginCheck {}
// eslint-disable-next-line @typescript-eslint/no-empty-interface
interface GetLoginIp {}
// eslint-disable-next-line @typescript-eslint/no-empty-interface
interface GetUserInfo {}

interface ChangePassword {
  password_old: string;
  password: string;
}

interface DescribeAppAndAccountInfo {
  sdkAppId: number;
}

interface GetCosKey {
  file_name: string;
  bucket: string;
}

interface GetPurchaseSupplier {
  supplier_id?: number;
  name?: string;
  page_index?: number;
  page_size?: number;
}

interface DeletePurchaseSupplier {
  supplier_id: number;
}

interface AddPurchaseSupplier {
  name?: string;
  alias?: string;
  alias_pinyin?: string;
  report_email?: string;
  email_file_secret?: string;
}
interface EditPurchaseSupplier {
  supplier_id?: number;
  name?: string;
  alias?: string;
  alias_pinyin?: string;
  report_email?: string;
  email_file_secret?: string;
}

interface AddSupplierAccount {
  supplier_id: number;
  name: string;
  password: string;
}

interface GetSupplierLoginAccount {
  supplier_id: number;
  page_index?: number;
  page_size?: number;
}

interface EditSupplierLoginAccount {
  user_id: number;
  ips: string[];
  deleted: 1 | 0;
}

export type GetOperatorList = {
  country_code?: string;
  mcc?: string;
  mnc?: string;
  search_key?: string;
  page_index?: number;
  page_size?: number;
};

interface AddUserConfig {
  staff_name: string;
  role: string;
}

interface EditUserConfig {
  user_id: string;
  role: string;
  status: number;
}

interface DeleteUserConfig {
  user_id: number;
}

interface GetUserConfig {
  user_id?: string;
  staff_name?: string;
  role?: string;
  status?: number;
  page_index?: number;
  page_size?: number;
}

interface GetVerifyVCode {
  email: string;
  password: string;
}
interface ResetLoginPassword {
  user_id: number;
  password: string;
}
interface RefreshResourcePool {
  price_id: number;
}

interface DumpRecordWarning {
  route_name: string;
  param: any;
  route: string;
}
interface GetIPBlock {
  ip?: string;
}
interface DeleteIPBlock {
  ip: string;
}

interface AddIPBlock {
  ip: string;
}

interface AddSignApply {
  params: SignApplyFormField[];
}

interface EditSignApply extends SignApplyFormField {
  apply_id: number;
}

interface DeleteSignApply {
  apply_id: number;
  status?: number;
  msg?: string;
}

interface GetSignApplyList {
  apply_ids?: number[];
  uin?: number;
  qappid?: number;
  sign?: string;
  signs?: string[];
  provider_ids?: number[];
  provider_name?: string;
  sign_id?: number;
  remark_type?: string;
  company_name?: string;
  unisc_id?: string;
  corp_name?: string;
  transactor_name?: string;
  transactor_cr_num?: string;
  status?: number;
  source?: 0 | 1;
  page_size?: number;
  page_index?: number;
}

interface ChangeSignApplyStatus {
  apply_id: number;
  status: number;
  msg?: string;
  failure_reason_ids?: string;
  missing_keys?: string;
  wrong_keys?: string;
}

interface SubmitSignApply {
  params: {
    apply_id: number;
    provider_info_arr: {
      provider_id: number | string;
      supplier_id: number | string;
      account: string;
      sub_code: string;
      show_keys: string;
    }[];
    sign: string;
    remark?: string;
  }[];
}

interface GetSignReportList {
  uin?: number;
  qappid?: number;
  report_id?: number;
  apply_id?: number;
  provide_ids?: string;
  sign?: string;
  signs?: string[];
  account?: string;
  sub_code?: string;
  remark_type?: string;
  company_name?: string;
  unisc_id?: string;
  corp_name?: string;
  transactor_name?: string;
  transactor_cr_num?: string;
  source?: 0 | 1;
  status?: number;
  from_time?: string;
  to_time?: string;
  page_size?: number;
  page_index?: number;
}

interface ChangeSignReportStatus {
  params: {
    report_id: number;
    status: number | undefined;
    msg?: string;
    failure_reason_ids?: string; // 报备失败/补充资料必传
    missing_keys?: string; // 资料异常必传
    wrong_keys?: string; // 资料异常必传
  }[];
}

interface GetNeedReportProvider {
  apply_id_arr: number[];
}

interface ChangeReportShowKeys {
  report_ids: string;
  show_keys: string[];
}

interface GetProviderList {
  provider_ids?: number[];
  provider_name?: string;
  sms_type?: string;
  page_index?: number;
  page_size?: number;
}

interface GetSubCode {
  qappid: number;
  signs: string[];
  provider_ids: number[];
}

interface ChangeSignAdditionalReportStatus {
  params: { additional_id: number; status: number; msg?: string }[];
}

interface GetSignAdditionalReportNeedReport {
  additional_ids?: string[];
  additional_type?: number;
}

interface AddSignAdditionalReportByProvider {
  params: Array<{
    provider_id: number;
    peer_provider_id: number;
    sms_type: string;
    remark: string;
  }>;
}

interface AddSignAdditionalReportByCustomer {
  params: Array<{
    uin: number;
    qappid: number;
    remark: string;
  }>;
}

interface GetSignAdditionalReport {
  additional_id?: number;
  provider_id?: number;
  peer_provider_id?: number;
  sms_type?: string;
  uin?: number;
  qappid?: number;
  sign?: string;
  status?: number;
  type?: number;
  page_index?: number;
  page_size?: number;
}

interface DeleteSignAdditionalReport {
  additional_id: string;
  msg?: string;
}

interface AddReportFailureReason {
  params: {
    parent_category: string;
    son_category: string;
    report_status: number;
    solution: string;
    wrong_keys: string;
  }[];
}

interface EditReportFailureReason {
  id: number;
  parent_category: string;
  son_category: string;
  report_status: number;
  solution: string;
  wrong_keys: string;
}

interface DeleteReportFailureReason {
  ids: number[];
}

interface GetReportFailureReason {
  id?: number;
  parent_category?: string;
  son_category?: string;
  page_index?: number;
  page_size?: number;
  report_status?: number;
}

interface SendVerifyCode {
  route: string;
}

interface SendReportEmail {
  report_ids: number[];
}

interface ExportSignReport extends GetSignReportList {
  verify_code?: string;
  report_ids?: number[];
  need_attachment?: number;
}

interface GetAsyncTaskInfo {
  task_id: number | string;
  command: string;
}

interface GetAsyncTaskList {
  command: string;
}

interface AddAsyncTask {
  command: string;
  params: any[];
}

interface GetExportProgessList {
  task_id?: number | string;
}

interface GetExportProgessInfo {
  progress_key: string;
}

export type _ApiV3ReqType = {
  Login: Login;
  GetUserInfo: GetUserInfo;
  LoginCheck: LoginCheck;
  GetLoginIp: GetLoginIp;
  Logout: void;
  ChangePassword: ChangePassword;
  DescribeAppAndAccountInfo: DescribeAppAndAccountInfo;

  GetPurchaseSupplier: GetPurchaseSupplier;
  DeletePurchaseSupplier: DeletePurchaseSupplier;
  AddPurchaseSupplier: AddPurchaseSupplier;
  EditPurchaseSupplier: EditPurchaseSupplier;
  AddSupplierAccount: AddSupplierAccount;
  GetSupplierLoginAccount: GetSupplierLoginAccount;
  EditSupplierLoginAccount: EditSupplierLoginAccount;

  GetCosKey: GetCosKey;
  AddUserConfig: AddUserConfig;
  EditUserConfig: EditUserConfig;
  DeleteUserConfig: DeleteUserConfig;
  GetUserConfig: GetUserConfig;

  GetVerifyVCode: GetVerifyVCode;
  ResetLoginPassword: ResetLoginPassword;
  RefreshResourcePool: RefreshResourcePool;

  DumpRecordWarning: DumpRecordWarning;
  AddIPBlock: AddIPBlock;
  DeleteIPBlock: DeleteIPBlock;
  GetIPBlock: GetIPBlock;

  AddSignApply: AddSignApply;
  EditSignApply: EditSignApply;
  DeleteSignApply: DeleteSignApply;
  GetSignApplyList: GetSignApplyList;
  ChangeSignApplyStatus: ChangeSignApplyStatus;
  SubmitSignApply: SubmitSignApply;
  GetSignReportList: GetSignReportList;
  ChangeSignReportStatus: ChangeSignReportStatus;
  GetNeedReportProvider: GetNeedReportProvider;
  ChangeReportShowKeys: ChangeReportShowKeys;
  GetProviderList: GetProviderList;
  GetSubCode: GetSubCode;
  ChangeSignAdditionalReportStatus: ChangeSignAdditionalReportStatus;
  GetSignAdditionalReportNeedReport: GetSignAdditionalReportNeedReport;
  AddSignAdditionalReportByProvider: AddSignAdditionalReportByProvider;
  AddSignAdditionalReportByCustomer: AddSignAdditionalReportByCustomer;
  GetSignAdditionalReport: GetSignAdditionalReport;
  DeleteSignAdditionalReport: DeleteSignAdditionalReport;
  ExportSignReport: ExportSignReport;
  AddReportFailureReason: AddReportFailureReason;
  EditReportFailureReason: EditReportFailureReason;
  DeleteReportFailureReason: DeleteReportFailureReason;
  GetReportFailureReason: GetReportFailureReason;
  SendVerifyCode: SendVerifyCode;
  SendReportEmail: SendReportEmail;
  GetAsyncTaskInfo: GetAsyncTaskInfo;
  GetAsyncTaskList: GetAsyncTaskList;
  AddAsyncTask: AddAsyncTask;
  GetSignReportIdList: GetSignReportList;
  GetExportProgessInfo: GetExportProgessInfo;
  GetExportProgessList: GetExportProgessList;
};

export type ApiV3ReqType = {
  [k in keyof _ApiV3ReqType]: _ApiV3ReqType[k] & ReqOption;
};
export type ApiV3ReqKey = keyof ApiV3ReqType;
