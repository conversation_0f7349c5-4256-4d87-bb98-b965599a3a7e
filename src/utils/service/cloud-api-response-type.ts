import { SignApply<PERSON>orm<PERSON>ield } from '@src/view/sign-report/type';

/* eslint-disable @typescript-eslint/no-empty-interface */
interface ErrorRes {
  actionStatus?: string;
  errorCode?: number;
  errorInfo?: string;
  msg?: any;
  code?: number;
  data?: any;
}

interface LogicalErrorRes {
  data?: {
    errors?: any[];
  };
}

interface User {
  tinyId: number;
  nickname: string;
  account: string;
}
interface Login {
  data: {
    supplier_id: number;
    supplier_name: string;
    user_name: string;
    user_id: number;
  };
}

interface ChangePassword {}
interface DescribeUploadSign {
  sign: string;
}
interface LoginCheck {
  data: {
    user_id: number;
    user_name: string;
    supplier_id: number;
    supplier_name: string;
  };
}

interface GetUserInfo {
  data: {
    user_id: number;
    user_name: string;
    supplier_name: string;
    supplier_abbr_name: string;
    supplier_id: number;
    email: string;
  };
}

interface GetLoginIp {
  data: {
    ip: string;
  };
}

export interface DescribeAppAndAccountInfo {
  actionStatus: string;
  // android 的证书
  andIdentifiers: {
    id: number;
    certUrl: string;
    channelId: string;
    extField: string;
    identifierId: string;
    identifierKey: string;
    intent: string;
    notifyMode: number;
    packageName: string;
    platform: number;
  }[];
  // ios 的证书
  identifiers: {
    id: number;
    identifierKey: string;
    identifierType: number;
    identifierUrl: string;
  }[];
  errorInfo: string;
  errorCode: number;
  appStatus: number;
  bizId: number;
  bizName: string;
  bizScheme: number;
  bizType: number;
  flagAppUsePush: number;
  hasAndroid: number;
  hasIos: number;
  hasMac: number;
  hasWeb: number;
  hasWindows: number;
  registerFrom: number;
  updateTime: string;
  createTime: string;
  accountInfos: [
    {
      accountId: number;
      accountMode: number;
      accountSystem: number;
      accountType: Array<{
        appid3rd: string;
        type: number;
      }>;
      administrators: Array<{
        adminFormat: number;
        adminName: string;
      }>;
      isUploadkey: number;
    },
  ];
  status: number;
  description: string;
}

export type GetPurchaseAccountData = {
  account_id: number;
  account_name: string;
  supplier_id: number;
  supplier_name: string;
  smpp_account: string;
  smpp_main_ip: string;
  smpp_spare_ip: string;
  smpp_domain: string;
  tencent_node: string;
  tps: number;
  max_conn_num: number;
  channel_support: number;
  sms_type: string;
  route_type: number;
  version: string;
  status: number;
  remark: string;
  created_at: string;
  updated_at: string;
};

interface GetPurchaseSupplier {
  data: {
    count: number;
    list: {
      created_at: string;
      emails: string[];
      name: string;
      remark: string;
      supplier_id: number;
    }[];
  };
}
interface DeletePurchaseSupplier {}
interface AddPurchaseSupplier {}
interface EditPurchaseSupplier {}
interface AddSupplierAccount {}

interface GetSupplierLoginAccount {
  data: {
    count: number;
    list: {
      supplier_id: number;
      user_name: string;
      user_id: string;
      deleted: string;
      ips: string[];
      email: string;
    }[];
  };
}

interface EditSupplierLoginAccount {}

interface GetCosKey {
  data: {
    session_token: string;
    tmp_skey: string;
    tmp_sid: string;
    start_time: Date;
    expired_time: Date;
    bucket: string;
  };
}

export type TelqListData = {
  id: number;
  mcc: string;
  mnc: string;
  country_name: string;
  operator: string;
  ported_from_mnc: string;
  ported_from_operator: string;
  created_at: string;
};
interface GetTelqList {
  data: {
    count: number;
    list: TelqListData[];
  };
}

interface AddUserConfig {}
interface EditUserConfig {}

interface GetUserConfig {
  data: {
    count: number;
    list: Array<{
      user_id: number;
      staff_name: string;
      role: string;
      status: number;
      created_at: string;
      updated_at: string;
    }>;
  };
}
interface GetVerifyVCode {}
interface ResetLoginPassword {}
interface RefreshResourcePool {}

interface GetIPBlock {
  data: {
    count: number;
    list: { ip: string; created_at: string }[];
  };
}

interface SignApplyType extends SignApplyFormField {
  apply_id: number;
  created_at: string;
  updated_at: string;
}

interface GetSignApplyList {
  data: {
    count: number;
    list: Array<SignApplyType>;
  };
}

interface GetSignReportList {
  data: {
    count: number;
    list: Array<{
      uin: number;
      qappid: number;
      report_id: number;
      apply_id: number;
      provide_ids: string;
      sign: string;
      account: string;
      sub_code: string;
      remark_type: string;
      company_name: string;
      unisc_id: string;
      corp_name: string;
      transactor_name: string;
      transactor_cr_num: string;
      created_at: string;
      updated_at: string;
    }>;
  };
}

interface GetNeedReportProvider {
  data: {
    [key: number]: {
      apply_id: number;
      apply_info: SignApplyFormField;
      provider_info_arr: {
        account: string;
        provider_id: number;
        show_keys: string[];
        sub_code: string;
        supplier_id: number;
      }[];
      sign: string;
    };
  };
}

interface ChangeSignReportStatus extends LogicalErrorRes {}

interface ChangeReportShowKeys extends LogicalErrorRes {}

interface SubmitSignApply extends LogicalErrorRes {}

interface GetSubCode {
  data:
    | []
    | {
        [key: string]: {
          sign: string;
          provider_id: string;
          sub_code: string[];
        };
      };
}
interface GetProviderList {
  data: {
    count: number;
    list: {
      provider_id: number;
      provider_name: string;
      provider_str: string;
      supplier_id: number;
      account1: string;
      account2: string;
    }[];
  };
}

interface ChangeSignAdditionalReportStatus extends LogicalErrorRes {}

interface GetSignAdditionalReportNeedReport {
  data: {
    count: number;
    list: Array<{
      additional_id?: number;
      provider_id?: number;
      provider_name?: string;
      peer_provider_id?: number;
      peer_provider_name?: string;
      sms_type?: string;
      uin?: number;
      qappid?: number;
      sign?: string;
      status?: number;
      created_at?: string;
      updated_at?: string;
    }>;
  };
}

interface AddSignAdditionalReportByProvider extends LogicalErrorRes {}

interface AddSignAdditionalReportByCustomer extends LogicalErrorRes {}

interface GetSignAdditionalReport {
  data: {
    count: number;
    list: Array<{
      additional_id: number;
      provider_id: number;
      peer_provider_id: number;
      sms_type: number;
      uin: number;
      qappid: number;
      type: number;
      create_rtx: string;
      remark: string;
      ext: string;
      status: number;
      created_at: string;
    }>;
  };
}

interface DeleteSignAdditionalReport {}

interface AddReportFailureReason extends LogicalErrorRes {}

interface EditReportFailureReason {}

interface DeleteReportFailureReason {}

interface GetReportFailureReason {
  data: {
    count: number;
    list: Array<{
      id: number;
      parent_category: string;
      son_category: string;
    }>;
  };
}

interface SendVerifyCode {}

interface SendReportEmail {
  report_id: number;
}

interface GetExportProgessList {
  data: {
    [key: string]:
      | {
          all_params: string;
          supplier_id: string;
          report_count: string;
          created_at: string;
          split_finally_count: string;
          split_total_count: string;
          [key: string]: string;
        }
      | string;
  };
}
interface GetExportProgessInfo {
  data:
    | {
        all_params: string;
        supplier_id: string;
        report_count: string;
        created_at: string;
        split_finally_count: string;
        split_total_count: string;
        [key: string]: string;
      }
    | string;
}

export interface _ApiV3ResType {
  Login: Login;
  GetUserInfo: GetUserInfo; // 外部
  LoginCheck: LoginCheck; // 内部
  GetLoginIp: GetLoginIp;
  Logout: void;
  ChangePassword: ChangePassword;
  DescribeAppAndAccountInfo: DescribeAppAndAccountInfo;

  GetPurchaseSupplier: GetPurchaseSupplier;
  DeletePurchaseSupplier: DeletePurchaseSupplier;
  AddPurchaseSupplier: AddPurchaseSupplier;
  EditPurchaseSupplier: EditPurchaseSupplier;
  AddSupplierAccount: AddSupplierAccount;
  GetSupplierLoginAccount: GetSupplierLoginAccount;
  EditSupplierLoginAccount: EditSupplierLoginAccount;

  GetCosKey: GetCosKey;
  AddUserConfig: AddUserConfig;
  EditUserConfig: EditUserConfig;
  DeleteUserConfig: EditUserConfig;
  GetUserConfig: GetUserConfig;

  GetVerifyVCode: GetVerifyVCode;
  ResetLoginPassword: ResetLoginPassword;
  RefreshResourcePool: RefreshResourcePool;

  DumpRecordWarning: {};
  AddIPBlock: {};
  DeleteIPBlock: {};
  GetIPBlock: GetIPBlock;

  AddSignApply: {};
  EditSignApply: {};
  DeleteSignApply: {};
  GetSignApplyList: GetSignApplyList;
  ChangeSignApplyStatus: {};
  SubmitSignApply: SubmitSignApply;
  GetSignReportList: GetSignReportList;
  ChangeSignReportStatus: ChangeSignReportStatus;
  GetNeedReportProvider: GetNeedReportProvider;
  ChangeReportShowKeys: ChangeReportShowKeys;
  GetProviderList: GetProviderList;
  GetSubCode: GetSubCode;
  ChangeSignAdditionalReportStatus: ChangeSignAdditionalReportStatus;
  GetSignAdditionalReportNeedReport: GetSignAdditionalReportNeedReport;
  AddSignAdditionalReportByProvider: AddSignAdditionalReportByProvider;
  AddSignAdditionalReportByCustomer: AddSignAdditionalReportByCustomer;
  GetSignAdditionalReport: GetSignAdditionalReport;
  DeleteSignAdditionalReport: DeleteSignAdditionalReport;
  ExportSignReport: {};
  AddReportFailureReason: AddReportFailureReason;
  EditReportFailureReason: EditReportFailureReason;
  DeleteReportFailureReason: DeleteReportFailureReason;
  GetReportFailureReason: GetReportFailureReason;
  SendVerifyCode: SendVerifyCode;
  SendReportEmail: SendReportEmail;
  GetAsyncTaskInfo: {};
  GetAsyncTaskList: {};
  AddAsyncTask: {};
  GetSignReportIdList: GetSignReportList;
  GetExportProgessList: GetExportProgessList;
  GetExportProgessInfo: GetExportProgessInfo;
}

export type ApiV3ResType = {
  [k in keyof _ApiV3ResType]: _ApiV3ResType[k] & ErrorRes;
};
export type ApiV3ResKey = keyof ApiV3ResType;
