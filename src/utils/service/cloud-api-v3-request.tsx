import _ from 'lodash';
import { cloneWithLowerFirst } from '@src/utils/formatter/case-formatter';
import { app } from '../tea/app';
import { cloudApiMaps } from './cloud-api-name-maps';
import { Report } from './tdw-report';
import {
  cloudApiTips,
  errorTips,
  CloudApiTipsType,
} from './cloud-api-error-tip-maps';
import { historyMemo } from '@src/global-state/global-history';

import { get, has } from 'lodash-es';
import { ApiV3ReqKey } from './cloud-api-request-type';
import { HotReport } from './hot-report';
import { t } from '@src/utils/i18n';
import { modalMemo } from '@src/global-components/ip-change-modal/modalMemo';
import {
  Copy,
  List,
  Modal,
  Table,
  message,
  Text,
} from '@tencent/tea-component';
import { scrollable } from '@tencent/tea-component/lib/table/addons';
import Cookies from 'js-cookie';
import { uuid } from '../tools';

export const checkRedirectToLogin = () => {
  if (window.location.href.includes('/login')) {
    return;
  }
  if (historyMemo.history) {
    historyMemo.history.replace({ pathname: '/login' });
  } else {
    window.location.replace('/login');
  }
};

export const cloudApiV3Request = async (
  name: ApiV3ReqKey,
  params: any,
  opt: {
    serviceType: string;
  } = {
    serviceType: 'sms',
  },
) => {
  if (!Cookies.get('auth_token')) {
    checkRedirectToLogin();
  }
  const apiName = cloudApiMaps[name] || name;
  try {
    const REQUEST_ID = uuid();
    const result = await app.capi.requestV3(
      {
        regionId: 1,
        serviceType: 'sms',
        cmd: apiName,
        data: {
          ...params,
        },
        headers: {
          'request-id': REQUEST_ID,
        },
      },
      {
        tipErr: false,
      },
    );

    Report({
      tdw: {
        op_name: 'apisuccess',
        op_type: apiName,
        info: result.data.Response?.RequestId,
      },
    });
    const resData = cloneWithLowerFirst(
      result.data.Response || result.data,
    ) as any;

    if (resData.code === 10000) {
      checkRedirectToLogin();
    }
    if (resData.code === 10003) {
      modalMemo.changeVisible(true);
    }

    if (result.headers['content-type'].includes('text/csv')) {
      return result.data;
    }

    if (resData.code !== 0) {
      if (resData.code === 2) {
        Modal.error({
          message: t('参数错误【{{REQUEST_ID}}】', { REQUEST_ID }),
          description: (
            <>
              {Object.keys(resData.msg ?? {}).map((v) => {
                return (
                  <List type="bullet">
                    {resData.msg[v].map((item) => {
                      return (
                        <List.Item style={{ marginBottom: 0 }}>
                          {item}
                        </List.Item>
                      );
                    })}
                  </List>
                );
              })}
            </>
          ),
        });
      } else {
        app.tips.error(
          typeof resData.msg === 'string'
            ? `${resData.msg}【${REQUEST_ID}】`
            : JSON.stringify(
                `${resData.msg}【${REQUEST_ID}】` ||
                  t('失败【{{REQUEST_ID}}】', { REQUEST_ID }),
              ),
        );
      }
      throw new Error(resData.msg);
    } else {
      if (resData?.data?.errors?.length) {
        const list = resData.data.errors;
        const columns = Object.keys(resData.data.errors[0]).map((k) => ({
          header: k,
          key: k,
          width: k === 'line' || k === 'code' ? 50 : 200,
          render: (row) => {
            const showCopy = k !== 'line' && k !== 'code';
            const text =
              typeof row[k] === 'string' ? row[k] : JSON.stringify(row[k]);
            return (
              <>
                <Text tooltip={text} className="text-overflow">
                  {showCopy && <Copy text={text} />}
                  {text}
                </Text>
              </>
            );
          },
        }));
        Modal.error({
          message: t('操作失败【{{REQUEST_ID}}】', { REQUEST_ID }),
          size: 'l',
          description: (
            <Table
              // compact
              bordered
              records={list}
              columns={columns}
              addons={[
                scrollable({
                  maxHeight: 500,
                  virtualizedOptions: {
                    height: 310,
                    itemHeight: 60,
                  },
                }),
              ]}
            />
          ),
          maskClosable: false,
        });
      }
    }
    return resData;
  } catch (error: any) {
    if (error?.response?.status === 504) {
      message.error({ content: t('操作超时未响应') });
    }
    if (/^(4|5)\d{2}$/.test(error?.response?.status)) {
      message.error({
        content: t('接口失败【{{attr0}}】', { attr0: error?.response?.status }),
      });
    }

    const errorCode: CloudApiTipsType = get(
      error,
      'response.data.Response.Error.Code',
    );
    const errorMessage: keyof typeof cloudApiTips.InternalError = get(
      error,
      'response.data.Response.Error.Message',
    );

    let tips = '';

    if (errorCode === 'AuthFailure.UnauthorizedOperation') {
      tips = errorMessage;
      const cam = await app.sdk.use('cam-sdk');
      cam.showBreakModal({ message: errorMessage });
    }

    console.log({ errorCode, errorMessage });
    /* --- 错误消息的四种分类 --- */
    if (has(cloudApiTips, [errorCode, errorMessage, 'errorMsg'])) {
      tips = get(cloudApiTips, [errorCode, errorMessage, 'errorMsg']);
    } else if (has(cloudApiTips, [errorCode, 'errorMsg'])) {
      tips = get(cloudApiTips, [errorCode, 'errorMsg']);
    } else if (has(errorTips, errorMessage)) {
      // @ts-ignore
      tips = get(errorTips, errorMessage);
    } else {
      tips = get(error, ['data', 'message']);
    }
    if (tips) app.tips.error(tips);

    console.log(tips);

    HotReport('apierror', apiName, {
      info: get(error, 'data.data.Response.RequestId'),
      custom1: tips,
    });
    if (errorCode) throw new Error(errorCode);
    throw error;
  }
};
