import _ from 'lodash';
import { cloneWithLowerFirst } from '@src/utils/formatter/case-formatter';
import { app } from '../tea/app';
import { Copy, Text } from '@tencent/tea-component';
import { Report } from './tdw-report';
import {
  cloudApiTips,
  errorTips,
  CloudApiTipsType,
} from './cloud-api-error-tip-maps';
import { cloudApiMapsV2 } from './cloud-api-name-maps';

import { get, has } from 'lodash-es';
import { HotReport } from './hot-report';
import axios, { Method } from 'axios';
import { t } from '@src/utils/i18n';
import { List, Modal, Table, message } from '@tencent/tea-component';
import { scrollable } from '@tencent/tea-component/lib/table/addons';
import qs from 'qs';
import { uuid } from '../tools';

export const cloudApiV3RequestV2 = async ({
  url,
  params,
  config = {},
}: {
  url: string;
  params?: any;
  config?: { [key: string]: any };
}) => {
  try {
    const apiName = cloudApiMapsV2[url] || url;
    const found = apiName.match(/((post|get|put)::)?(.*)/) || [];
    const method = (found[2] as Method) || 'get';
    const indexKey = method === 'get' ? 'params' : 'data';
    const REQUEST_ID = uuid();

    const result = await axios({
      method,
      url: `/api-inner${found[3]}`,
      [indexKey]: {
        ...params,
      },
      ...config,
      headers: {
        'request-id': REQUEST_ID,
      },
    });

    Report({
      tdw: {
        op_name: 'apisuccess',
        op_type: url,
        info: result.data.Response?.RequestId,
      },
    });

    const resData = result.data;

    if (resData.code !== 0) {
      if (resData.code === 2) {
        Modal.error({
          message: t('参数错误【{{REQUEST_ID}}】', { REQUEST_ID }),
          description: (
            <>
              {Object.keys(resData.msg ?? {}).map((v) => {
                return (
                  <List type="bullet">
                    {resData.msg[v].map((item) => {
                      return (
                        <List.Item style={{ marginBottom: 0 }}>
                          {item}
                        </List.Item>
                      );
                    })}
                  </List>
                );
              })}
            </>
          ),
        });
      } else {
        app.tips.error(
          typeof resData.msg === 'string'
            ? `${resData.msg}【${REQUEST_ID}】`
            : JSON.stringify(
                `${resData.msg}【${REQUEST_ID}】` ||
                  t('失败【{{REQUEST_ID}}】', { REQUEST_ID }),
              ),
        );
      }
      throw new Error(resData.msg);
    } else {
      if (resData?.data?.errors?.length) {
        const list = resData.data.errors;
        const columns = Object.keys(resData.data.errors[0]).map((k) => ({
          header: k,
          key: k,
          width: k === 'line' || k === 'code' ? 50 : 200,
          render: (row) => {
            const showCopy = k !== 'line' && k !== 'code';
            const text =
              typeof row[k] === 'string' ? row[k] : JSON.stringify(row[k]);
            return (
              <>
                <Text tooltip={text} className="text-overflow">
                  {showCopy && <Copy text={text} />}
                  {text}
                </Text>
              </>
            );
          },
        }));
        Modal.error({
          message: t('操作失败【{{REQUEST_ID}}】', { REQUEST_ID }),
          size: 'l',
          description: (
            <Table
              // compact
              bordered
              records={list}
              columns={columns}
              addons={[
                scrollable({
                  maxHeight: 500,
                  virtualizedOptions: {
                    height: 310,
                    itemHeight: 60,
                  },
                }),
              ]}
            />
          ),
          maskClosable: false,
        });
      }
    }

    return resData;
  } catch (error: any) {
    if (error?.response?.status === 504) {
      message.error({ content: t('操作超时未响应') });
    }

    const errorCode: CloudApiTipsType = get(
      error,
      'response.data.Response.Error.Code',
    );
    const errorMessage: keyof typeof cloudApiTips.InternalError = get(
      error,
      'response.data.Response.Error.Message',
    );

    let tips = '';

    if (errorCode === 'AuthFailure.UnauthorizedOperation') {
      tips = errorMessage;
      const cam = await app.sdk.use('cam-sdk');
      cam.showBreakModal({ message: errorMessage });
    }

    console.log({ errorCode, errorMessage });
    /* --- 错误消息的四种分类 --- */
    if (has(cloudApiTips, [errorCode, errorMessage, 'errorMsg'])) {
      tips = get(cloudApiTips, [errorCode, errorMessage, 'errorMsg']);
    } else if (has(cloudApiTips, [errorCode, 'errorMsg'])) {
      tips = get(cloudApiTips, [errorCode, 'errorMsg']);
    } else if (has(errorTips, errorMessage)) {
      // @ts-ignore
      tips = get(errorTips, errorMessage);
      // } else if (isCN && has(chinaLandCloudApiTips, [errorCode, 'errorMsg'])) {
      //   tips = get(chinaLandCloudApiTips, [errorCode, 'errorMsg']);
      // } else if (!isCN && has(overseasCloudApiTips, [errorCode, 'errorMsg'])) {
      //   tips = get(overseasCloudApiTips, [errorCode, 'errorMsg']);
    } else {
      tips = get(error, ['data', 'message']);
    }
    if (tips) app.tips.error(tips);

    console.log(tips);

    HotReport('apierror', url, {
      info: get(error, 'data.data.Response.RequestId'),
      custom1: tips,
    });
    if (errorCode) throw new Error(errorCode);
    throw error;
  }
};
