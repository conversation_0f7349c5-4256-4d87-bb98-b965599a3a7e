/* eslint-disable @typescript-eslint/no-unused-vars */

// 热点上报

// TS 参数类型互相校验
export type HotReportType = {
  overview:
    | 'exp_page'
    | 'clk_createSDKAppID'
    | 'suc_createSDKAppID'
    | 'clk_nextPage'
    | 'clk_notification'
    | 'clk_guideDoc'
    | 'clk_webDemo'
    | 'clk_webLiveDemo'
    | 'clk_SDKdownload'
    | 'clk_videoGuide'
    | 'clk_helpCenter';

  // 应用详情-登录与消息
  AppInfo_msg:
    | 'exp_page'
    | 'edit_login'
    | 'suc_login'
    | 'edit_historicalMsg'
    | 'suc_historicalMsg'
    | 'docView_historicalMsg'
    | 'edit_msgRecall'
    | 'suc_msgRecall'
    | 'docView_msgRecall'
    | 'on_blacklist'
    | 'off_blacklist'
    | 'docView_blacklist'
    | 'on_FrdCheck'
    | 'off_FrdCheck'
    | 'docView_FrdCheck'
    | 'view_FAQ'
    | 'clk_helpCenter';

  // 好友与关系链
  AppInfo_frd:
    | 'exp_page'
    | 'edit_addFrdSetting'
    | 'suc_addFrdSetting'
    | 'add_customFields'
    | 'suc_customFields'
    | 'clk_helpCenter';
  // 用户自定义字段
  AppInfo_user:
    | 'exp_page'
    | 'add_customFields'
    | 'suc_customFields'
    | 'edit_customFields'
    | 'sucEdit_customFields'
    | 'clk_helpCenter';
  // 群成员自定义字段
  AppInfo_grpMember:
    | 'exp_page'
    | 'add_customFields'
    | 'suc_customFields'
    | 'edit_customFields'
    | 'sucEdit_customFields'
    | 'clk_helpCenter';

  // 群自定义字段
  AppInfo_grpCustom:
    | 'exp_page'
    | 'add_customFields'
    | 'suc_customFields'
    | 'edit_customFields'
    | 'sucEdit_customFields'
    | 'clk_helpCenter'
    | 'view_FAQ';

  overview_AppCard:
    | 'clk_AppCard'
    | 'clk_AppInfo'
    | 'clk_copySDKAppID'
    | 'exp_verCompare'
    | 'exp_reachedTop';

  overview_verCompare:
    | 'exp_upgrade'
    | 'clk_upgrade'
    | 'exp_addService'
    | 'clk_addService'
    | 'exp_flagship'
    | 'clk_flagship'
    | 'exp_cntUpgrade';

  AppInfo: 'clk_back' | 'clk_changeApp';

  AppInfo_basic:
    | 'exp_page'
    | 'exp_deactivate'
    | 'clk_deactivate'
    | 'suc_deactivate'
    | 'exp_delete'
    | 'clk_delete'
    | 'suc_delete'
    | 'exp_renewSetting'
    | 'clk_renewSetting'
    | 'exp_upgrade'
    | 'clk_upgrade'
    | 'exp_flagship'
    | 'clk_flagship'
    | 'exp_addService'
    | 'clk_addService'
    | 'exp_setting'
    | 'clk_setting'
    | 'suc_setting'
    | 'exp_applyUpgrade'
    | 'clk_applyUpgrade'
    | 'clk_applyOrder'
    | 'exp_resetTrail'
    | 'clk_resetTrail'
    | 'suc_resetTrail'
    | 'exp_activate'
    | 'clk_activate'
    | 'clk_noOverdue'
    | 'clk_activateOrder'
    | 'exp_renew'
    | 'clk_renew'
    | 'exp_refund'
    | 'clk_refund'
    | 'exp_recharge'
    | 'clk_recharge'
    | 'exp_cntUpgrade'
    | 'copy_SDKAppID'
    | 'clk_editInfo'
    | 'suc_editInfo'
    | 'display_userSig'
    | 'hide_userSig'
    | 'copy_userSig'
    | 'clk_addAdmin'
    | 'suc_addAdmin'
    | 'clk_delAdmin'
    | 'suc_delAdmin'
    | 'view_offlinePushDoc'
    | 'clk_addAndroidID'
    | 'suc_addAndroidID'
    | 'clk_addiOSID'
    | 'suc_addiOSID'
    | 'clk_editChannel'
    | 'suc_editChannel'
    | 'clk_delChannel'
    | 'suc_delChannel'
    | 'clk_editTag'
    | 'suc_editTag'
    | 'clk_onTRTC'
    | 'suc_onTRTC'
    | 'view_FAQ'
    | 'clk_helpCenter';

  apierror: string;
  apisuccess: string;
};

type OtherOpt = {
  info?: string;
  appname?: string;
  sdkappid?: number;
  custom1?: string | number;
  custom2?: string | number;
};

export const HotReport = <
  Key extends keyof HotReportType,
  Key2 extends HotReportType[Key],
>(
  opType: Key,
  opName: Key2,
  otherOpt?: OtherOpt,
) => {
  console.log('hotReport', [opType, opName, otherOpt]);
};

// for example
// HotReport('AppInfo_msg', 'edit_login');
