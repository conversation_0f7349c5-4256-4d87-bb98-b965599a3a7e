import Cookies from 'js-cookie';
import _ from 'lodash';
import * as jose from 'jose';
import { loadingMemo } from '@src/global-state/global-loading';

import { cloudApiV3Request } from './cloud-api-v3-request';
import { ApiV3ReqType } from './cloud-api-request-type';
import { ApiV3ResType } from './cloud-api-response-type';
import { ApiV3ReqKey } from './cloud-api-request-type';
import axios from 'axios';
import { modalMemo } from '@src/global-components/ip-change-modal/modalMemo';
import { uuid } from '../tools';
import { app } from '@tea/app/index';

window.Cookies = Cookies;
const requestStart = () => {
  loadingMemo.setGlobalLoading((count: number) => {
    return count + 1;
  });
};
const requestEnd = () => {
  loadingMemo.setGlobalLoading((count: number) => {
    return count - 1;
  });
};

const getExpired = () => {
  const token = Cookies.get('auth_token');
  if (!token) return 0;
  const dec = jose.decodeJwt(token);
  return dec.exp ?? 0;
};

let preP: any = null;
export const refreshFn = async () => {
  const REQUEST_ID = uuid();
  const main = async () => {
    if (window.location.href.includes('/login')) return;
    if (preP) {
      await preP;
    }
    if (Date.now() - getExpired() * 1000 > 5) {
      const res = await axios({
        method: 'post',
        url: '/apis/admin/supplier-user/refresh-token',
        headers: {
          Authorization: 'Bearer ' + Cookies.get('auth_token'),
          'request-id': REQUEST_ID,
        },
      });
      if (res?.data?.code === 10003) {
        modalMemo.changeVisible(true);
        app.tips.error(res?.data?.msg + `【${REQUEST_ID}】`);
        throw new Error(res?.data?.msg);
      }
    }
  };
  preP = main();
  return await preP;
};

// @ts-ignore
// window.refreshFn = refreshFn;
/**
 * !warning 非 api3.0 的接口不要在这里
 * @param url api3.0 的 name
 * @param params api3.0 的参数
 */

export const getTinyId = () => {
  const tinyId = Cookies.get('tinyid');
  return tinyId ?? '';
};
export const getSKey = () => {
  const skey = Cookies.get('skey');
  return skey ?? '';
};
export const getAppId = () => {
  const appid = Cookies.get('appid');
  return appid ?? '';
};

export const service = async function <
  T extends keyof ApiV3ResType & keyof ApiV3ReqType,
  PT extends ApiV3ReqType[T] = ApiV3ReqType[T],
>(
  // cmd
  url: T,
  params?: PT,
  opt = {
    serviceType: 'sms',
  },
): Promise<ApiV3ResType[T]> {
  if (!url || typeof url !== 'string') {
    return Promise.reject('url must be string');
  }

  try {
    await refreshFn();

    requestStart();
    const name = url;

    const reqParams = _.omit<ApiV3ReqType[T]>(params, [
      'base',
      'needSkey',
      'needTinyId',
      'CamTag',
      'Language',
    ]);

    const reqOption = _.pick(params, [
      'base',
      'needSkey',
      'needTinyId',
      'CamTag',
      'Language',
    ]);

    if (reqOption.needSkey) {
      const skey = Cookies.get('skey');
      if (!skey) {
        // console.log(t('参数（skey）错误，请刷新后重试。'));
        // throw new Error('needSkey');
      }
      reqParams.skey = skey;
    }

    if (reqOption.needTinyId) {
      const tinyId = Cookies.get('tinyid');
      if (!tinyId) {
        if (import.meta.env.REACT_APP_IS_PRIVATI) {
          // console.log(t('参数（tinyid）错误，请刷新后重试。'));
        }
        // throw new Error('needTinyId');
      } else {
        reqParams.tinyId = tinyId;
      }
    }

    // if (i18n.lang !== 'zh') {
    //   reqParams.Language = 'en-US';
    // }

    const result = await cloudApiV3Request(name, reqParams, opt);
    await refreshFn();
    return result;
  } catch (error: any) {
    console.error(error);
    throw error;
  } finally {
    requestEnd();
  }
};
