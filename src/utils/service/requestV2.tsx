import Cookies from 'js-cookie';
import _ from 'lodash';
import { t } from '@src/utils/i18n';

import { loadingMemo } from '@src/global-state/global-loading';
import { historyMemo } from '@src/global-state/global-history';

import { i18n } from '../tea/app';

import { cloudApiV3RequestV2 } from './cloud-api-v3-requestV2';
import { ApiV3ReqType } from './cloud-api-request-type';
import { ApiV3ResType } from './cloud-api-response-type';
import { ApiV3ReqKey } from './cloud-api-request-type';

const requestStart = () => {
  loadingMemo.setGlobalLoading((count: number) => {
    return count + 1;
  });
};
const requestEnd = () => {
  loadingMemo.setGlobalLoading((count: number) => {
    return count - 1;
  });
};

/**
 * !warning 非 api3.0 的接口不要在这里
 * @param url api3.0 的 name
 * @param params api3.0 的参数
 */

export const getTinyId = () => {
  const tinyId = Cookies.get('tinyid');
  return tinyId ?? '';
};
export const getSKey = () => {
  const skey = Cookies.get('skey');
  return skey ?? '';
};
export const getAppId = () => {
  const appid = Cookies.get('appid');
  return appid ?? '';
};

export const serviceV2 = async function <
  T extends keyof ApiV3ResType & keyof ApiV3ReqType,
  PT extends ApiV3ReqType[T] = ApiV3ReqType[T],
>(
  // cmd
  url: string,
  params?: PT,
  config?: { [key: string]: any },
): Promise<ApiV3ResType[T]> {
  if (!url || typeof url !== 'string') {
    Promise.reject('url must be string');
  }

  requestStart();

  const reqParams = _.omit<ApiV3ReqType[T]>(params, [
    'base',
    'needSkey',
    'needTinyId',
    'CamTag',
    'Language',
  ]);

  const reqOption = _.pick(params, [
    'base',
    'needSkey',
    'needTinyId',
    'CamTag',
    'Language',
  ]);

  if (reqOption.needSkey) {
    const skey = Cookies.get('skey');
    if (!skey) {
      // console.log(t('参数（skey）错误，请刷新后重试。'));
      // throw new Error('needSkey');
    }
    reqParams.skey = skey;
  }

  if (reqOption.needTinyId) {
    const tinyId = Cookies.get('tinyid');
    if (!tinyId) {
      if (import.meta.env.REACT_APP_IS_PRIVATI) {
        // console.log(t('参数（tinyid）错误，请刷新后重试。'));
      }

      // throw new Error('needTinyId');
    } else {
      reqParams.tinyId = tinyId;
    }
  }

  // if (i18n.lang !== 'zh') {
  //   reqParams.Language = 'en-US';
  // }

  try {
    const result = await cloudApiV3RequestV2({
      url,
      params: reqParams,
      config,
    });
    return result;
  } catch (error: any) {
    console.error(error);

    throw error;
  } finally {
    requestEnd();
  }
};
