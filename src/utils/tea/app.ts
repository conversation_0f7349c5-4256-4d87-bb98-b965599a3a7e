import { app as _app, i18n as _i18n } from '@tea/app';
import { message } from '@tencent/tea-component';
import axios from 'axios';
import _, { isNil } from 'lodash';
import { v4 as uuidv4 } from 'uuid';
import Cookies from 'js-cookie';

export const app = {
  tips: {
    success: (content: React.ReactNode, duration?: number) => {
      message.success({ content, duration });
    },
    warning: (content: React.ReactNode, duration?: number) => {
      message.warning({ content, duration });
    },
    error: (content: React.ReactNode, duration?: number) => {
      message.error({ content, duration });
    },
    loading: (content: React.ReactNode, duration?: number) => {
      message.loading({ content, duration });
    },
  },
  capi: {
    requestV3: async (requestBody: any, requestOptions: any): Promise<any> => {
      const _requestBody = _.pick(_.cloneDeep(requestBody), ['cmd', 'data']);
      _.set(_requestBody, 'data.RequestId', uuidv4());
      _.unset(_requestBody, 'data.Region');
      const cmd = _.get(_requestBody, 'cmd');
      const found = cmd.match(/((post|get|put)::)?(.*)/);
      const method = found[2] || 'get';
      const indexKey = method === 'get' ? 'params' : 'data';
      try {
        // const path = _.words(cmd)
        //   .map((v) => v.toLowerCase())
        //   .join('/');
        const reqeust = await axios({
          method,
          url: `/apis${found[3]}`,
          headers: {
            Authorization: 'Bearer ' + Cookies.get('auth_token'),
            ...(requestBody?.headers ?? {}),
          },
          [indexKey]: {
            ...requestBody.data,
          },
        });
        // const reqeust = await axios.post(
        //   `/mock/cgi/capi/${cmd}`,
        //   _requestBody,
        //   {
        //     params: { cmd }
        //   }
        // );
        return reqeust;
      } catch (error: any) {
        throw error;
      }
    },
  },
  sdk: {
    async use(pluginNames: string) {
      console.log(pluginNames);
      return {
        showBreakModal: console.log,
      };
    },
  },
};
export const i18n = _i18n;
