import dayjs from 'dayjs';
import 'dayjs/locale/zh-cn';

import utc from 'dayjs/plugin/utc'; // dependent on utc plugin
import timezone from 'dayjs/plugin/timezone';

dayjs.extend(utc);
dayjs.extend(timezone);

// 设置国际化 i18n 翻译
dayjs.locale('zh-cn');

// 设置时区
// dayjs.tz.setDefault('UTC')

// @ts-ignore
window.dayjs = dayjs;

export default dayjs;

/**
 * @description 通过 unix 秒数，解析出 dayjs 对象
 * @param unixTimeStamp unix 秒数，解析为 dayjs 对象
 */
export const dayJsUnix = (unixTimeStamp: number) => {
  const d1 = dayjs.unix(unixTimeStamp);
  return d1.isValid() ? d1 : dayjs(unixTimeStamp);
};

/**
 * @description 通过 任何可以传给 dayjs 的格式 ，解析出 dayjs 对象 或者合法的 dayjs 参数，不传则为当前时间
 * @param timeString 任何可以传给 dayjs 的格式
 */
export const dayJsYMD = (timeString?: dayjs.ConfigType | undefined) =>
  dayjs.tz(dayjs(timeString));
