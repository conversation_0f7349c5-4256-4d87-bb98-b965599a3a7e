export const timeTaskRunner = (timeout?: number) => {
  let lastTaskTimeId: NodeJS.Timeout | null;
  let lastRunedTaskTimeId: NodeJS.Timeout | null;

  const cancel = () => {
    lastTaskTimeId && clearTimeout(lastTaskTimeId);
  };

  const ttr = {
    run: (fn: (...args: any[]) => any) => {
      cancel();
      lastTaskTimeId = setTimeout(() => {
        fn();
        lastTaskTimeId = null;
      }, timeout || 5);
      lastRunedTaskTimeId = lastTaskTimeId;
      return lastTaskTimeId;
    },
    getLastRunedTaskId() {
      return lastRunedTaskTimeId;
    },
    cancel,
    getCurrentTTR() {
      return ttr;
    },
  };

  return ttr;
};
