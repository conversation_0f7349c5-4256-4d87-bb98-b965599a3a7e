import { i18n } from '@tencent/tea-app';

export const isMainLand = () => {
  return i18n.site === 1;
};

export const isChinaLand = () => {
  return i18n.lng === 'zh';
};

export const computeUrl = (s?: string) => {
  if (!s) return '-';
  return s.replace(/^http(s)?:/, '');
};

export const isPureEn = (str: String) => {
  // 注意：GSMSStandardWords 第一个字符为空格
  const GSMSStandardWords =
    ' !"#$%\'()*+,-./:;<=>?@_¡£¥§¿&¤0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyzÄÅÆÇÉÑØøÜßÖàäåæèéìñòöùüΔΦΓΛΩΠΨΣΘΞ';
  const GSMExtendWords = '|^€{}[]~\\\n';
  // eslint-disable-next-line @typescript-eslint/prefer-for-of
  for (var i = 0; i < str.length; i++) {
    if (
      !(GSMExtendWords.includes(str[i]) || GSMSStandardWords.includes(str[i]))
    ) {
      return false;
    }
  }
  return true;
};

export const convertFeeByContent = (sms_type: Number, tpl_content: String) => {
  var tpl_fee = 0;
  var tpl_length = tpl_content.length;
  if (sms_type === 10 || (sms_type === 30 && !isPureEn(tpl_content))) {
    // 非纯英文
    tpl_fee = tpl_length <= 70 ? 1 : Math.ceil(tpl_length / 67);
    tpl_fee = tpl_length === 0 ? 0 : tpl_fee;
  } else {
    // 国际的需要判断是否为纯英文，纯英文按照160/153算，非纯英文按照70/67
    // eslint-disable-next-line no-useless-escape
    const regex = /[|,€,{,},\[,\],\~,\\,\^,\n]/g; // 扩展字符按2个字符计费
    const found = tpl_content.match(regex);
    if (found?.length) {
      tpl_length = tpl_length + found.length;
    }
    tpl_fee = tpl_length <= 160 ? 1 : Math.ceil(tpl_length / 153);
    tpl_fee = tpl_length === 0 ? 0 : tpl_fee;
  }
  return tpl_fee;
};

export function isURL(urlString: string): boolean {
  try {
    new URL(urlString);
    return true;
  } catch (error) {
    return false;
  }
}

export function generateRandomString() {
  const characters =
    'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
  let randomString = '';
  for (let i = 0; i < 16; i++) {
    const randomIndex = Math.floor(Math.random() * characters.length);
    randomString += characters.charAt(randomIndex);
  }
  return randomString;
}

export function uuid() {
  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, (c) => {
    const r = (Math.random() * 16) | 0;
    const v = c === 'x' ? r : (r & 0x3) | 0x8;
    return v.toString(16);
  });
}
