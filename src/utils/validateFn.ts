import { t } from '@src/utils/i18n';
import _, { trim } from 'lodash';

export const validateRequired = (_value: string | any) => {
  const value = typeof _value === 'string' ? _.trim(_value) : _value;

  if (_.isNil(value) || value === '') {
    return t('必填');
  }
  if (Array.isArray(value) && value.length === 0) {
    return t('必填');
  }
};

export const validateNumber = (value: string) => {
  const reg = new RegExp(/^\d{1,}$/g);
  if (value === undefined || value === '') {
    return t('必填');
  }
  console.log(value);
  if (!reg.test(value)) {
    return t('请输入整数');
  }
};

export const validateNumberWithRange = (value: string, range?: number[]) => {
  const reg = new RegExp(/^\d{1,}$/g);
  if (value === undefined || value === '') {
    return t('必填');
  }
  if (!reg.test(value)) {
    return t('请输入整数');
  }
  if (range && range.length === 2) {
    if (Number(value) > range[1] || Number(value) < range[0]) {
      return t('请输入{{min}} ～ {{max}}之间的数值', {
        max: range[1],
        min: range[0],
      });
    }
  }
};

export const validatePrice = (value: string) => {
  const reg = new RegExp(/(^[1-9]\d*(\.\d{1,6})?$)|(^0(\.\d{1,6})?$)/);
  if (value === undefined || value === '') {
    return t('必填');
  }
  if (!reg.test(value)) {
    return t('请输入正确的数字格式');
  }
};

export const validateEmail = (_value: string) => {
  const value = trim(_value);
  const reg = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;
  if (value === undefined || value === '') {
    return t('必填');
  }
  if (!reg.test(value)) {
    return t('邮箱格式错误');
  }
};

export const validateEmailPure = (_value: string) => {
  const value = trim(_value);
  const reg = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;
  if (value === undefined || value === '') {
    return undefined;
  }
  if (!reg.test(value)) {
    return t('邮箱格式错误');
  }
};

export const validatePassword = (value: string) => {
  const reg =
    /(?=.*([a-zA-Z].*))(?=.*[0-9].*)[a-zA-Z0-9-*/+.~!@#$%^&*()]{10,100}$/;
  if (!value || value.length < 10) {
    return t('密码太短');
  }
  if (!reg.test(value)) {
    return t('密码格式错误，可包含数字、字母和符号，字母区分大小写');
  }
};

// 组合校验
export const validateCombined = (
  value,
  validates: Array<(value) => string | undefined>,
) => {
  const errors: string[] = [];
  validates.forEach((validate) => {
    const res = validate(value);
    res !== undefined && errors.push(res);
  });
  return errors.length > 0 ? errors[0] : undefined;
};

export const validateDomainPort = (value: string) => {
  const reg = /^([a-zA-Z0-9-]{1,63}\.)+[a-zA-Z]{2,63}:(\d{1,5})$/;
  if (!reg.test(value)) {
    return t('域名格式错误，正确格式：域名:port，例如：example.com:8080');
  }
};

export const validateIdCard = (value: string) => {
  const _IDRe18 =
    /^([1-6][1-9]|50)\d{4}(18|19|20)\d{2}((0[1-9])|10|11|12)(([0-2][1-9])|10|20|30|31)\d{3}[0-9X]$/;
  const _IDre15 =
    /^([1-6][1-9]|50)\d{4}\d{2}((0[1-9])|10|11|12)(([0-2][1-9])|10|20|30|31)\d{3}$/;
  if (_IDRe18.test(value) || _IDre15.test(value)) {
    return undefined;
  }
  return t('请输入正确的证件号码');
};

export const validateChineseName = (value: string) => {
  const reg =
    /^[\u00B7\u3007\u3400-\u9FFF\uE000-\uF8FF\uF900-\uFAFF\u{20000}-\u{2FFFF}\u{30000}-\u{3FFFF}]+$/u;
  if (!reg.test(value)) {
    return t('请输入正确姓名');
  }
};
