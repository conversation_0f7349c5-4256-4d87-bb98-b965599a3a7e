import { getHasPermission, useUserInfo } from '@src/PermissionController';
import { t, Trans, Slot } from '@src/utils/i18n';
import { qsStringify } from '@src/utils/react-use/qsStringify';
import { useQuery } from '@src/utils/react-use/useQuery';
import { Button, H3 } from '@tencent/tea-component';
import { Link, Redirect, useLocation } from 'react-router-dom';

interface Props {
  redirect?: string;
  need?: string;
}
export const ForbiddenPage = (props: Props) => {
  const { need, redirect } = useQuery<Props>();
  const location = useLocation();
  const userInfo = useUserInfo();

  const hasPermission = getHasPermission(need?.split(','), userInfo);

  if (location.pathname === '/') {
    return (
      <Redirect
        to={{
          pathname: '/index',
        }}
      />
    );
  }

  if (hasPermission) {
    const pathname = new URL(redirect || '')?.pathname || '/index';
    return (
      <Redirect
        to={{
          pathname,
        }}
      />
    );
  }

  return (
    <div style={{ textAlign: 'center', paddingTop: 40 }}>
      <H3>{t('您无权限')}</H3>
      <div style={{ textAlign: 'center', paddingTop: 40 }}></div>

      {need && (
        <div>
          <Trans>
            缺少以下权限：
            <Slot content={need} />
          </Trans>
        </div>
      )}
      <div style={{ textAlign: 'center', paddingTop: 40 }}></div>
      <Link
        to={{
          pathname: '/index',
        }}
      >
        {t('跳转概览页')}
      </Link>
    </div>
  );
};
