import { useMount, useSearchParam, useToggle } from 'react-use';
import { useHistory } from 'react-router-dom';
import {
  Card,
  Text,
  H3,
  Layout,
  Icon,
  List,
  NavMenu,
} from '@tencent/tea-component';
import { LoginForm } from './login-components/LoginForm';
import style from './Login.module.less';
import { t } from '@src/utils/i18n';
import { login } from '@src/api/login';
import { _useUserState } from '@src/global-state';
import _ from 'lodash';
import { getLoginIp } from '@src/api/getLoginIp';
import { useState } from 'react';
import { lng } from '@tea/app/i18n';
import { LanguageChange } from '@src/global-components/layout/layout-components/LanguageChange';

const { Content, Header } = Layout;

console.log(lng, '=========');
export const Login = () => {
  const history = useHistory();
  const redirect = useSearchParam('redirect') || '';
  const [loginIp, setLoginIp] = useState('');
  const [loading, setLoading] = useState(false);

  const [, setUserInfo] = _useUserState();

  const [isSubLogin, setIsSubLogin] = useToggle(false);

  useMount(() => {
    setLoading(true);
    getLoginIp()
      .then((res) => {
        setLoading(false);
        setLoginIp(res?.data?.ip || '');
      })
      .catch(() => {
        setLoading(false);
        setLoginIp('');
      });
  });

  async function onSubmit({
    password,
    email,
    verifycode,
  }: {
    verifycode: string;
    password: string;
    email: string;
  }) {
    // if (!isSubLogin) {
    const userInfo = await login({
      email,
      password,
      verifycode,
    });

    setUserInfo({
      loginState: true,
      ...userInfo.data,
    });
    // } else {
    //   const userInfo = await subLogin({
    //     username,
    //     password,
    //     parentUsername: parentUsername ?? '',
    //   });
    //   setUserInfo({
    //     loginState: true,
    //     ...userInfo,
    //   });
    // }
  }

  const handlerSuccess = () => {
    try {
      const redirectPath = new URL(redirect).pathname;
      history.replace({ pathname: redirectPath || '/sign/apply' });
    } catch (error: any) {
      history.replace({ pathname: '/sign/apply' });
    }
  };

  return (
    <Layout>
      <Header>
        <NavMenu
          style={{ backgroundColor: '#fff' }}
          right={
            <NavMenu.Item
              type="dropdown"
              overlay={() => (
                <List type="option">
                  <LanguageChange />
                </List>
              )}
            >
              <span style={{ color: '#000' }}>
                {lng === 'en' ? 'English' : t('中文')}
              </span>
            </NavMenu.Item>
          }
        />
      </Header>
      <Content.Body className={style.main}>
        <Card className={style.card}>
          <Card.Header className={style.header}>
            <H3>{t('腾讯云国内短信管理后台')}</H3>
          </Card.Header>
          <Card.Body className={style.body}>
            <LoginForm
              onSubmit={onSubmit}
              onSuccess={handlerSuccess}
            ></LoginForm>
          </Card.Body>
        </Card>
      </Content.Body>
      <Content.Footer>
        <div
          style={{
            position: 'absolute',
            bottom: 10,
            right: 10,
          }}
        >
          {loading ? <Icon type="loading" /> : <Text>{'IP：' + loginIp}</Text>}
        </div>
      </Content.Footer>
    </Layout>
  );
};
export default Login;
