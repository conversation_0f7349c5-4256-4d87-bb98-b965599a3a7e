// import { editMainAccountPassword } from '@src/api/EditMainAccountPassword';
import { message } from '@tencent/tea-component';
import { useHistory } from 'react-router-dom';
import { EditPasswordForm } from './main-account-components/EditPasswordForm';
import { t } from '@src/utils/i18n';

export interface FormValue {
  oldPassword: string;
  newPassword: string;
}

export const MainAccountPasswordEdit = () => {
  const history = useHistory();

  async function onSubmit(formValue: FormValue) {
    console.log(999);
  }

  function onSuccess() {
    message.success({ content: t('密码修改成功') });
    history.push({ pathname: '/login' });
  }

  return (
    <EditPasswordForm
      onSubmit={onSubmit}
      onSuccess={onSuccess}
    ></EditPasswordForm>
  );
};
