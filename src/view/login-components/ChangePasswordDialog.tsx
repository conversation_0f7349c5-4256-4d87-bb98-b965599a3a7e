import React from 'react';
import { Form, Input, Button, Modal } from '@tencent/tea-component';
import { Form as FinalForm, Field } from 'react-final-form';
import { getStatus } from '@src/utils/form/getStatus';
import { t } from '@src/utils/i18n';
import { app } from '@src/utils/tea/app';
import { useHistory } from 'react-router';
import { changePassword } from '@src/api/changePassword';
import { DialogRef, useDialog } from '@src/utils/react-use/useDialog';

const validatePassword = (value: string) => {
  if (!value || value.length < 10) {
    return t('密码太短');
  }
};

interface DialogProps {
  dialogRef: DialogRef;
}

async function onSubmit(params: { password_old: string; password: string }) {}

export const ChangePasswordDialog = (props: DialogProps) => {
  const { dialogRef } = props;
  const [visible, setShowState, defaultVal] = useDialog<{
    onSuccess: () => void;
  }>(dialogRef);

  const { onSuccess } = defaultVal;

  const history = useHistory();

  async function _handlerSubmit(formValue: {
    password_old: string;
    password: string;
  }) {
    try {
      await changePassword(formValue);
      onSuccess?.();
      app.tips.success(t('修改成功，请重新登录'));
    } catch (error: any) {}
  }

  return (
    <Modal
      visible={visible}
      size="s"
      caption={t('修改密码')}
      onClose={() => setShowState(false)}
    >
      <Modal.Body>
        <FinalForm
          onSubmit={_handlerSubmit}
          initialValuesEqual={() => true}
          initialValues={{
            password_old: '',
            password: '',
          }}
        >
          {({ handleSubmit, validating, submitting }) => {
            return (
              <form onSubmit={handleSubmit}>
                <Form layout="default" style={{ width: '100%' }}>
                  {/* 垃圾谷歌浏览器，自动检测到 username 的 input 框，自动补全。关闭都没用 */}
                  <Field
                    name="password_old"
                    type="password"
                    validateFields={[]}
                    validate={validatePassword}
                  >
                    {({ input, meta }) => (
                      <Form.Item
                        required
                        showStatusIcon={false}
                        label={t('旧密码')}
                        status={getStatus(meta, validating)}
                        message={
                          getStatus(meta, validating) === 'error' && meta.error
                        }
                      >
                        <Input
                          {...(input as any)}
                          placeholder={t('请输入旧密码')}
                          size="full"
                          autoComplete="off"
                          disabled={submitting}
                        />
                      </Form.Item>
                    )}
                  </Field>
                  <Field
                    name="password"
                    type="password"
                    disabled={submitting}
                    validateFields={[]}
                    validate={validatePassword}
                  >
                    {({ input, meta }) => (
                      <Form.Item
                        required
                        showStatusIcon={false}
                        label={t('新密码')}
                        status={getStatus(meta, validating)}
                        message={
                          getStatus(meta, validating) === 'error' && meta.error
                        }
                      >
                        <Input
                          autoComplete="off"
                          {...(input as any)}
                          placeholder={t('请输入新密码')}
                          size="full"
                          disabled={submitting}
                        />
                      </Form.Item>
                    )}
                  </Field>
                </Form>
                <Form.Action style={{ border: 'none', textAlign: 'center' }}>
                  <Button
                    type="primary"
                    htmlType="submit"
                    loading={submitting}
                    style={{ width: '70%' }}
                  >
                    {t('提交')}
                  </Button>
                </Form.Action>
              </form>
            );
          }}
        </FinalForm>
      </Modal.Body>
    </Modal>
  );
};
