import React, { useEffect, useRef, useState } from 'react';
import { Form, Input, Button, InputAdornment } from '@tencent/tea-component';
import { Form as FinalForm, Field } from 'react-final-form';
import { getStatus } from '@src/utils/form/getStatus';
import { t } from '@src/utils/i18n';
import { validateEmail, validateRequired } from '@src/utils/validateFn';
import { trim } from 'lodash';
import style from './login.module.less';
import { useBoolean, useInterval } from 'react-use';
import { app } from '@src/utils/tea/app';
import { getVerifyVCode } from '@src/api/getVerifyVCode';
import _ from 'lodash';

const validateUsername = (value: string) => {
  if (!value || value.length < 2) {
    return t('账号太短');
  }
};

const validatePassword = (value: string) => {
  if (!value || value.length < 10) {
    return t('密码太短');
  }
};

interface LoginProps {
  onSubmit: (formValue: {
    verifycode: string;
    password: string;
    email: string;
  }) => Promise<void>;
  onSuccess?: () => any;
  onError?: (e?: Error) => any;
}

export const LoginForm = (loginProps: LoginProps) => {
  const { onSubmit, onSuccess, onError } = loginProps;
  const [count, setCount] = useState(60);
  const [isRunning, toggleIsRunning] = useBoolean(false);

  useInterval(
    () => {
      setCount(count - 1);
      if (count <= 0) {
        toggleIsRunning(false);
        setCount(60);
      }
    },
    isRunning ? 1000 : null,
  );

  const _handlerSubmit = async (formValue: {
    username: string;
    password: string;
    email: string;
    verifycode: string;
  }) => {
    try {
      await onSubmit({ ...formValue, email: trim(formValue.email) });
      onSuccess?.();
    } catch (error: any) {
      onError?.(error);
    }
  };

  return (
    <FinalForm
      onSubmit={_handlerSubmit}
      initialValuesEqual={() => true}
      initialValues={{
        username: '',
        password: '',
        email: '',
      }}
    >
      {({ handleSubmit, validating, submitting, form }) => {
        return (
          <form onSubmit={handleSubmit}>
            <Form layout="fixed" style={{ width: '100%' }}>
              {/* 垃圾谷歌浏览器，自动检测到 username 的 input 框，自动补全。关闭都没用 */}
              {/* <Field
                name="username"
                validateFields={[]}
                validate={validateUsername}
              >
                {({ input, meta }) => (
                  <Form.Item
                    required
                    showStatusIcon={false}
                    label={t('账号')}
                    status={getStatus(meta, validating)}
                    message={
                      getStatus(meta, validating) === 'error' && meta.error
                    }
                  >
                    <Input
                      {...(input as any)}
                      placeholder={t('请输入账号')}
                      size="full"
                      autoComplete="off"
                      disabled={submitting}
                    />
                  </Form.Item>
                )}
              </Field> */}
              <Field name="email" validateFields={[]} validate={validateEmail}>
                {({ input, meta }) => (
                  <Form.Item
                    required
                    showStatusIcon={false}
                    label={t('邮箱')}
                    status={getStatus(meta, validating)}
                    message={
                      getStatus(meta, validating) === 'error' && meta.error
                    }
                  >
                    <Input
                      {...(input as any)}
                      placeholder={t('请输入邮箱')}
                      size="full"
                      autoComplete="off"
                      disabled={submitting}
                    />
                  </Form.Item>
                )}
              </Field>
              <Field
                name="password"
                type="password"
                disabled={submitting}
                validateFields={[]}
                validate={validatePassword}
              >
                {({ input, meta }) => (
                  <Form.Item
                    required
                    showStatusIcon={false}
                    label={t('密码')}
                    status={getStatus(meta, validating)}
                    message={
                      getStatus(meta, validating) === 'error' && meta.error
                    }
                  >
                    <Input
                      autoComplete="off"
                      {...(input as any)}
                      placeholder={t('请输入密码')}
                      size="full"
                      disabled={submitting}
                    />
                  </Form.Item>
                )}
              </Field>
              <Field
                name="verifycode"
                disabled={submitting}
                validateFields={[]}
                validate={validateRequired}
              >
                {({ input, meta }) => (
                  <>
                    <Form.Item
                      required
                      align="middle"
                      showStatusIcon={false}
                      label={t('验证码')}
                      status={getStatus(meta, validating)}
                      message={
                        getStatus(meta, validating) === 'error' && meta.error
                      }
                    >
                      <InputAdornment
                        after={
                          <Button
                            style={{ width: 130 }}
                            onClick={(e) => {
                              e?.preventDefault();
                              const email = _.trim(
                                form.getFieldState('email')?.value,
                              );
                              const password = _.trim(
                                form.getFieldState('password')?.value,
                              );
                              if (
                                !form.getFieldState('email')?.valid ||
                                !form.getFieldState('password')?.valid
                              ) {
                                return app.tips.error(
                                  t('请先输入正确格式的邮箱和密码'),
                                );
                              }
                              getVerifyVCode({
                                email,
                                password,
                              }).catch((e) => {
                                toggleIsRunning(false);
                              });
                              toggleIsRunning(true);
                            }}
                            disabled={isRunning}
                            type="primary"
                          >
                            {isRunning
                              ? t('重新获取（{{count}}s）', { count })
                              : t('获取验证码')}
                          </Button>
                        }
                      >
                        <Input
                          autoComplete="off"
                          {...(input as any)}
                          placeholder={t('请输入邮件验证码')}
                          size="m"
                          disabled={submitting}
                        />
                      </InputAdornment>
                    </Form.Item>
                  </>
                )}
              </Field>
            </Form>
            <Form.Action style={{ border: 'none', textAlign: 'center' }}>
              <Button
                type="primary"
                htmlType="submit"
                loading={submitting}
                style={{ width: '70%' }}
              >
                {t('登录')}
              </Button>
            </Form.Action>
          </form>
        );
      }}
    </FinalForm>
  );
};
