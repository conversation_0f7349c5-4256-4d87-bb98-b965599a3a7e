import { getStatus } from '@src/utils/form/getStatus';
import { Form, Input, Button } from '@tencent/tea-component';
import { useState } from 'react';
import { Form as FinalForm, Field } from 'react-final-form';
import { FormValue } from '../MainAccountPasswordEdit';
import { t } from '@src/utils/i18n';

const validateNewPassword = (value: string) => {
  if (!value || !value.match(/^[0-9a-zA-Z.#?!@$%^&*-]{8,16}$/)) {
    return t('密码格式错误，必须为8-16位数字字母和.#?!@$%^&*-');
  }
};

const validateConfirmPassword = (value: string, formData: FormValue) => {
  const { newPassword } = formData;
  console.log(formData);
  if (!value || value !== newPassword) {
    return t('两次输入密码不一致');
  }
};

interface Props {
  onSubmit: (formValue: FormValue) => Promise<void>;
  onSuccess?: () => any;
  onError?: () => any;
}

export const EditPasswordForm = (props: Props) => {
  const { onSubmit, onSuccess, onError } = props;

  const _handlerSubmit = async (
    formValue: FormValue & {
      confirmPassword: string;
    },
  ) => {
    const { oldPassword, newPassword } = formValue;
    try {
      await onSubmit({ oldPassword, newPassword });
      onSuccess?.();
    } catch (error: any) {
      onError?.();
    }
  };

  return (
    <FinalForm
      onSubmit={_handlerSubmit}
      initialValuesEqual={() => true}
      initialValues={{
        oldPassword: '',
        newPassword: '',
        confirmPassword: '',
      }}
    >
      {({ handleSubmit, submitting, validating }) => {
        return (
          <form onSubmit={handleSubmit}>
            <Form layout="vertical" style={{ width: '100%' }}>
              <Field
                name="oldPassword"
                type="password"
                disabled={submitting}
                validateFields={[]}
              >
                {({ input }) => (
                  <Form.Item required label={t('旧密码')}>
                    <Input
                      autoComplete="off"
                      {...(input as any)}
                      placeholder={t('请输入旧密码')}
                      size="l"
                      disabled={submitting}
                    />
                  </Form.Item>
                )}
              </Field>
              <Field
                name="newPassword"
                type="password"
                disabled={submitting}
                validateFields={['confirmPassword']}
                validate={validateNewPassword}
              >
                {({ meta, input }) => (
                  <Form.Item
                    required
                    label={t('新密码')}
                    status={getStatus(meta, validating)}
                    message={
                      getStatus(meta, validating) === 'error' && meta.error
                    }
                  >
                    <Input
                      autoComplete="off"
                      {...(input as any)}
                      placeholder={t('请输入新密码')}
                      size="l"
                      disabled={submitting}
                    />
                  </Form.Item>
                )}
              </Field>
              <Field
                name="confirmPassword"
                type="password"
                disabled={submitting}
                validateFields={['newPassword']}
                validate={validateConfirmPassword as any}
              >
                {({ input, meta }) => {
                  return (
                    <Form.Item
                      required
                      label={t('确认新密码')}
                      status={getStatus(meta, validating)}
                      message={
                        getStatus(meta, validating) === 'error' && meta.error
                      }
                    >
                      <Input
                        autoComplete="off"
                        {...(input as any)}
                        placeholder={t('请再次输入新密码')}
                        size="l"
                        disabled={submitting}
                      />
                    </Form.Item>
                  );
                }}
              </Field>
            </Form>
            <Form.Action style={{ border: 'none' }}>
              <Button type="primary" htmlType="submit" loading={submitting}>
                {t('修改密码')}
              </Button>
            </Form.Action>
          </form>
        );
      }}
    </FinalForm>
  );
};
