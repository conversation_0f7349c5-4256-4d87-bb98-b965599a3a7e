import { useMemo, useState } from 'react';
import { getStatus } from '@src/utils/form/getStatus';
import { t } from '@src/utils/i18n';
import {
  Button,
  Table,
  Input,
  Form,
  PopConfirm,
  Select,
  Tag,
  Modal,
  Text,
  Copy,
} from '@tencent/tea-component';
import { Form as FinalForm, Field } from 'react-final-form';
import { pageable } from '@tencent/tea-component/lib/table/addons';
import _ from 'lodash';
import { useSetState } from 'react-use';
import useAsyncRetryFunc from '@src/utils/react-use/useAsyncFunc';
import { useTableTopTip } from '@src/global-components/table-top-tip/useTableTopTip';
import { app } from '@src/utils/tea/app';
import { useDialogRef } from '@src/utils/react-use/useDialog';
import {
  AddReportFailureReason,
  assignReportStatusOptions,
} from './component/AddReportFailureReason';
import { findText, signReportStatus } from '@src/const/const';
import {
  deleteReportFailureReason,
  getReportFailureReason,
} from '@src/apiV2/sign';
import { getHeaderByKey } from './component/const';

type FormType = {
  id?: string;
  name?: string;
};

export const ReportFailureResonManage = () => {
  const addRef = useDialogRef();
  const [searchKeys, setSearchKeys] = useState<any>({});
  const [pagination, setPagnation] = useSetState({
    page_index: 1,
    page_size: 10,
  });

  const {
    value: state,
    loading,
    retry,
  } = useAsyncRetryFunc(async () => {
    const result = await getReportFailureReason({
      ...searchKeys,
      ...pagination,
    });
    return result?.data;
  }, [pagination, searchKeys]);

  const list = useMemo(() => {
    return state?.list ?? [];
  }, [state]);

  const total = useMemo(() => {
    return state?.count ?? 0;
  }, [state]);

  const TableTopTip = useTableTopTip({
    record: list,
    loading: loading,
  });

  const _handlerSubmit = (formValue: FormType) => {
    setSearchKeys({ ...formValue });
    setPagnation({
      page_index: 1,
    });
  };

  async function handleDelete(ids: number[]) {
    const res = await deleteReportFailureReason({ ids: ids });
    if (res.code === 0) {
      app.tips.success(t('删除成功'));
      retry();
    }
  }

  return (
    <>
      <Button
        type="primary"
        style={{ marginBottom: 10 }}
        onClick={() => {
          addRef.current.open({
            initValues: {
              type: '0',
              parent_category: '',
              son_category: '',
              solution: '',
              wrong_keys: [],
            },
            isEdit: false,
            onSuccess: retry,
          });
        }}
      >
        {t('+新增')}
      </Button>
      <Table.ActionPanel>
        <div>
          <FinalForm
            onSubmit={_handlerSubmit}
            initialValuesEqual={(val, oldVal) => {
              return _.isEqual(val, oldVal);
            }}
          >
            {({ handleSubmit, validating, submitting }) => {
              return (
                <form onSubmit={handleSubmit}>
                  <Form layout="inline">
                    <Field name="parent_category" validateFields={[]}>
                      {({ input, meta }) => (
                        <Form.Item
                          showStatusIcon={false}
                          label={t('一级分类')}
                          status={getStatus(meta, validating)}
                          message={
                            getStatus(meta, validating) === 'error' &&
                            meta.error
                          }
                        >
                          <Input
                            {...(input as any)}
                            autoComplete="off"
                            size="s"
                          />
                        </Form.Item>
                      )}
                    </Field>
                    <Field name="son_category" validateFields={[]}>
                      {({ input, meta }) => (
                        <Form.Item
                          showStatusIcon={false}
                          label={t('二级分类')}
                          status={getStatus(meta, validating)}
                          message={
                            getStatus(meta, validating) === 'error' &&
                            meta.error
                          }
                        >
                          <Input
                            {...(input as any)}
                            autoComplete="off"
                            size="s"
                          />
                        </Form.Item>
                      )}
                    </Field>
                    <Field name="report_status" validateFields={[]}>
                      {({ input, meta }) => (
                        <Form.Item
                          showStatusIcon={false}
                          label={t('场景')}
                          status={getStatus(meta, validating)}
                          message={
                            getStatus(meta, validating) === 'error' &&
                            meta.error
                          }
                        >
                          <Select
                            {...(input as any)}
                            size="m"
                            appearance="button"
                            options={assignReportStatusOptions}
                            clearable
                          />
                        </Form.Item>
                      )}
                    </Field>

                    <Button
                      type="primary"
                      htmlType="submit"
                      loading={loading}
                      style={{ marginLeft: 5 }}
                    >
                      {t('查询')}
                    </Button>
                  </Form>
                </form>
              );
            }}
          </FinalForm>
        </div>
      </Table.ActionPanel>
      <Table
        compact
        bordered
        records={list}
        recordKey="id"
        topTip={TableTopTip}
        columns={[
          { key: 'id', header: t('id'), width: 80 },
          {
            key: 'parent_category',
            header: t('一级分类'),
          },
          {
            key: 'son_category',
            header: t('二级分类'),
            render: (row) => <Text>{row?.son_category}</Text>,
          },
          {
            key: 'report_status',
            header: t('场景'),
            render: (row) =>
              _.map(row.report_status?.split(','), (item) =>
                findText(signReportStatus, item),
              )?.join(',') || '-',
          },
          {
            key: 'wrong_keys',
            header: t('对应字段'),
            render: (row) => getHeaderByKey(row.wrong_keys) || '-',
          },
          {
            key: 'solution',
            header: t('解决方案'),
            render: (row) => (
              <>
                <Copy
                  text={row.solution}
                  tips={(copied) => (copied ? t('复制成功') : t('点击复制'))}
                >
                  <Text tooltip overflow>
                    {row.solution || '-'}
                  </Text>
                </Copy>
              </>
            ),
          },
          {
            key: 'operation',
            header: t('操作'),
            width: 120,
            render: (row: any) => {
              return (
                <>
                  <Button
                    type="link"
                    onClick={() => {
                      addRef.current.open({
                        initValues: {
                          ...row,
                          type: '0',
                          wrong_keys: row.wrong_keys?.split(','),
                          report_status: row.report_status
                            ?.split(',')
                            ?.map((el) => +el),
                        },
                        isEdit: true,
                        onSuccess: retry,
                      });
                    }}
                  >
                    {t('编辑')}
                  </Button>
                  <PopConfirm
                    title={t('确定删除？')}
                    footer={(close) => (
                      <>
                        <Button
                          type="link"
                          onClick={() => {
                            close();
                            handleDelete([row.id]);
                          }}
                        >
                          {t('确定')}
                        </Button>
                        <Button
                          type="text"
                          onClick={() => {
                            close();
                          }}
                        >
                          {t('取消')}
                        </Button>
                      </>
                    )}
                    placement="top-start"
                  >
                    <Button type="link">{t('删除')}</Button>
                  </PopConfirm>
                </>
              );
            },
          },
        ]}
        addons={[
          pageable({
            recordCount: total,
            pageIndex: pagination.page_index,
            pageSize: pagination.page_size,
            onPagingChange: (query) =>
              setPagnation({
                page_index: query.pageIndex,
                page_size: query.pageSize,
              }),
          }),
        ]}
      />
      <AddReportFailureReason dialogRef={addRef} />
      {/* <EditReportFailureReason dialogRef={editUserRef} /> */}
    </>
  );
};
export default ReportFailureResonManage;
