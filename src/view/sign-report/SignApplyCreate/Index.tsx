import React, { useEffect, useRef, useState } from 'react';
import { useHistory } from 'react-router-dom';
import SignApplyCreate from './SignApplyCreate';
import SignApplyEdit from './SignApplyEdit';

export default function SignApplyCreateIndex() {
  const history = useHistory();
  const isCreate = window.location.pathname.includes('sign/apply/create');

  return isCreate ? (
    <SignApplyCreate></SignApplyCreate>
  ) : (
    <SignApplyEdit></SignApplyEdit>
  );
}
