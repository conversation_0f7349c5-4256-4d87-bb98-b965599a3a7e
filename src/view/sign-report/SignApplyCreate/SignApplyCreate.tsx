import { addSignApply } from '@src/apiV2/sign';
import { SignApplyForm } from './SignApplyForm';
import { t } from '@src/utils/i18n';
import { message } from '@tea/component';
import { useHistory } from 'react-router-dom';
import {
  signApplyInitValues,
  SIGN_TYPE_APP,
  SIGN_TYPE_TRADEMARK,
  SIGN_REPORT_APPLY_SOURCE_INNER,
} from '@src/const/const';
import _ from 'lodash';
import { isURL } from '@src/utils/tools';

export default function SignApplyCreate() {
  const history = useHistory();
  const state: { row?: any } = history.location.state as any;

  const onSubmit = async (values) => {
    const isSignTypeValid =
      values.remark_type === SIGN_TYPE_APP ||
      values.remark_type === SIGN_TYPE_TRADEMARK;
    const v = {
      ..._.pickBy(values, (v) => v !== '' && !_.isNil(v)),
      registration_url: isSignTypeValid ? values.registration_url : undefined,
      registration_num: isSignTypeValid ? values.registration_num : undefined,
      app_resource_url:
        values.remark_type === SIGN_TYPE_APP
          ? values.app_resource_url
          : undefined,
      attach_url: isSignTypeValid ? values.attach_url : undefined,
      source: SIGN_REPORT_APPLY_SOURCE_INNER,
    };
    const params = _.mapValues(v, (s) => {
      if (typeof s === 'string') {
        return s.trim();
      } else {
        return s;
      }
    });
    const _params = _.mapValues(params, (v: any) => {
      if (isURL(v)) {
        return decodeURIComponent(new URL(v)?.pathname.slice(1) ?? '');
      } else {
        return v;
      }
    });
    const res = await addSignApply({
      params: [_params],
    });
    if (res.code === 0 && !res?.data?.errors?.length) {
      message.success({ content: t('提交成功') });
      history.push('/sign/apply');
    }
  };

  const _initValues = _.pick(state?.row, _.keys(signApplyInitValues));

  return (
    <div>
      <SignApplyForm
        onSubmit={onSubmit}
        onSuccess={() => {}}
        onError={() => {}}
        onCancel={() => {}}
        initValues={_initValues}
      ></SignApplyForm>
    </div>
  );
}
