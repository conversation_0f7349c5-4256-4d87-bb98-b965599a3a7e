import { editSignApply } from '@src/apiV2/sign';
import { SignApplyForm } from './SignApplyForm';
import { t } from '@src/utils/i18n';
import { message } from '@tea/component';
import { useHistory } from 'react-router-dom';
import _ from 'lodash';
import { signApplyInitValues } from '@src/const/const';
import { isURL } from '@src/utils/tools';

export default function SignApplyEdit() {
  const history = useHistory();
  const { row = {} } = (history.location.state as any) ?? { row: {} };

  const onSubmit = async (values) => {
    const params = { ...values, apply_id: row.apply_id };
    const _params = _.mapValues(params, (v) => {
      if (isURL(v)) {
        return decodeURIComponent(new URL(v)?.pathname.slice(1) ?? '');
      } else {
        return v;
      }
    });
    // @ts-ignore
    const res = await editSignApply({ ..._params });
    if (res.code === 0) {
      message.success({ content: t('提交成功') });
      history.push('/sign/apply');
    }
  };

  const _initValues = _.mapValues(
    _.pick(row, _.keys(signApplyInitValues)),
    (v) => v ?? '',
  );

  return (
    <div>
      <SignApplyForm
        onSubmit={onSubmit}
        onSuccess={() => {}}
        onError={() => {}}
        onCancel={() => {}}
        initValues={_initValues}
      ></SignApplyForm>
    </div>
  );
}
