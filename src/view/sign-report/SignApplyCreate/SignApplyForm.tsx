// @i18n-noscan
import React from 'react';
import { t } from '@tea/app/i18n';
import {
  Bubble,
  Button,
  Form,
  Input,
  List,
  Select,
  TextArea,
} from '@tencent/tea-component';
import { useField, useForm } from 'react-final-form-hooks';
import _ from 'lodash';
import { useHistory } from 'react-router-dom';
import { validateChineseName, validateIdCard } from '@src/utils/validateFn';
import { getFormStatusAndMessage } from '@src/utils/form/getStatus';
import {
  idCardType,
  remarkTypeOptions,
  SIGN_TYPE_APP,
  SIGN_TYPE_TRADEMARK,
  smsTypeOptions,
} from '@src/const/const';
import ImageUpload from '@src/global-components/file-upload/ImageUpload';
import { SignApplyFormField } from '../type';
import { app } from '@src/utils/tea/app';
import MutipleFileUpload from '@src/global-components/file-upload/MutipleFileUpload';

const isInvalid = (field, error) => {
  return !field || field.length === 0 ? error : undefined;
};

const _validateIdCard = (field, error) => {
  if (!field) {
    return isInvalid(field, error);
  }
  return validateIdCard(field);
};

const _validateChineseName = (field, error) => {
  if (!field) {
    return isInvalid(field, error);
  }
  return validateChineseName(field);
};

export const asyncValidatePhone = async (field, error) => {};

interface EnterpriseFormProps {
  onSubmit: (values: SignApplyFormField) => Promise<any>;
  onSuccess: (data) => void;
  onError: (errors, values: SignApplyFormField) => void;
  onCancel: () => void;
  needUpload?: boolean;
  initValues: Partial<SignApplyFormField> & {
    HasPassSign?: 0 | 1;
  };
  isEdit?: boolean;
  reply?: string;
}

export const SignApplyForm = ({
  onSuccess,
  onError,
  onSubmit,
  onCancel,
  initValues,
  reply,
}: EnterpriseFormProps) => {
  const history = useHistory();

  const validate = (values) => {
    const {
      // uin,
      qappid,
      sms_type,
      remark_type,
      sign,
      company_name,
      unisc_id,
      corp_name,
      corp_cr_num,
      corp_idcard_url,
      corp_idcard_back_url,
      transactor_name,
      transactor_cr_num,
      transactor_idcard_url,
      transactor_idcard_back_url,
      commitment_letter_url,
      authorization_letter_url,
      other_attach_url,
      company_file_url,
      transactor_phone,
      attach_url,
      app_resource_url,
      registration_num,
      registration_url,
    } = values;
    const validateJson = {
      // uin: isInvalid(uin, t('腾讯云UIN不能为空')),
      qappid: isInvalid(qappid, t('腾讯云APPID不能为空')),
      sms_type: isInvalid(sms_type, t('短信类型不能为空')),
      remark_type: isInvalid(remark_type, t('签名类型不能为空')),
      sign: isInvalid(sign, t('签名内容不能为空')),
      company_name: isInvalid(company_name, t('企业名称不能为空')),
      unisc_id: isInvalid(unisc_id, t('统一社会信用代码不能为空')),
      corp_name: isInvalid(corp_name, t('企业法人姓名不能为空')),
      corp_cr_num: _validateIdCard(corp_cr_num, t('证件号码格式错误')),
      // corp_idcard_url: isInvalid(
      //   corp_idcard_url,
      //   t('企业法人身份证正面不能为空'),
      // ),
      // corp_idcard_back_url: isInvalid(
      //   corp_idcard_back_url,
      //   t('企业法人身份证反面不能为空'),
      // ),
      transactor_name: _validateChineseName(
        transactor_name,
        t('经办人名称不能为空'),
      ),
      // transactor_phone: isInvalid(transactor_phone, t('经办人电话不能为空')),
      // attach_url:
      //   values.remark_type === SIGN_TYPE_APP ||
      //   values.remark_type === SIGN_TYPE_TRADEMARK
      //     ? isInvalid(attach_url, t('必填项'))
      //     : undefined,
      // registration_url:
      //   values.remark_type === SIGN_TYPE_APP ||
      //   values.remark_type === SIGN_TYPE_TRADEMARK
      //     ? isInvalid(registration_url, t('必填项'))
      //     : undefined,
      registration_num:
        values.remark_type === SIGN_TYPE_APP ||
        values.remark_type === SIGN_TYPE_TRADEMARK
          ? isInvalid(registration_num, t('必填项'))
          : undefined,
      app_resource_url:
        values.remark_type === SIGN_TYPE_APP
          ? isInvalid(app_resource_url, t('必填项'))
          : undefined,

      // commitment_letter_url: isInvalid(
      //   commitment_letter_url,
      //   t('安全合规承诺书不能为空'),
      // ),
      transactor_cr_num: _validateIdCard(
        transactor_cr_num,
        t('证件号码格式错误'),
      ),
      // transactor_idcard_url: isInvalid(
      //   transactor_idcard_url,
      //   t('经办人证件正面照不能为空'),
      // ),
      // transactor_idcard_back_url: isInvalid(
      //   transactor_idcard_back_url,
      //   t('经办人证件反面照不能为空'),
      // ),
      // authorization_letter_url: isInvalid(
      //   authorization_letter_url,
      //   t('授权书不能为空'),
      // ),
      // company_file_url: isInvalid(company_file_url, t('企业资质不能为空')),
    };
    return validateJson;
  };

  const _handleSubmit = async (values) => {
    try {
      const params = { ...values };
      const data = await onSubmit(params);
      if (!data) {
        return;
      }
      onSuccess({ result: data, params });
    } catch (err) {
      console.log(err);
      onError?.(err, values);
    }
  };

  const { form, handleSubmit, submitting, values } = useForm({
    onSubmit: _handleSubmit,
    validate,
    initialValues: {
      ...initValues,
      corp_cr_type: `${initValues.corp_cr_type}`,
      transactor_cr_type: `${initValues.transactor_cr_type}`,
    },
    // initialValues: {
    //   uin: 100000043512,
    //   qappid: *********,
    //   // sign_id: 976293,
    //   sign: t('新签名测试'),
    //   sms_type: '1',
    //   remark_type: t('公司名'),
    //   company_name: t('北京小浩科技有限公司'),
    //   unisc_id: '*********',
    //   corp_name: t('王召'),
    //   corp_cr_num: '630104197709028899',
    //   transactor_name: t('王召'),
    //   transactor_cr_num: '63010419770902203X',
    //   transactor_phone: '15222222222',
    //   commitment_letter_url: '/*********/2024/09/07.10641912.81765237.png',
    //   other_attach_url: '',
    //   transactor_idcard_url: '/*********/2024/09/07.10641912.81765237.png',
    //   transactor_idcard_back_url: '/*********/2024/09/07.10641912.81765237.png',
    //   transactor_photo_idcard_url:
    //     '/*********/2024/09/07.10641912.81765237.png',
    //   corp_idcard_url: '/*********/2024/09/07.10641912.81765237.png',
    //   corp_idcard_back_url: '/*********/2024/09/07.10641912.81765237.png',
    //   company_file_url: '/*********/2024/09/07.10641912.81765237.png',
    //   authorization_letter_url: '',
    //   registration_num: '4345',
    //   registration_url: '453',
    //   app_resource_url: '43543',
    //   attach_url: '8989',
    //   remark: null,
    // },
  });

  // 签名信息、账号信息
  const UinField = useField('uin', form);
  const QappIdField = useField('qappid', form);
  const SignField = useField('sign', form);
  const SmsTypeField = useField('sms_type', form);

  const RemarkTypeField = useField('remark_type', form);

  const RegistrationNumField = useField('registration_num', form); // ICP备案截图/商标备案截图（app、商标）
  const RegistrationUrlField = useField('registration_url', form); //  ICP备案或许可证号/商标号（app、商标）
  const AppResourceUrlField = useField('app_resource_url', form); // 应用商店下载页链接（app）
  const AttachUrlField = useField('attach_url', form); // 应用商店下载页截图、商标注册书 (app、商标)

  /* 企业信息 */
  const CompanyNameField = useField('company_name', form);
  const CompanyFileUrlField = useField('company_file_url', form);
  const UniscIdField = useField('unisc_id', form);

  /* 法人信息 */
  const CorpCrTypeField = useField('corp_cr_type', form);
  const CorporationNameField = useField('corp_name', form);
  const CorporationCredentialsNumberField = useField('corp_cr_num', form);
  const CorporationIDCardUrlField = useField('corp_idcard_url', form);
  const CorporationIDCardBackUrlField = useField('corp_idcard_back_url', form);

  /* 短信业务管理员信息 */
  const TransactorCrTypeField = useField('transactor_cr_type', form);
  const TransactorNameField = useField('transactor_name', form);
  const TransactorCredentialsNumberField = useField('transactor_cr_num', form);
  const TransactorPhoneField = useField('transactor_phone', form);
  const TransactorIDCardUrlField = useField('transactor_idcard_url', form);
  const TransactorIDCardBackUrlField = useField(
    'transactor_idcard_back_url',
    form,
  );
  const TransactorPhotoIDCardUrlField = useField(
    'transactor_photo_idcard_url',
    form,
  );

  /* 承诺书、其他附件、授权书*/
  const CommitmentLetterUrlField = useField('commitment_letter_url', form);
  const OtherAttachUrlField = useField('other_attach_url', form);
  const AuthorizationLetterUrlField = useField(
    'authorization_letter_url',
    form,
  );

  // 备注
  const RemarkField = useField('remark', form);

  function onUploadResult(remotePath) {
    try {
      // 提取文件名并解码
      const getFilenameFromPath = (path) => {
        try {
          const pathname = new URL(path)?.pathname?.slice(1) || '';
          const lastSegment = pathname.split('/')?.pop() || '';
          const filenameWithoutExt = lastSegment.split('.')?.shift() || '';
          return decodeURIComponent(decodeURIComponent(filenameWithoutExt));
        } catch {
          return '';
        }
      };

      const filename = getFilenameFromPath(remotePath);
      if (!filename) return;

      const { company_name, sign, remark_type, transactor_name, corp_name } =
        values || {};

      const isSignTypeValid = [SIGN_TYPE_APP, SIGN_TYPE_TRADEMARK].includes(
        remark_type,
      );

      // 定义文件名与字段的映射关系
      const fieldMappings = [
        {
          condition: filename === company_name,
          field: 'company_file_url',
        },
        {
          condition: filename === sign && isSignTypeValid,
          field: 'registration_url',
        },
        {
          condition: filename === transactor_name,
          field: 'transactor_idcard_url',
        },
        {
          condition: filename === corp_name,
          field: 'corp_idcard_url',
        },
      ];

      // 处理以数字结尾的特殊情况
      const lastChar = filename.slice(-1);
      const baseName = filename.slice(0, -1);

      if (lastChar === '2') {
        fieldMappings.push(
          {
            condition: baseName === corp_name,
            field: 'corp_idcard_back_url',
          },
          {
            condition: baseName === transactor_name,
            field: 'transactor_idcard_back_url',
          },
          {
            condition: baseName === sign && isSignTypeValid,
            field: 'attach_url',
          },
        );
      } else if (lastChar === '3') {
        fieldMappings.push({
          condition: true,
          field: 'transactor_photo_idcard_url',
        });
      }

      // 找到第一个匹配的条件并更新表单字段
      const matchedMapping = fieldMappings.find((mapping) => mapping.condition);
      if (matchedMapping) {
        form.change(matchedMapping.field, remotePath);
      }
    } catch (err) {
      app.tips.error(t('获取cos链接失败'));
    }
  }

  return (
    <>
      <form onSubmit={handleSubmit}>
        <Form layout="inline-vertical" fixedLabelWidth={110}>
          <Form.Title>{t('签名信息')}</Form.Title>
          {/* <Form.Item
            required
            label={t('腾讯云UIN')}
            showStatusIcon={false}
            {...getFormStatusAndMessage(CompanyFileUrlField.meta, false)}
          >
            <Input {...UinField.input} />
          </Form.Item> */}
          <Form.Item
            required
            label={t('腾讯云APPID')}
            showStatusIcon={false}
            {...getFormStatusAndMessage(QappIdField.meta, false)}
          >
            <Input {...QappIdField.input} />
          </Form.Item>
          <Form.Item
            required
            label={t('短信类型')}
            showStatusIcon={false}
            {...getFormStatusAndMessage(SmsTypeField.meta, false)}
          >
            <Select
              {...SmsTypeField.input}
              options={smsTypeOptions}
              appearance="button"
              style={{ width: 200 }}
            />
          </Form.Item>
          <Form.Item
            required
            label={t('签名类型')}
            showStatusIcon={false}
            {...getFormStatusAndMessage(RemarkTypeField.meta, false)}
          >
            <Select
              {...RemarkTypeField.input}
              options={remarkTypeOptions}
              appearance="button"
              style={{ width: 200 }}
            />
          </Form.Item>

          <Form.Item
            required
            label={t('签名')}
            showStatusIcon={false}
            {...getFormStatusAndMessage(SignField.meta, false)}
          >
            <Input {...SignField.input} />
          </Form.Item>

          {values.remark_type === SIGN_TYPE_APP ||
          values.remark_type === SIGN_TYPE_TRADEMARK ? (
            <>
              <Form.Item
                required
                label={t(' ICP备案或许可证号/商标号')}
                showStatusIcon={false}
                {...getFormStatusAndMessage(RegistrationNumField.meta, false)}
              >
                <Input {...RegistrationNumField.input} />
              </Form.Item>
              <Form.Item
                label={t('ICP备案截图/商标备案截图')}
                showStatusIcon={false}
                {...getFormStatusAndMessage(RegistrationUrlField.meta, false)}
              >
                <ImageUpload
                  {...RegistrationUrlField.input}
                  imagUrl={values.registration_url}
                  onUploadResult={(url) => {
                    RegistrationUrlField.input.onChange(url);
                  }}
                />
              </Form.Item>
              <Form.Item
                label={
                  values.remark_type === SIGN_TYPE_APP
                    ? t('应用商店下载页截图')
                    : t('商标注册书')
                }
                showStatusIcon={false}
                {...getFormStatusAndMessage(AttachUrlField.meta, false)}
              >
                <ImageUpload
                  {...AttachUrlField.input}
                  imagUrl={values.attach_url}
                  onUploadResult={(url) => {
                    AttachUrlField.input.onChange(url);
                  }}
                />
              </Form.Item>
            </>
          ) : null}

          {values.remark_type === SIGN_TYPE_APP ? (
            <Form.Item
              required
              label={t('应用商店下载页链接')}
              showStatusIcon={false}
              {...getFormStatusAndMessage(AppResourceUrlField.meta, false)}
            >
              <Input {...AppResourceUrlField.input} />
            </Form.Item>
          ) : null}

          <Form.Title>{t('企业信息')}</Form.Title>
          <Form.Item
            required
            label={t('公司名称')}
            showStatusIcon={false}
            {...getFormStatusAndMessage(CompanyNameField.meta, false)}
          >
            <Input {...CompanyNameField.input} />
          </Form.Item>
          <Form.Item
            required
            label={t('社会信用代码')}
            showStatusIcon={false}
            {...getFormStatusAndMessage(UniscIdField.meta, false)}
          >
            <Input {...UniscIdField.input} />
          </Form.Item>
          <Form.Item
            label={t('企业资质证明')}
            showStatusIcon={false}
            {...getFormStatusAndMessage(CompanyFileUrlField.meta, false)}
          >
            <ImageUpload
              {...CompanyFileUrlField.input}
              imagUrl={values.company_file_url}
              onUploadResult={(url) => {
                CompanyFileUrlField.input.onChange(url);
              }}
            />
          </Form.Item>
          <Form.Title>{t('法人信息')}</Form.Title>
          <Form.Item
            required
            label={t('企业法人证件类型')}
            showStatusIcon={false}
            {...getFormStatusAndMessage(CorpCrTypeField.meta, false)}
          >
            <Select
              {...CorpCrTypeField.input}
              options={idCardType}
              appearance="button"
              size="m"
            />
          </Form.Item>
          <Form.Item
            required
            label={t('企业法人姓名')}
            showStatusIcon={false}
            {...getFormStatusAndMessage(CorporationNameField.meta, false)}
          >
            <Input {...CorporationNameField.input} />
          </Form.Item>
          <Form.Item
            required
            label={t('企业法人身份证号码')}
            showStatusIcon={false}
            {...getFormStatusAndMessage(
              CorporationCredentialsNumberField.meta,
              false,
            )}
          >
            <Input {...CorporationCredentialsNumberField.input} />
          </Form.Item>
          <Form.Item
            label={t('企业法人身份证正面')}
            showStatusIcon={false}
            {...getFormStatusAndMessage(CorporationIDCardUrlField.meta, false)}
          >
            <ImageUpload
              {...CorporationIDCardUrlField.input}
              imagUrl={values.corp_idcard_url}
              onUploadResult={(url) => {
                CorporationIDCardUrlField.input.onChange(url);
              }}
            />
          </Form.Item>
          <Form.Item
            label={t('企业法人身份证反面')}
            showStatusIcon={false}
            {...getFormStatusAndMessage(
              CorporationIDCardBackUrlField.meta,
              false,
            )}
          >
            <ImageUpload
              {...CorporationIDCardBackUrlField.input}
              imagUrl={values.corp_idcard_back_url}
              onUploadResult={(url) => {
                CorporationIDCardBackUrlField.input.onChange(url);
              }}
            />
          </Form.Item>
          <Form.Title>{t('经办人信息')}</Form.Title>
          <Form.Item
            required
            label={t('经办人证件类型')}
            showStatusIcon={false}
            {...getFormStatusAndMessage(TransactorCrTypeField.meta, false)}
          >
            <Select
              {...TransactorCrTypeField.input}
              options={idCardType}
              appearance="button"
              size="m"
            />
          </Form.Item>
          <Form.Item
            required
            label={t('经办人姓名')}
            showStatusIcon={false}
            {...getFormStatusAndMessage(TransactorNameField.meta, false)}
          >
            <Input {...TransactorNameField.input} />
          </Form.Item>
          <Form.Item
            required
            label={t('经办人身份证号码')}
            showStatusIcon={false}
            {...getFormStatusAndMessage(
              TransactorCredentialsNumberField.meta,
              false,
            )}
          >
            <Input {...TransactorCredentialsNumberField.input} />
          </Form.Item>
          <Form.Item
            label={t('经办人手机号')}
            showStatusIcon={false}
            {...getFormStatusAndMessage(TransactorPhoneField.meta, false)}
          >
            <Input {...TransactorPhoneField.input} />
          </Form.Item>
          <Form.Item
            label={t('经办人身份证正面')}
            showStatusIcon={false}
            {...getFormStatusAndMessage(TransactorIDCardUrlField.meta, false)}
          >
            <ImageUpload
              {...TransactorIDCardUrlField.input}
              imagUrl={values.transactor_idcard_url}
              onUploadResult={(url) => {
                TransactorIDCardUrlField.input.onChange(url);
              }}
            />
          </Form.Item>
          <Form.Item
            label={t('经办人身份证反面')}
            showStatusIcon={false}
            {...getFormStatusAndMessage(
              TransactorIDCardBackUrlField.meta,
              false,
            )}
          >
            <ImageUpload
              {...TransactorIDCardBackUrlField.input}
              imagUrl={values.transactor_idcard_back_url}
              onUploadResult={(url) => {
                TransactorIDCardBackUrlField.input.onChange(url);
              }}
            />
          </Form.Item>
          <Form.Item
            label={t('经办人手持身份证照片')}
            showStatusIcon={false}
            {...getFormStatusAndMessage(
              TransactorPhotoIDCardUrlField.meta,
              false,
            )}
          >
            <ImageUpload
              {...TransactorPhotoIDCardUrlField.input}
              imagUrl={values.transactor_photo_idcard_url}
              onUploadResult={(url) => {
                TransactorPhotoIDCardUrlField.input.onChange(url);
              }}
            />
          </Form.Item>
          <Form.Title>{t('承诺书、其他附件、授权书')}</Form.Title>
          <Form.Item
            label={t('合规承诺书')}
            showStatusIcon={false}
            {...getFormStatusAndMessage(CommitmentLetterUrlField.meta, false)}
          >
            <ImageUpload
              {...CommitmentLetterUrlField.input}
              imagUrl={values.commitment_letter_url}
              onUploadResult={(url) => {
                CommitmentLetterUrlField.input.onChange(url);
              }}
            />
          </Form.Item>
          <Form.Item
            label={t('授权书')}
            showStatusIcon={false}
            {...getFormStatusAndMessage(
              AuthorizationLetterUrlField.meta,
              false,
            )}
          >
            <ImageUpload
              {...AuthorizationLetterUrlField.input}
              imagUrl={values.authorization_letter_url}
              onUploadResult={(url) => {
                AuthorizationLetterUrlField.input.onChange(url);
              }}
            />
          </Form.Item>
          <Form.Item
            label={t('其他附件')}
            showStatusIcon={false}
            {...getFormStatusAndMessage(OtherAttachUrlField.meta, false)}
          >
            <ImageUpload
              {...OtherAttachUrlField.input}
              imagUrl={values.other_attach_url}
              onUploadResult={(url) => {
                OtherAttachUrlField.input.onChange(url);
              }}
            />
          </Form.Item>

          <br />
          <Form.Item
            label={t('备注')}
            showStatusIcon={false}
            {...getFormStatusAndMessage(RemarkField.meta, false)}
          >
            <TextArea {...RemarkField.input} rows={4} />
          </Form.Item>
          <MutipleFileUpload
            onUploadResult={onUploadResult}
            isSupportDragging={false}
            multiple
            needCosUrl={true}
            message={
              <List type="bullet">
                <List.Item>
                  <Button type="link">
                    <Bubble
                      content={
                        <p>
                          <p>{t('营业执照 - 公司名.jpg')}</p>
                          <p>{t('ICP备案截图/商标备案截图 - 签名内容.jpg')}</p>
                          <p>{t('应用商店截图 - 签名内容2.jpg')}</p>
                          <p>{t('商标局截图 - 签名内容.jpg')}</p>
                          <p>{t('商标注册书截图 - 签名内容2.jpg')}</p>
                          <p>{t('法人人像 - 姓名.jpg')}</p>
                          <p>{t('法人国徽 - 姓名2.jpg')}</p>
                          <p>{t('经办人人像 - 姓名.jpg')}</p>
                          <p>{t('经办人国徽 - 姓名2.jpg')}</p>
                          <p>{t('经办人手持 - 姓名3.jpg')}</p>
                        </p>
                      }
                    >
                      {t('附件命名格式要求')}
                    </Bubble>
                  </Button>
                </List.Item>
                <List.Item>
                  {t('请上传 png、jpg、jpeg 格式文件，大小 5MB 以内')}
                </List.Item>
              </List>
            }
          ></MutipleFileUpload>

          <Form.Action>
            <Button type="primary" htmlType="submit" loading={submitting}>
              {t('提交审核')}
            </Button>
            <Button
              htmlType="button"
              type="weak"
              disabled={submitting}
              onClick={() => {
                history.push('/sign/apply');
              }}
            >
              {t('取消')}
            </Button>
          </Form.Action>
        </Form>
      </form>
    </>
  );
};
