import { message, Button } from '@tencent/tea-component';
import { t } from '@src/utils/i18n';
import { useHistory } from 'react-router-dom';
import ScrollLoadMore from '@src/global-components/ScrollLoadMore';
import { FieldConfig, ProForm } from '@tencent/tea-material-pro-form';
import { useMemo, useRef, useState } from 'react';
import {
  AddSignAdditionalReportByCustomer,
  AddSignAdditionalReportByProvider,
  getProviderList,
} from '@src/apiV2/sign';
import { smsTypeOptions } from '@src/const/const';
import { useQuery } from '@src/utils/react-use/useQuery';
import { validateNumber } from '@src/utils/validateFn';
import _ from 'lodash';

const ProviderListScrollLoadMore = (props) => {
  const { value, onChange } = props;
  return (
    <ScrollLoadMore
      value={value}
      onChange={onChange}
      loadFn={async (params) => {
        const res = await getProviderList({
          ..._.omit(params, ['search_key']),
          provider_ids: params.search_key ? [params.search_key] : undefined,
        });
        return {
          options:
            res.data?.list?.map((el) => ({
              value: el.provider_id,
              text: `${el.provider_name}(${el.provider_id})`,
            })) ?? [],
          total: res?.data?.count || 0,
        };
      }}
    />
  );
};

export const SignApplyMakeUpCreate = (props) => {
  const history = useHistory();
  const searchParams = useQuery();
  const { type } = searchParams;
  const formRef = useRef<any>();
  const [submitting, setSubmitting] = useState(false);

  // @ts-ignore
  const customerTypeFields: FieldConfig<any>[] = useMemo(() => {
    return [
      // {
      //   type: 'string',
      //   name: 'uin',
      //   title: t('uin'),
      //   required: true,
      //   validator: (values) => validateNumber(values.uin),
      // },
      {
        type: 'string',
        name: 'qappid',
        title: t('qappid'),
        required: true,
        validator: (values) => validateNumber(values.qappid),
      },
      {
        type: 'string',
        name: 'provider_id',
        title: t('调度id'),
        required: true,
        component: ProviderListScrollLoadMore,
      },
      {
        type: 'string',
        name: 'remark',
        title: t('备注'),
      },
    ];
  }, []);

  const providerTypeFields: FieldConfig<any>[] = useMemo(() => {
    return [
      {
        type: 'string',
        name: 'provider_id',
        title: t('调度id'),
        required: true,
        component: ProviderListScrollLoadMore,
      },
      {
        type: 'string',
        name: 'peer_provider_id',
        title: t('对等调度id'),
        required: true,
        component: ProviderListScrollLoadMore,
      },
      {
        type: 'string',
        name: 'sms_type',
        title: t('短信类型'),
        component: 'select',
        appearance: 'button',
        size: 'm',
        options: smsTypeOptions,
        required: true,
      },
      {
        type: 'string',
        name: 'remark',
        title: t('备注'),
      },
    ];
  }, []);

  const fields = useMemo(() => {
    return type === 'customer' ? customerTypeFields : providerTypeFields;
  }, [customerTypeFields, providerTypeFields, type]);

  async function onSubmit(values) {
    setSubmitting(true);
    const fn =
      type === 'customer'
        ? AddSignAdditionalReportByCustomer
        : AddSignAdditionalReportByProvider;
    try {
      const res = await fn({
        params: [{ ...values }],
      });
      if (res.code === 0 && !res.data?.errors?.length) {
        message.success({ content: t('新增成功') });
        history.push('/sign/additional-report');
      }
    } catch (err) {
      console.log(err);
    }
    setSubmitting(false);
  }

  return (
    <ProForm
      fields={fields}
      onFinish={(values) => onSubmit(values)}
      submitter={{
        render: (form) => {
          return (
            <>
              <Button
                type="primary"
                htmlType="submit"
                loading={submitting}
                onClick={() => {
                  form.submit();
                }}
              >
                {t('提交')}
              </Button>
              <Button onClick={() => formRef.current.reset()}>
                {t('重置')}
              </Button>
              <Button onClick={(e) => history.push('/sign/additional-report')}>
                {t('取消')}
              </Button>
            </>
          );
        },
      }}
      onRef={(form) => (formRef.current = form)}
    />
  );
};
export default SignApplyMakeUpCreate;
