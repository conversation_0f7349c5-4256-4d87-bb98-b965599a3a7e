import React, { useState, useMemo, useRef, useEffect } from 'react';
import {
  Button,
  Text,
  TableColumn,
  message,
  Tabs,
  Tab<PERSON>anel,
  Alert,
} from '@tencent/tea-component';
import { t } from '@src/utils/i18n';
import { useHistory } from 'react-router-dom';
import { useDialogRef } from '@src/utils/react-use/useDialog';
import _ from 'lodash';
import {
  findText,
  getStatusText,
  SIGN_ADDITIONAL_REPORT_STATUS_REPORT_FAIL,
  SIGN_ADDITIONAL_REPORT_STATUS_REPORT_SUCC,
  SIGN_ADDITIONAL_REPORT_STATUS_REPORTING,
  SIGN_ADDITIONAL_REPORT_TYPE_CUSTOMER,
  SIGN_ADDITIONAL_REPORT_TYPE_PROVIDER,
  signAdditionalReportStatus,
} from '@src/const/const';
import { MakeUpSignDialog } from './component/MakeUpSignApplyDialog';
import { SignReportList } from './component/ReportList';
import { ActionType, ProTable } from '@tencent/tea-material-pro-table';
import { AuditOperateDialog } from '@src/global-components/AuditOperateDialog';
import { PermissionShowController } from '@src/PermissionShowController';
import {
  ChangeSignAdditionalReportStatus,
  getProviderList,
  getSignAdditionalReport,
} from '@src/apiV2/sign';
import { smsTypeOptions } from '@src/const/const';
import { useAsyncRetry } from 'react-use';

export const SignApplyMakeUpMange = (props) => {
  const history = useHistory();
  const dialogRef = useDialogRef();
  const actionRef = useRef<ActionType>();
  const [additionalType, setAdditionalType] = useState('customer'); // provider 对等账号, customer 客户

  const [expandedKeys, setExpandedKeys] = useState<string[]>([]);
  const auditDialog = useDialogRef();

  const { value: providerList } = useAsyncRetry(async () => {
    const { data } = await getProviderList({
      page_index: 1,
      page_size: 10000,
    });
    return data?.list;
  }, []);

  const columns: TableColumn<any>[] = useMemo(() => {
    const byProvider = [
      {
        header: t('账号名称'),
        key: 'provider_name',
        size: 's',
      },
      {
        header: t('对等账号名称'),
        key: 'peer_provider_id',
        render: (row) =>
          _.find(providerList, (el) => el.provider_id === row.peer_provider_id)
            ?.provider_name,
      },
      {
        header: t('短信类型'),
        key: 'sms_type',
        render: (row) => findText(smsTypeOptions, row.sms_type),
      },
    ];
    const byCustomer = [
      {
        header: t('uin'),
        key: 'uin',
      },
      {
        header: t('qappid'),
        key: 'qappid',
      },
      {
        header: t('账号名称'),
        key: 'provider_name',
        size: 's',
      },
    ];
    const cols = additionalType === 'provider' ? byProvider : byCustomer;
    return [
      {
        header: t('补报备单ID'),
        key: 'additional_id',
        size: 's',
      },
      ...cols,
      {
        header: t('提单人'),
        key: 'create_rtx',
        hideForm: true,
      },
      {
        header: t('状态'),
        key: 'status',
        component: 'select',
        appearance: 'button',
        size: 's',
        options: signAdditionalReportStatus,
        clearable: true,
        render: (row) => getStatusText(signAdditionalReportStatus, row.status),
      },
      {
        header: t('操作'),
        key: 'operation',
        width: 180,
        hideForm: true,
        fixed: 'right',
        render: (row, index, i) => {
          return (
            <>
              <PermissionShowController
                need={[
                  '/sign/additional-report/add-by-provider',
                  '/sign/additional-report/add-by-provider',
                ]}
              >
                <Button
                  type="link"
                  onClick={() => {
                    dialogRef.current.open({
                      row,
                      reload: actionRef.current?.reload,
                    });
                  }}
                >
                  {t('提交报备')}
                </Button>
              </PermissionShowController>

              {row.status === SIGN_ADDITIONAL_REPORT_STATUS_REPORTING && (
                <PermissionShowController
                  need={['/sign/additional-report/change-status']}
                >
                  <Button
                    type="link"
                    onClick={() => {
                      auditDialog.current.open({
                        caption: t('报备成功'),
                        status: SIGN_ADDITIONAL_REPORT_STATUS_REPORT_SUCC,
                        id: row.additional_id,
                      });
                    }}
                  >
                    <Text theme="success">{t('报备成功')}</Text>
                  </Button>
                  <Button
                    type="link"
                    onClick={() => {
                      auditDialog.current.open({
                        caption: t('报备失败'),
                        status: SIGN_ADDITIONAL_REPORT_STATUS_REPORT_FAIL,
                        id: row.additional_id,
                      });
                    }}
                  >
                    <Text theme="danger">{t('报备失败')}</Text>
                  </Button>
                </PermissionShowController>
              )}
            </>
          );
        },
      },
    ];
  }, [additionalType, auditDialog, dialogRef, providerList]);

  function handleAdd(additionalType) {
    history.push(`/sign/additional-report/create?type=${additionalType}`);
  }

  const operateStatus = async ({ id, status, msg }) => {
    const params: any = { additional_id: id, status };
    msg && (params.msg = msg);
    const res = await ChangeSignAdditionalReportStatus(params);
    if (res.code === 0) {
      actionRef.current?.reload();
      message.success({ content: t('操作成功') });
    }
    return res;
  };

  function renderTable(additionalType) {
    return (
      <>
        <Alert>
          {additionalType === 'customer'
            ? t('给定待报备通道，拉取对应客户全量签名进行报备')
            : t('给定待报备通道及对等通道，拉取对等通道已报备签名进行报备')}
        </Alert>
        <div style={{ marginBottom: 10 }}>
          <Button
            style={{ marginRight: 10 }}
            type="primary"
            onClick={() => {
              handleAdd(additionalType);
            }}
          >
            {t('新建补报备需求')}
          </Button>
        </div>
        <ProTable
          actionRef={actionRef}
          searchable={{
            layout: 'inline',
            submitter: {
              render: (form: any) => {
                return (
                  <>
                    <Button
                      type="primary"
                      loading={form?.submitting}
                      onClick={() => form.submit()}
                    >
                      {t('查询')}
                    </Button>
                    <Button onClick={() => actionRef.current?.reset()}>
                      {t('重置')}
                    </Button>
                  </>
                );
              },
            },
          }}
          disableTextOverflow
          bordered
          recordKey="additional_id"
          request={async (params) => {
            const { data } = await getSignAdditionalReport({
              ..._.omit(
                _.pickBy(params, (v) => v !== '' && !_.isNil(v)),
                ['pageSize', 'pageIndex', 'current'],
              ),
              page_size: params.pageSize,
              page_index: params.pageIndex,
              type:
                additionalType === 'provider'
                  ? SIGN_ADDITIONAL_REPORT_TYPE_PROVIDER
                  : SIGN_ADDITIONAL_REPORT_TYPE_CUSTOMER,
            });
            const { list, count } = data;
            return {
              data: list,
              success: true,
              total: count,
            };
          }}
          pageable
          columns={columns}
          addons={[
            {
              type: 'expandable',
              expandedKeys: expandedKeys,
              onExpandedKeysChange: (keys: any, { event }) => {
                setExpandedKeys(keys);
              },
              render: (record) => {
                return <SignReportList record={record}></SignReportList>;
              },
            },
          ]}
        />
      </>
    );
  }

  return (
    <div className="proTable">
      <Tabs
        tabs={[
          {
            label: t('客户维度'),
            id: 'customer',
          },
          {
            label: t('对等账号维度'),
            id: 'provider',
          },
        ]}
        activeId={additionalType}
        onActive={(tab) => {
          setAdditionalType(tab.id);
        }}
      >
        <TabPanel id="provider">{renderTable('provider')}</TabPanel>
        <TabPanel id="customer">{renderTable('customer')}</TabPanel>
      </Tabs>

      <MakeUpSignDialog dialogRef={dialogRef} />
      <AuditOperateDialog dialogRef={auditDialog} onSubmit={operateStatus} />
    </div>
  );
};
export default SignApplyMakeUpMange;
