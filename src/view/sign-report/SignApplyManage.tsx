import React, { useState, useMemo, useRef } from 'react';
import { app } from '@src/utils/tea/app';
import {
  Button,
  Form,
  Text,
  Dropdown,
  List,
  Modal,
  Row,
  Col,
  message,
  Icon,
} from '@tencent/tea-component';
import { t, Trans } from '@src/utils/i18n';
import { useHistory, useParams } from 'react-router-dom';
import { useDialogRef } from '@src/utils/react-use/useDialog';
import _ from 'lodash';
import { allSignApplyColumns, signApplyColumns } from './component/const';
import { SignReportList } from './component/ReportList';
import { saveCSV } from '@src/utils/saveCsv';
import {
  getExportData,
  getPrivacyInfo,
} from '@src/global-components/import-component/utils';
import { ProductAuditDialog } from './component/ProductAuditDialog';
import { ActionType, ProTable } from '@tencent/tea-material-pro-table';
import {
  addSignApply,
  getReportFailureReason,
  getSignApplyList,
  sendReportEmail,
} from '@src/apiV2/sign';
import { ImportExcelDialog } from '@src/global-components/import-component/ImportExcelDialog';
import { scrollable } from '@tea/component/table/addons';
import {
  idCardType,
  SIGN_REPORT_APPLY_SOURCE_INNER,
  SIGN_REPORT_APPLY_STATUS_REPORT_FAIL,
  SIGN_REPORT_APPLY_STATUS_REPORT_SUCC,
  SIGN_REPORT_APPLY_STATUS_REPORTING,
  SIGN_REPORT_APPLY_STATUS_WAIT_ADD_INFO,
  SIGN_REPORT_APPLY_STATUS_WAIT_REPORT,
  SIGN_REPORT_STATUS_REPORTING,
  SIGN_REPORT_STATUS_STOP_USE,
  SIGN_REPORT_STATUS_WAIT_ADD_INFO,
  SIGN_REPORT_STATUS_WAIT_REPORT,
  SIGN_TYPE_APP,
  SIGN_TYPE_TRADEMARK,
  smsTypeOptions,
} from '@src/const/const';
import { EditShowKeysDialog } from './component/EditShowKeysDialog';
import useAsyncRetryFunc from '@src/utils/react-use/useAsyncFunc';

export const SignApplyManage = (props) => {
  const history = useHistory<any>();
  const params = useParams<{ applyId: string }>();
  const importDialogRef = useDialogRef();
  const auditDialogRef = useDialogRef();
  const [expandedKeys, setExpandedKeys] = useState<string[]>([]);
  const [list, setList] = useState<any[]>([]);
  const actionRef = useRef<ActionType>();
  const editShowKeysDialogRef = useDialogRef();

  const [transactorCrNumShow, setTransactorCrNumShow] = useState(false);
  const [corpCrNumShow, setCorpCrNumShow] = useState(false);
  const [selectedKeys, setSelectedKeys] = useState<string[]>([]);

  const { value: failureReason } = useAsyncRetryFunc(async () => {
    const result = await getReportFailureReason({
      page_index: 1,
      page_size: 1000,
    });
    return result?.data;
  }, []);

  const failureReasonList = useMemo(() => {
    return failureReason?.list ?? [];
  }, [failureReason]);

  async function openDetail(row) {
    Modal.alert({
      message: t('申请单详情'),
      size: 'xl',
      description: (
        <div>
          <Form readonly style={{ display: 'block' }}>
            <Row>
              {signApplyColumns.map((el) => (
                <Col span={8} key={el.key}>
                  <Form.Item label={el.header}>
                    <Form.Text>
                      {el.render ? el.render(row) : row[el.key]}
                    </Form.Text>
                  </Form.Item>
                </Col>
              ))}
            </Row>
          </Form>
        </div>
      ),
    });
  }

  async function sendEmail(rows) {
    try {
      const res = await sendReportEmail({
        report_ids: rows.map((r) => r.report_id),
      });
      if (res.code === 0) {
        message.success({ content: t('催单成功') });
      }
    } catch (err) {}
  }

  const columns = useMemo(() => {
    const _signApplyColumns = _.cloneDeep(signApplyColumns);
    const failure_reason_ids: any =
      _.find(_signApplyColumns, (el) => el.key === 'failure_reason_ids') ?? {};
    const transactor_cr_num: any =
      _.find(_signApplyColumns, (el) => el.key === 'transactor_cr_num') ?? {};
    const corp_cr_num: any =
      _.find(_signApplyColumns, (el) => el.key === 'corp_cr_num') ?? {};

    failure_reason_ids.render = (row) => {
      const reason = _.map(
        row.failure_reason_ids?.split(','),
        (r) => _.find(failureReasonList, (f) => +f.id === +r)?.son_category,
      )?.join(';');
      return reason || '-';
    };

    transactor_cr_num.header = (
      <>
        <Text>{t('企业法人身份证号码')}</Text>
        <Button type="link">
          {!transactorCrNumShow ? (
            <Icon
              type="hide"
              onClick={() => {
                setTransactorCrNumShow(true);
              }}
            />
          ) : (
            <Icon
              type="show"
              onClick={() => {
                setTransactorCrNumShow(false);
              }}
            />
          )}
        </Button>
      </>
    );
    transactor_cr_num.render = (row) => {
      return transactorCrNumShow
        ? row.transactor_cr_num
        : getPrivacyInfo(row.transactor_cr_num);
    };

    corp_cr_num.header = (
      <>
        <Text>{t('经办人身份证号码')}</Text>
        <Button type="link">
          {!corpCrNumShow ? (
            <Icon
              type="hide"
              onClick={() => {
                setCorpCrNumShow(true);
              }}
            />
          ) : (
            <Icon
              type="show"
              onClick={() => {
                setCorpCrNumShow(false);
              }}
            />
          )}
        </Button>
      </>
    );
    corp_cr_num.render = (row) => {
      return corpCrNumShow ? row.corp_cr_num : getPrivacyInfo(row.corp_cr_num);
    };

    const edit = [
      SIGN_REPORT_APPLY_STATUS_WAIT_REPORT,
      SIGN_REPORT_APPLY_STATUS_REPORT_FAIL,
      SIGN_REPORT_APPLY_STATUS_WAIT_ADD_INFO,
    ];
    const registr = [
      SIGN_REPORT_APPLY_STATUS_REPORTING,
      SIGN_REPORT_APPLY_STATUS_REPORT_SUCC,
      SIGN_REPORT_APPLY_STATUS_REPORT_FAIL,
    ];
    const canDelete = [
      SIGN_REPORT_APPLY_STATUS_WAIT_REPORT,
      SIGN_REPORT_APPLY_STATUS_REPORTING,
      SIGN_REPORT_APPLY_STATUS_REPORT_SUCC,
    ];
    return [
      ..._signApplyColumns,
      {
        header: t('操作'),
        key: 'operation',
        width: 120,
        fixed: 'right',
        hideForm: true,
        render: (row, index, i) => {
          return (
            <>
              {/* 待报备 */}
              {row.status === SIGN_REPORT_APPLY_STATUS_WAIT_REPORT && (
                <Button
                  type="link"
                  onClick={() => {
                    history.push({
                      pathname: `/sign/apply/submit/${row.apply_id}`,
                      state: {
                        row: { ...row, sms_type: `${row.sms_type}` },
                      },
                    });
                  }}
                >
                  {t('提交报备')}
                </Button>
              )}
              {/* 报备中 */}
              {row.status === SIGN_REPORT_APPLY_STATUS_REPORTING && (
                <>
                  <Button
                    type="link"
                    onClick={() => {
                      auditDialogRef.current.open({
                        row,
                        status: SIGN_REPORT_APPLY_STATUS_REPORT_SUCC,
                        caption: t('报备成功'),
                        reload: async () => {
                          setExpandedKeys([]);
                          await actionRef.current?.reload();
                          setExpandedKeys([`${row.apply_id}`]);
                        },
                      });
                    }}
                  >
                    <Text theme="success">{t('报备成功')}</Text>
                  </Button>
                  <Button
                    type="link"
                    onClick={() => {
                      auditDialogRef.current.open({
                        row,
                        status: SIGN_REPORT_APPLY_STATUS_REPORT_FAIL,
                        caption: t('报备失败'),
                        reload: async () => {
                          setExpandedKeys([]);
                          await actionRef.current?.reload();
                          setExpandedKeys([`${row.apply_id}`]);
                        },
                      });
                    }}
                  >
                    <Text theme="danger">{t('报备失败')}</Text>
                  </Button>
                </>
              )}
              {/* {[5, 6].includes(row.status) && (
                <Button
                  type="link"
                  onClick={() => {
                    auditDialogRef.current.open({
                      row,
                      // type: 'pass',
                      status: 3,
                      caption: t('重新报备'),
                      reload: async () => {
                        setExpandedKeys([]);
                        await actionRef.current?.reload();
                        setExpandedKeys([`${row.apply_id}`]);
                      },
                    });
                  }}
                >
                  {t('重新报备')}
                </Button>
              )} */}
              {row.status === SIGN_REPORT_APPLY_STATUS_REPORTING && (
                <Button
                  type="link"
                  onClick={() => {
                    auditDialogRef.current.open({
                      row,
                      status: SIGN_REPORT_APPLY_STATUS_WAIT_ADD_INFO,
                      caption: t('资料异常'),
                      reload: async () => {
                        setExpandedKeys([]);
                        await actionRef.current?.reload();
                        setExpandedKeys([`${row.apply_id}`]);
                      },
                    });
                  }}
                >
                  <Text theme="warning">{t('资料异常')}</Text>
                </Button>
              )}
              {canDelete.includes(row.status) &&
                row.source === SIGN_REPORT_APPLY_SOURCE_INNER && (
                  <Button
                    type="link"
                    onClick={() => {
                      auditDialogRef.current.open({
                        row,
                        type: 'delete',
                        status: -1,
                        caption: t('确定删除吗？'),
                        reload: async () => {
                          setExpandedKeys([]);
                          await actionRef.current?.reload();
                          setExpandedKeys([`${row.apply_id}`]);
                        },
                      });
                    }}
                  >
                    {t('删除')}
                  </Button>
                )}
              <Dropdown
                trigger="hover"
                clickClose={false}
                style={{ marginRight: 10 }}
                button={t('更多')}
                appearance="link"
              >
                <List type="option">
                  <List.Item onClick={() => openDetail(row)}>
                    <Trans>详情</Trans>
                  </List.Item>
                  {edit.includes(row.status) && (
                    <List.Item
                      onClick={() => {
                        console.log(row);
                        history.push({
                          pathname: `/sign/apply/modify`,
                          state: {
                            row: { ...row, sms_type: `${row.sms_type}` },
                          },
                        });
                      }}
                    >
                      <Trans>编辑</Trans>
                    </List.Item>
                  )}
                  {registr.includes(row.status) && (
                    <List.Item
                      onClick={() => {
                        history.push({
                          pathname: `/sign/apply/submit/${row.apply_id}`,
                          state: {
                            row: { ...row, sms_type: `${row.sms_type}` },
                          },
                        });
                      }}
                    >
                      {t('补充报备')}
                    </List.Item>
                  )}
                  <List.Item
                    onClick={() => {
                      history.push({
                        pathname: `/sign/apply/create`,
                        state: {
                          row: { ...row, sms_type: `${row.sms_type}` },
                        },
                      });
                    }}
                  >
                    {t('复制')}
                  </List.Item>
                  <List.Item
                    onClick={() => {
                      if (!row.selectedReports?.length) {
                        return app.tips.error(t('请选择要补充资料的报备单'));
                      }
                      if (
                        _.some(
                          row.selectedReports,
                          (r) => +r.status !== SIGN_REPORT_STATUS_WAIT_ADD_INFO,
                        )
                      ) {
                        return app.tips.error(t('只能选择资料异常的报备单'));
                      }
                      editShowKeysDialogRef.current?.open({
                        selectedIds: row.selectedReports?.map(
                          (s) => s.report_id,
                        ),
                        reload: () => actionRef.current?.reload(),
                        type: 'batch',
                      });
                    }}
                  >
                    {t('批量补充资料')}
                  </List.Item>
                  <List.Item
                    onClick={() => {
                      if (!row.selectedReports?.length) {
                        return app.tips.error(t('请选择要催单的报备单'));
                      }
                      if (
                        _.every(row.selectedReports, (r) =>
                          [
                            SIGN_REPORT_STATUS_REPORTING,
                            SIGN_REPORT_STATUS_WAIT_REPORT,
                            SIGN_REPORT_STATUS_STOP_USE,
                            SIGN_REPORT_STATUS_WAIT_ADD_INFO,
                          ].includes(+row.status),
                        )
                      ) {
                        return app.tips.error(
                          t(
                            '只能对待报备、报备中、供应商停用、资料异常状态进行催单',
                          ),
                        );
                      }
                      sendEmail(row.selectedReports);
                    }}
                  >
                    {t('批量催单')}
                  </List.Item>
                </List>
              </Dropdown>
            </>
          );
        },
      },
    ];
  }, [
    auditDialogRef,
    corpCrNumShow,
    editShowKeysDialogRef,
    failureReasonList,
    history,
    transactorCrNumShow,
  ]);

  function createSignApply() {
    history.push('/sign/apply/create');
  }

  function downloadFile() {
    const link = document.createElement('a');
    link.download = '';
    link.href = t(
      'https://dscache.tencent-cloud.cn/upload/uploader/申请单批量导入模版-0954750b2b70c249a2eca58ef2fae75156014f22.xlsx',
    );
    link.click();
  }

  const importCols = useMemo(() => {
    const importColsOrder = [
      'qappid',
      'sms_type',
      'sign',
      'company_name',
      'unisc_id',
      'corp_name',
      'corp_cr_type',
      'corp_cr_num',
      'transactor_name',
      'transactor_cr_type',
      'transactor_cr_num',
      'transactor_phone',
      'remark_type',
      'registration_num',
      'app_resource_url',
      'remark',
    ];
    const cols = _.filter(_.cloneDeep(allSignApplyColumns), (el) =>
      importColsOrder.includes(el.key),
    );
    return _.sortBy(cols, (item) => {
      return importColsOrder.indexOf(item.key);
    });
  }, []);

  function handleData(data: any[]) {
    return _.map(data, (item) => {
      return {
        // uin: +item.uin,
        qappid: +item.qappid,
        sms_type: _.find(smsTypeOptions, (s) => s.text === item.sms_type)
          ?.value,
        corp_cr_type: _.find(idCardType, (s) => s.text === item.corp_cr_type)
          ?.value,
        transactor_cr_type: _.find(
          idCardType,
          (s) => s.text === item.transactor_cr_type,
        )?.value,
        remark_type: item.remark_type,
        sign: item.sign,
        company_name: `${item.company_name}`,
        unisc_id: `${item.unisc_id}`,
        corp_name: `${item.corp_name}`,
        corp_cr_num: `${item.corp_cr_num}`,
        transactor_name: `${item.transactor_name}`,
        transactor_cr_num: `${item.transactor_cr_num}`,
        transactor_phone: `${item.transactor_phone}`,
        registration_num: `${item.registration_num}`,
        app_resource_url: `${item.app_resource_url}`,
        remark: item.remark || undefined,
      };
    });
  }

  return (
    <>
      <div className="proTable">
        <div style={{ marginBottom: 10 }}>
          <Button
            style={{ marginRight: 10 }}
            type="primary"
            onClick={createSignApply}
          >
            {t('新建报备需求')}
          </Button>
          <Button
            onClick={() => {
              importDialogRef.current.open();
            }}
            style={{ marginRight: 10 }}
          >
            {t('批量导入')}
          </Button>
          {/* <Button
            onClick={() => {
              history.push({
                pathname: `/sign/apply/submit`,
                state: {
                  applyIds: selectedKeys,
                },
              });
            }}
          >
            {t('批量提交报备')}
          </Button> */}
        </div>
        <ProTable
          actionRef={actionRef}
          searchable={{
            layout: 'inline',
            submitter: {
              render: (form: any) => {
                return (
                  <>
                    <Button
                      type="primary"
                      loading={form?.submitting}
                      onClick={() => {
                        form.submit();
                        setExpandedKeys([]);
                      }}
                    >
                      {t('查询')}
                    </Button>
                    <Button onClick={() => actionRef.current?.reset()}>
                      {t('重置')}
                    </Button>
                  </>
                );
              },
            },
            initialValues: {
              apply_id: params?.applyId?.toString(),
              status: SIGN_REPORT_APPLY_STATUS_REPORTING,
            },
          }}
          disableTextOverflow
          bordered
          recordKey="apply_id"
          request={async (params) => {
            const { data } = await getSignApplyList({
              ..._.omit(
                _.pickBy(params, (v) => v !== '' && !_.isNil(v)),
                ['pageSize', 'pageIndex', 'current', 'apply_id', 'sign'],
              ),
              page_size: params.pageSize,
              page_index: params.pageIndex,
              apply_ids: params.apply_id
                ? [...params.apply_id?.split(',')]
                : undefined,
              signs:
                params.sign?.split('\n')?.map((s) => s.trim()) ?? undefined,
            });
            const { list, count } = data;
            setList(list ?? []);
            return {
              data: list,
              success: true,
              total: count,
            };
          }}
          pageable
          columns={columns}
          addons={[
            {
              type: 'expandable',
              expandedKeys: expandedKeys,
              onExpandedKeysChange: (keys: any, { event }) => {
                setExpandedKeys(keys);
              },
              render: (record) => {
                return (
                  <SignReportList
                    record={record}
                    onSelectedChange={(records) => {
                      record.selectedReports = records;
                    }}
                  ></SignReportList>
                );
              },
            },
            {
              type: 'selectable',
              value: selectedKeys,
              // rowSelectable: (rowKey, { record }) => record.status === 5,
              onChange: (keys, context) => {
                setSelectedKeys(keys);
                // onSelectedChange?.(context?.selectedRecords);
              },
              rowSelect: true,
            },
          ]}
        />
      </div>
      <ImportExcelDialog
        dialogRef={importDialogRef}
        columns={importCols}
        header={importCols.map((v) => v.key)}
        onSubmit={async (params) => {
          return await addSignApply({
            params: params.params.map((p) => {
              return {
                ..._.pickBy(p, (v) => v !== '' && !_.isNil(v)),
                registration_url:
                  p.remark_type === SIGN_TYPE_APP ||
                  p.remark_type === SIGN_TYPE_TRADEMARK
                    ? p.registration_url
                    : undefined,
                registration_num:
                  p.remark_type === SIGN_TYPE_APP ||
                  p.remark_type === SIGN_TYPE_TRADEMARK
                    ? p.registration_num
                    : undefined,
                app_resource_url:
                  p.remark_type === SIGN_TYPE_APP
                    ? p.app_resource_url
                    : undefined,
                attach_url:
                  p.remark_type === SIGN_TYPE_APP ||
                  p.remark_type === SIGN_TYPE_TRADEMARK
                    ? p.attach_url
                    : undefined,
              };
            }),
          });
        }}
        onSuccess={() => {
          actionRef?.current?.reload();
        }}
        tableAddons={[
          scrollable({
            maxHeight: 192,
            virtualizedOptions: {
              height: 310,
              itemHeight: 30,
            },
          }),
        ]}
        downloadFile={downloadFile}
        handleData={handleData}
        range={1}
      />

      <ProductAuditDialog dialogRef={auditDialogRef} />
      <EditShowKeysDialog
        dialogRef={editShowKeysDialogRef}
      ></EditShowKeysDialog>
    </>
  );
};
export default SignApplyManage;
