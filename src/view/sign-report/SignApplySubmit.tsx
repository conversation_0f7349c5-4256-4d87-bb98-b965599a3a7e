import React, { useState, useEffect, useMemo, useCallback } from 'react';
import {
  Button,
  Form,
  Checkbox,
  message,
  Row,
  Col,
  H3,
  StatusTip,
  Text,
  Input,
  Cascader,
} from '@tencent/tea-component';
import style from './index.module.less';
import { t } from '@src/utils/i18n';
import { useHistory } from 'react-router-dom';
import _ from 'lodash';
import { useAsyncFn, useList, useSetState } from 'react-use';
import {
  allSignApplyColumns,
  getSignShowKeys,
  signApplyColumns,
} from './component/const';
import { defaultSignShowKeys } from '@src/const/const';
import {
  getNeedReportProvider,
  getProviderList,
  getSignReportList,
  getSubCode,
  submitSignApply,
} from '@src/apiV2/sign';
import useAsyncRetryFunc from '@src/utils/react-use/useAsyncFunc';
import { getPurchaseSupplier } from '@src/apiV2/getPurchaseSupplier';

function getSupplierName(list, id) {
  return _.find(list, (c) => c.supplier_id === id)?.name;
}
export const SignApplySubmit = (props) => {
  const history = useHistory<any>();
  const { row: detail = {}, applyIds } = history.location?.state || {};
  const _signShowKeys = getSignShowKeys(detail);
  const _defaultSignShowKeys = _.intersection(
    defaultSignShowKeys,
    _signShowKeys,
  );
  console.log(_signShowKeys, _defaultSignShowKeys);
  const isMutiple = applyIds?.length > 1; // 是否为批量报备
  const [selectedKeys, setSelectedKeys] = useSetState<{
    [key: string]: string[];
  }>();
  const [selfSelectedProviders, { set: setSelfSelectedProviders }] =
    useList<string[]>();
  const [_selfSelectedProviders, { set: _setSelfSelectedProviders }] =
    useList<string[]>();
  const [selectedProviders, setSelectedProviders] = useSetState<{
    [key: string]: string[];
  }>();
  const [subCode, setSubCode] = useSetState<{
    [key: string]: string | string[];
  }>();
  const [submitting, setSubmitting] = useState(false);

  // 获取全量供应商列表
  const { value: supplierList } = useAsyncRetryFunc(async () => {
    const result = await getPurchaseSupplier({
      page_index: 1,
      page_size: 1000,
    });
    return result?.data?.list;
  }, []);

  // 获取通道配置列表
  const { value: providerList } = useAsyncRetryFunc(async () => {
    const result = await getProviderList({
      page_index: 1,
      page_size: 10000,
      sms_type: detail?.sms_type,
    });
    return result?.data?.list;
  }, [detail?.sms_type]);

  //  获取可报备的供应商账号
  const [needRegAccount, fetchNeedRegAccount] = useAsyncFn(async () => {
    const { data } = await getNeedReportProvider({
      apply_id_arr: isMutiple ? applyIds : [detail?.apply_id],
    });
    return _.map(data?.[detail?.apply_id]?.provider_info_arr ?? [], (el) => ({
      ...el,
      show_keys: el.show_keys?.split(','),
    }));
  }, [applyIds, detail?.apply_id, isMutiple]);

  //  获取已提交报备的报备单
  const [reportedAccount, fetchReportedAccount] = useAsyncFn(async () => {
    const { data } = await getSignReportList({
      apply_id: detail?.apply_id,
    });
    return (
      data?.list?.map((el) => ({
        account: el.account,
        provider_id: el.provider_id,
        show_keys: el.show_keys?.split(','),
        sub_code: el.sub_code,
        supplier_id: el.supplier_id,
      })) ?? []
    );
  }, [detail?.apply_id]);

  // 可报备的供应商账号、已提交报备的、自选账号合并
  const allRegAccount = useMemo(() => {
    const self = _.map(_selfSelectedProviders, (s) => {
      const info: any =
        _.find(
          providerList,
          (p) => p.supplier_id === +s[0] && p.provider_id === +s[1],
        ) ?? {};
      return {
        account: info?.account,
        provider_id: info?.provider_id,
        show_keys: info?.show_keys ?? _defaultSignShowKeys,
        sub_code: subCode[info?.provider_id],
        supplier_id: info?.supplier_id,
      };
    });
    return _.concat(
      _.flatten([needRegAccount?.value ?? [], reportedAccount?.value ?? []]),
      self,
    );
  }, [
    _selfSelectedProviders,
    needRegAccount?.value,
    reportedAccount?.value,
    providerList,
  ]);

  const needRegAccountGroupBySupplier = useMemo(() => {
    return _.reduce(
      allRegAccount,
      (acc: any[], cur) => {
        const index = _.findIndex(
          acc,
          (el) => el.supplier_id === cur?.supplier_id,
        );
        if (index !== -1) {
          acc[index].children.push(cur);
        } else {
          acc.push({
            supplier_id: cur?.supplier_id,
            children: [cur],
          });
        }
        return acc;
      },
      [],
    );
  }, [allRegAccount]);

  const setDefaultValues = useCallback(
    (del = []) => {
      setSelectedKeys(
        _.reduce(
          allRegAccount,
          (acc, cur) => {
            return {
              ...acc,
              [`${cur?.supplier_id}`]: _.map(
                cur?.show_keys,
                (k) => `${k}-${cur?.supplier_id}`,
              ),
            };
          },
          {},
        ),
      );

      setSelectedProviders(
        _.reduce(
          needRegAccountGroupBySupplier,
          (acc, cur) => {
            return {
              ...acc,
              [cur.supplier_id]: cur.children.map((p) => `${p.provider_id}`),
            };
          },
          {},
        ),
      );
      setSubCode(
        _.reduce(
          allRegAccount,
          (acc, cur) => {
            return {
              ...acc,
              [`${cur.provider_id}`]: cur.sub_code,
            };
          },
          {},
        ),
      );
    },
    [
      allRegAccount,
      needRegAccountGroupBySupplier,
      setSelectedKeys,
      setSelectedProviders,
      setSubCode,
    ],
  );

  useEffect(() => {
    setDefaultValues();
  }, [setDefaultValues, _selfSelectedProviders]);

  useEffect(() => {
    if (isMutiple) {
      return;
    }
    fetchReportedAccount();
    fetchNeedRegAccount();
  }, [fetchNeedRegAccount, fetchReportedAccount, isMutiple]);

  const formatShowKeys = (val) => {
    return val?.map((k) => k.split('-').slice(0, -1).join('')).join(',') ?? '';
  };

  async function onSubmit() {
    const selectedProvidersArr: any[] = _.flatMap(
      selectedProviders,
      (providerIds, supplierId) =>
        _.flatMap(providerIds, (providerId) => {
          const info: any =
            _.find(
              allRegAccount,
              (el) =>
                el.supplier_id === +supplierId &&
                el.provider_id === +providerId,
            ) ?? {};
          if (_.isArray(subCode[providerId])) {
            return _.map(subCode[providerId], (s) => ({
              account: info?.account,
              provider_id: +providerId,
              supplier_id: supplierId,
              sub_code: s,
              show_keys: formatShowKeys(selectedKeys[+info.supplier_id]),
            }));
          }
          return {
            account: info?.account,
            provider_id: +providerId,
            supplier_id: supplierId,
            sub_code: subCode[providerId],
            show_keys: formatShowKeys(selectedKeys[+info.supplier_id]),
          };
        }),
    );

    if (!selectedProvidersArr?.length) {
      return message.error({ content: t('供应商不能为空') });
    }

    const params = {
      apply_id: detail?.apply_id,
      sign: detail?.sign,
      remark: detail?.remark || undefined,
      provider_info_arr: selectedProvidersArr,
    };
    setSubmitting(true);
    try {
      const res = await submitSignApply({ params: [params] });
      if (res.code === 0 && !res.data?.errors?.length) {
        message.success({ content: t('提交成功') });
        history.push('/sign/apply');
      }
    } catch (err) {
      console.log(err);
    }
    setSubmitting(false);
  }

  function renderShowKeysCheck(supplier_id) {
    const showKeys = _signShowKeys;
    return (
      <Checkbox.Group
        value={selectedKeys[supplier_id]}
        onChange={(val: any) => {
          setSelectedKeys({
            [supplier_id]: val,
          });
        }}
      >
        {showKeys.map((k, i) => (
          <Checkbox name={`${k}-${supplier_id}`} key={`${k}-${supplier_id}`}>
            {allSignApplyColumns.find((el) => el.key === k)?.header}
          </Checkbox>
        ))}
      </Checkbox.Group>
    );
  }

  function renderCascader(el) {
    return (
      <div key={el.value}>
        <hr />
        <>
          <Form.Item label={t('供应商')}>
            {!el ? (
              <Form.Text>
                <Text theme="danger">{t('【空】')}</Text>
              </Form.Text>
            ) : (
              <>
                <Form.Text style={{ marginBottom: 12 }}>
                  【{getSupplierName(supplierList, el.supplier_id)}】
                </Form.Text>
                <Checkbox.Group
                  value={selectedProviders[el.supplier_id]}
                  onChange={(val) =>
                    setSelectedProviders({ [el.supplier_id]: val })
                  }
                  style={{ display: 'block' }}
                >
                  {_.map(el.children, (child) => {
                    const key = `${child?.provider_id}`;
                    console.log(key);
                    return (
                      <div key={key}>
                        <Row>
                          <Col span={8}>
                            <Checkbox name={key}></Checkbox>
                            <Text reset style={{ marginRight: 30 }}>
                              {t('账号：')}
                              {child?.account || '-'}
                            </Text>
                          </Col>
                          <Col span={6}>
                            <Text reset>
                              {t('providerId：')}
                              {child?.provider_id || '-'}
                            </Text>
                          </Col>

                          <Col>
                            {_.isArray(subCode[`${child?.provider_id}`]) ? (
                              _.map(
                                subCode[`${child?.provider_id}`],
                                (c, i) => (
                                  <div key={`${c}_${i}`}>
                                    <Text reset>{t('签名子码：')}</Text>
                                    <Input
                                      value={c}
                                      onChange={(val) =>
                                        setSubCode({
                                          [`${child?.provider_id}`]: val,
                                        })
                                      }
                                    />
                                  </div>
                                ),
                              )
                            ) : (
                              <>
                                <Text reset>{t('签名子码：')}</Text>
                                <Input
                                  // @ts-ignore
                                  value={subCode[`${child?.provider_id}`]}
                                  onChange={(val) =>
                                    setSubCode({
                                      [`${child?.provider_id}`]: val,
                                    })
                                  }
                                />
                              </>
                            )}
                          </Col>
                        </Row>
                      </div>
                    );
                  })}
                </Checkbox.Group>
              </>
            )}
          </Form.Item>
          <Form.Item>{renderShowKeysCheck(el.supplier_id)}</Form.Item>
        </>
      </div>
    );
  }

  function groupProviderBySupplier() {
    return _.reduce(
      providerList,
      (
        result: Array<{
          label: string;
          value: string;
          children: any[];
        }>,
        cur,
      ) => {
        const index = result.findIndex(
          (el: any) => el.value.toString() === cur.supplier_id.toString(),
        );
        if (index === -1) {
          const name = getSupplierName(supplierList, cur.supplier_id);
          result.push({
            label: `${name}(${cur.supplier_id})`,
            value: cur.supplier_id.toString() ?? '',
            children: [
              {
                label: `${cur.provider_name}(${cur.provider_id})`,
                value: cur.provider_id.toString(),
              },
            ],
          });
        } else {
          result[index].children.push({
            label: `${cur.provider_name}(${cur.provider_id})`,
            value: cur.provider_id.toString(),
          });
        }
        return result;
      },
      [],
    );
  }

  async function handleGetSubCode() {
    const providerIds = _.flatMap(selfSelectedProviders, (el) => +el[1]);
    if (providerIds.length) {
      const res = await getSubCode({
        qappid: detail.qappid,
        signs: [detail.sign],
        provider_ids: providerIds,
      });
      if (res.code !== 0) {
        message.error({ content: t('获取签名子码失败') });
      }
      if (_.isEmpty(res.data)) {
        _setSelfSelectedProviders(_.cloneDeep(selfSelectedProviders));
      }

      _.map(providerIds, (p) => {
        const info = res.data[`${detail.sign}_${p}`];
        setSubCode({ [`${p}`]: info?.sub_code ?? '' });
      });
    }

    _setSelfSelectedProviders(_.cloneDeep(selfSelectedProviders));
  }

  return needRegAccount?.loading ? (
    <StatusTip status="loading"></StatusTip>
  ) : (
    <>
      <H3>{t('提交报备')}</H3>
      <hr />
      <Row>
        <Col span={20}>
          <div>
            <Form.Title>{t('基本信息')}</Form.Title>
            <Form layout="inline">
              {isMutiple ? (
                <Form.Item label={t('申请单ID')}>
                  <Form.Text>{applyIds?.join(',')}</Form.Text>
                </Form.Item>
              ) : (
                signApplyColumns
                  .filter(
                    (el) =>
                      !['status', 'created_at', 'updated_at'].includes(el.key),
                  )
                  .map((el) => (
                    <Form.Item label={el.header} key={el.key}>
                      <Form.Text>
                        {el.render ? el.render(detail) : detail[el.key]}
                      </Form.Text>
                    </Form.Item>
                  ))
              )}
              <br />
            </Form>
            <Form.Title>{t('需报备通道')}</Form.Title>
            <Form
              className={style.submit}
              style={{ marginTop: 20 }}
              layout="inline"
            >
              {needRegAccountGroupBySupplier?.length ? (
                needRegAccountGroupBySupplier.map((el) => renderCascader(el))
              ) : (
                <Text theme="danger">【{t('暂无可报备通道')}】</Text>
              )}
              {/* 自选通道 */}
              <Form.Title>{t('自选通道')}</Form.Title>
              <Form.Item label={t('自选通道')}>
                <Cascader
                  value={selfSelectedProviders}
                  onChange={(val, context) => {
                    setSelfSelectedProviders(val);
                  }}
                  all
                  multiple
                  clearable
                  type="menu"
                  data={groupProviderBySupplier()}
                  showMode="onlyLeaf"
                  valueMode="onlyLeaf"
                  searchable
                />
                <Button type="primary" onClick={handleGetSubCode}>
                  {t('确定')}
                </Button>
              </Form.Item>
              <Form.Action>
                <Button
                  type="primary"
                  // htmlType="submit"
                  loading={submitting}
                  onClick={onSubmit}
                  style={{ marginLeft: 5 }}
                >
                  {t('提交')}
                </Button>
                <Button
                  htmlType="button"
                  onClick={(e) => history.push('/sign/apply')}
                  style={{ marginLeft: 10 }}
                >
                  {t('取消')}
                </Button>
              </Form.Action>
            </Form>
          </div>
        </Col>
      </Row>
    </>
  );
};
export default SignApplySubmit;
