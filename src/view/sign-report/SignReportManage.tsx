import React, { useState, useEffect, useMemo, useCallback } from 'react';
import {
  Button,
  Input,
  Select,
  Form,
  Text,
  Table,
  Modal,
} from '@tencent/tea-component';
import { app } from '@src/utils/tea/app';
import { Form as FinalForm, Field } from 'react-final-form';
import { getStatus } from '@src/utils/form/getStatus';
import { t, Trans, Slot } from '@src/utils/i18n';
import { useDialogRef } from '@src/utils/react-use/useDialog';
import { useSetState, useAsyncFn } from 'react-use';
import _ from 'lodash';

import {
  getStatusText,
  findText,
  SIGN_REPORT_STATUS_REPORT_FAIL,
  SIGN_REPORT_STATUS_STOP_USE,
  SIGN_REPORT_STATUS_WAIT_ADD_INFO,
} from '@src/const/const';
import {
  getExtMsg,
  getHeaderByKey,
  allSignApplyColumns,
} from './component/const';
import { signReportStatus, signShowKeys } from '@src/const/const';
import { selectable } from '@tea/component/table/addons';
import { changeSignReportStatus } from '@src/apiV2/sign';
import { getSignReportList } from '@src/apiV2/sign';
import useFetchFailureReason from '@src/global-components/useFetch/useFetchFailureReason';
import { useHistory } from 'react-router-dom';

const { autotip } = Table.addons;
const { pageable } = Table.addons;

export const SignReportManage = (props) => {
  const history = useHistory();
  const [selected, setSelected] = useState<any[]>([]);

  const [pagination, setPagination] = useSetState({
    page_index: 1,
    page_size: 10,
  });
  const [searchKeys, setSearchKeys] = useState<any>({});
  const { list: faliureReasonList } = useFetchFailureReason();

  const openFailureReasonModal = useCallback(
    async (title: string, row: any) => {
      const reason = _.map(
        row.failure_reason_ids?.split(','),
        (r) => _.find(faliureReasonList, (f) => +f.id === +r)?.son_category,
      )?.join(';');
      await Modal.alert({
        type: 'warning',
        message: title,
        description: (
          <>
            <p>
              <Trans>
                原因：
                <Slot content={reason || '-'} />
              </Trans>
            </p>
            {row.wrong_keys && (
              <p>
                <Trans>
                  缺失/异常字段：
                  <Slot content={getHeaderByKey(row.wrong_keys)} />
                </Trans>
              </p>
            )}

            <p>
              <Trans>
                备注：
                <Slot
                  content={
                    getExtMsg(row.ext?.outside_record, row.status) || '-'
                  }
                />
              </Trans>
            </p>
          </>
        ),
      });
    },
    [faliureReasonList],
  );

  const [state, fetchList] = useAsyncFn(async () => {
    const res = await getSignReportList({
      ..._.omit(searchKeys, ['sign']),
      ...pagination,
      signs: searchKeys.sign
        ?.split('\n')
        ?.map((s) => s.trim())
        ?.filter((s) => !!s),
    });
    return res.data || {};
  }, [pagination, searchKeys]);

  const list = useMemo(() => {
    return state?.value?.list ?? [];
  }, [state]);

  useEffect(() => {
    fetchList();
  }, [searchKeys, pagination, fetchList]);

  const total = useMemo(() => {
    return state?.value?.count ?? 0;
  }, [state]);

  const showKeysColumns = useMemo(() => {
    return (
      allSignApplyColumns.filter((el) => signShowKeys.includes(el.key)) ?? []
    );
  }, []);

  const columns = useMemo(() => {
    return [
      {
        key: 'report_id',
        header: t('报备单ID'),
        width: 80,
      },
      {
        key: 'apply_id',
        header: t('需求单ID'),
        width: 80,
        render: (row) => (
          <Button
            type="link"
            onClick={() => history.push(`/sign/apply/${row.apply_id}`)}
          >
            {row.apply_id}
          </Button>
        ),
      },
      {
        key: 'supplier_name',
        header: t('供应商名称'),
        width: 80,
      },
      {
        key: t('provider_name'),
        header: t('账号名称'),
        width: 120,
      },
      {
        key: 'account',
        header: t('账号ID'),
        width: 80,
      },
      {
        key: 'sub_code',
        header: t('签名子码'),
        width: 80,
      },
      {
        key: 'remark_type',
        header: t('签名类型'),
        width: 80,
      },
      {
        key: 'sign',
        header: t('签名内容'),
        width: 80,
      },
      ...showKeysColumns,
      {
        key: 'status',
        header: t('报备状态'),
        width: 100,
        fixed: 'right',
        render: (row) => getStatusText(signReportStatus, row.status),
        exportRender: (row) => findText(signReportStatus, row.status),
      },
      {
        key: 'created_at',
        header: t('创建时间'),
        width: 100,
      },
      {
        key: 'updated_at',
        header: t('更新时间'),
        width: 100,
      },
      {
        key: 'operation',
        header: t('操作'),
        width: 160,
        fixed: 'right',
        render: (row) => (
          <>
            {row.status === SIGN_REPORT_STATUS_REPORT_FAIL && (
              <Button type="link">
                <Text
                  onClick={async () => {
                    await openFailureReasonModal(t('驳回原因'), row);
                  }}
                >
                  {t('驳回原因')}
                </Text>
              </Button>
            )}
            {row.status === SIGN_REPORT_STATUS_STOP_USE && (
              <>
                <Button type="link">
                  <Text
                    onClick={async () => {
                      await openFailureReasonModal(t('停用原因'), row);
                    }}
                  >
                    {t('停用原因')}
                  </Text>
                </Button>
              </>
            )}
            {row.status === SIGN_REPORT_STATUS_WAIT_ADD_INFO && (
              <Button type="link">
                <Text
                  onClick={async () => {
                    await openFailureReasonModal(t('资料缺失原因'), row);
                  }}
                >
                  {t('资料缺失原因')}
                </Text>
              </Button>
            )}
          </>
        ),
      },
    ];
  }, [openFailureReasonModal, showKeysColumns]);

  function onSubmit(values) {
    setSearchKeys({ ...values });
  }

  async function changeStatus(params) {
    const res = await changeSignReportStatus({
      params: _.map(params, (el) => ({
        report_id: el.id,
        status: el.status,
        msg: el.msg,
        failure_reason_ids: el.failure_reason_ids || undefined,
        wrong_keys: el.wrong_keys || undefined,
      })),
    });
    if (res?.code === 0 && !res?.data?.errors?.length) {
      fetchList();
    }
    return res;
  }

  return (
    <>
      <FinalForm
        onSubmit={onSubmit}
        initialValuesEqual={(val, oldVal) => {
          return _.isEqual(val, oldVal);
        }}
      >
        {({ handleSubmit, validating, values }) => {
          return (
            <form onSubmit={handleSubmit}>
              <Form layout="inline">
                <Field name="report_id" validateFields={[]}>
                  {({ input, meta }) => (
                    <Form.Item
                      showStatusIcon={false}
                      label={t('报备单ID')}
                      status={getStatus(meta, validating)}
                      message={
                        getStatus(meta, validating) === 'error' && meta.error
                      }
                    >
                      <Input {...(input as any)} placeholder={t('请输入')} />
                    </Form.Item>
                  )}
                </Field>
                <Field name="supplier_id" validateFields={[]}>
                  {({ input, meta }) => (
                    <Form.Item
                      showStatusIcon={false}
                      label={t('供应商ID')}
                      status={getStatus(meta, validating)}
                      message={
                        getStatus(meta, validating) === 'error' && meta.error
                      }
                    >
                      <Input {...(input as any)} placeholder={t('请输入')} />
                    </Form.Item>
                  )}
                </Field>
                <Field name="account" validateFields={[]}>
                  {({ input, meta }) => (
                    <Form.Item
                      showStatusIcon={false}
                      label={t('账号ID')}
                      status={getStatus(meta, validating)}
                      message={
                        getStatus(meta, validating) === 'error' && meta.error
                      }
                    >
                      <Input {...(input as any)} placeholder={t('请输入')} />
                    </Form.Item>
                  )}
                </Field>
                <Field name="sign" validateFields={[]}>
                  {({ input, meta }) => (
                    <Form.Item
                      showStatusIcon={false}
                      label={t('签名内容')}
                      status={getStatus(meta, validating)}
                      message={
                        getStatus(meta, validating) === 'error' && meta.error
                      }
                    >
                      <Input.TextArea
                        {...(input as any)}
                        placeholder={t('换行输入多个')}
                        rows={4}
                      />
                    </Form.Item>
                  )}
                </Field>
                <Field name="company_name" validateFields={[]}>
                  {({ input, meta }) => (
                    <Form.Item
                      showStatusIcon={false}
                      label={t('企业名称')}
                      status={getStatus(meta, validating)}
                      message={
                        getStatus(meta, validating) === 'error' && meta.error
                      }
                    >
                      <Input {...(input as any)} placeholder={t('请输入')} />
                    </Form.Item>
                  )}
                </Field>
                <Field name="transactor_name" validateFields={[]}>
                  {({ input, meta }) => (
                    <Form.Item
                      showStatusIcon={false}
                      label={t('经办人姓名')}
                      status={getStatus(meta, validating)}
                      message={
                        getStatus(meta, validating) === 'error' && meta.error
                      }
                    >
                      <Input {...(input as any)} placeholder={t('请输入')} />
                    </Form.Item>
                  )}
                </Field>
                <Field name="transactor_cr_num" validateFields={[]}>
                  {({ input, meta }) => (
                    <Form.Item
                      showStatusIcon={false}
                      label={t('经办人身份证号')}
                      status={getStatus(meta, validating)}
                      message={
                        getStatus(meta, validating) === 'error' && meta.error
                      }
                    >
                      <Input {...(input as any)} placeholder={t('请输入')} />
                    </Form.Item>
                  )}
                </Field>
                <Field name="status" validateFields={[]}>
                  {({ input, meta }) => (
                    <Form.Item
                      showStatusIcon={false}
                      label={t('报备状态')}
                      status={getStatus(meta, validating)}
                      message={
                        getStatus(meta, validating) === 'error' && meta.error
                      }
                    >
                      <Select
                        clearable
                        {...(input as any)}
                        type="simulate"
                        appearance="button"
                        options={signReportStatus}
                        placeholder={t('请选择')}
                        size="s"
                        autoComplete="off"
                      />
                    </Form.Item>
                  )}
                </Field>
                <div style={{ display: 'inline-block' }}>
                  <Button
                    type="primary"
                    loading={state.loading}
                    htmlType="submit"
                    style={{ marginLeft: 5 }}
                  >
                    {t('查询')}
                  </Button>
                </div>
              </Form>
            </form>
          );
        }}
      </FinalForm>
      <Table
        bordered
        disableTextOverflow
        records={list}
        recordKey="report_id"
        columns={columns}
        addons={[
          autotip({
            isLoading: state.loading,
          }),
          pageable({
            recordCount: total,
            onPagingChange: (query) =>
              setPagination({
                page_index: query.pageIndex,
                page_size: query.pageSize,
              }),
          }),
          selectable({
            onChange: (selectedKeys, context) => {
              setSelected(context.selectedRecords);
            },
          }),
        ]}
      />
    </>
  );
};

export default SignReportManage;
