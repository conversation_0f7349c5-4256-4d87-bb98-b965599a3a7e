import { useDialog, DialogRef } from '@src/utils/react-use/useDialog';
import {
  Modal,
  Button,
  Form,
  Input,
  Select,
  Radio,
  SelectMultiple,
} from '@tencent/tea-component';
import { Form as FinalForm, Field } from 'react-final-form';
import { getStatus } from '@src/utils/form/getStatus';
import { t } from '@src/utils/i18n';
import _ from 'lodash';
import { app } from '@src/utils/tea/app';
import { validateRequired } from '@src/utils/validateFn';
import {
  addReportFailureReason,
  editReportFailureReason,
} from '@src/apiV2/sign';
import { useMemo } from 'react';
import useFetchFailureReason from '@src/global-components/useFetch/useFetchFailureReason';
import {
  SIGN_REPORT_STATUS_REPORT_FAIL,
  SIGN_REPORT_STATUS_STOP_USE,
  SIGN_REPORT_STATUS_WAIT_ADD_INFO,
  signReportStatus,
  signShow<PERSON>ey<PERSON>,
} from '@src/const/const';
import { allSignApplyColumns } from './const';

interface DialogProps {
  dialogRef: DialogRef;
}

export const assignReportStatusOptions = signReportStatus.filter((el) =>
  [
    SIGN_REPORT_STATUS_WAIT_ADD_INFO,
    SIGN_REPORT_STATUS_STOP_USE,
    SIGN_REPORT_STATUS_REPORT_FAIL,
  ]?.includes(el.value),
);

export const AddReportFailureReason = (props: DialogProps) => {
  const { dialogRef } = props;
  const [visible, setShowState, defaultVal] = useDialog<{
    onSuccess: () => void;
    initValues?: any;
    isEdit?: boolean;
  }>(dialogRef);
  const { onSuccess, initValues, isEdit } = defaultVal;
  const { list } = useFetchFailureReason();

  const _handlerSubmit = async (vals: any) => {
    const res = isEdit
      ? await editReportFailureReason({
          id: initValues.id,
          parent_category: vals.parent_category,
          son_category: vals.son_category,
          report_status: vals.report_status,
          solution: vals.solution,
          wrong_keys: vals.wrong_keys?.join(',') ?? '',
        })
      : await addReportFailureReason({
          params: [
            {
              parent_category: vals.parent_category,
              son_category: vals.son_category,
              report_status: vals.report_status,
              solution: vals.solution,
              wrong_keys: vals.wrong_keys?.join(','),
            },
          ],
        });
    if (res?.code === 0) {
      app.tips.success(isEdit ? t('编辑成功') : t('添加成功'));
      setShowState(false);
      onSuccess();
    }
  };

  const parentCategoryOptions = useMemo(() => {
    const result = _.groupBy(list, 'parent_category');
    return _.map(result, (items, key) => ({
      text: key,
      value: key,
    }));
  }, [list]);

  return (
    <>
      <Modal
        visible={visible}
        size="s"
        caption={isEdit ? t('编辑') : t('新增')}
        onClose={() => setShowState(false)}
      >
        <Modal.Body>
          <FinalForm
            onSubmit={_handlerSubmit}
            initialValuesEqual={(val, oldVal) => {
              return _.isEqual(val, oldVal);
            }}
            initialValues={initValues}
          >
            {({ handleSubmit, validating, submitting, values }) => {
              return (
                <form onSubmit={handleSubmit}>
                  <Form layout="default">
                    <Form.Item label={t('一级分类')}>
                      <Field name="type">
                        {({ input, meta }) => (
                          <>
                            <Radio.Group {...input} disabled={isEdit}>
                              <Radio name="0">{t('选择')}</Radio>
                              <Radio name="1">{t('新增')}</Radio>
                            </Radio.Group>
                          </>
                        )}
                      </Field>
                      <Field name="parent_category" validate={validateRequired}>
                        {({ input, meta }) => (
                          <>
                            <Form.Item
                              showStatusIcon={false}
                              status={getStatus(meta, validating)}
                              message={
                                getStatus(meta, validating) === 'error' &&
                                meta.error
                              }
                            >
                              <div style={{ marginTop: 10, marginLeft: -20 }}>
                                {values.type === '0' ? (
                                  <Select
                                    {...(input as any)}
                                    options={parentCategoryOptions}
                                    appearance="button"
                                    size="m"
                                    disabled={submitting}
                                    searchable
                                    clearable
                                  />
                                ) : (
                                  <Input
                                    {...(input as any)}
                                    placeholder={t('请输入')}
                                    size="m"
                                    disabled={submitting}
                                  />
                                )}
                              </div>
                            </Form.Item>
                          </>
                        )}
                      </Field>
                    </Form.Item>
                    <Field name="son_category" validate={validateRequired}>
                      {({ input, meta }) => (
                        <Form.Item
                          required
                          showStatusIcon={false}
                          label={t('二级分类')}
                          status={getStatus(meta, validating)}
                          message={
                            getStatus(meta, validating) === 'error' &&
                            meta.error
                          }
                        >
                          <Input
                            {...(input as any)}
                            placeholder={t('请输入')}
                            size="m"
                            disabled={submitting}
                          />
                        </Form.Item>
                      )}
                    </Field>
                    <Field name="report_status" validate={validateRequired}>
                      {({ input, meta }) => (
                        <Form.Item
                          required
                          showStatusIcon={false}
                          label={t('场景')}
                          status={getStatus(meta, validating)}
                          message={
                            getStatus(meta, validating) === 'error' &&
                            meta.error
                          }
                        >
                          <SelectMultiple
                            {...(input as any)}
                            placeholder={t('请输入')}
                            size="m"
                            options={assignReportStatusOptions}
                            appearance="button"
                            disabled={submitting}
                          />
                        </Form.Item>
                      )}
                    </Field>
                    <Field name="wrong_keys">
                      {({ input, meta }) => (
                        <Form.Item
                          showStatusIcon={false}
                          label={t('对应字段')}
                          status={getStatus(meta, validating)}
                          message={
                            getStatus(meta, validating) === 'error' &&
                            meta.error
                          }
                        >
                          <SelectMultiple
                            {...(input as any)}
                            placeholder={t('请输入')}
                            size="m"
                            options={signShowKeys?.map((v) => ({
                              value: v,
                              text: _.find(
                                allSignApplyColumns,
                                (c) => c.key === v,
                              )?.header,
                            }))}
                            appearance="button"
                            disabled={submitting}
                          />
                        </Form.Item>
                      )}
                    </Field>
                    <Field name="solution" validate={validateRequired}>
                      {({ input, meta }) => (
                        <Form.Item
                          required
                          showStatusIcon={false}
                          label={t('解决方案')}
                          status={getStatus(meta, validating)}
                          message={
                            getStatus(meta, validating) === 'error' &&
                            meta.error
                          }
                        >
                          <Input.TextArea
                            {...(input as any)}
                            autoComplete="off"
                            size="m"
                          />
                        </Form.Item>
                      )}
                    </Field>
                  </Form>
                  <Form.Action style={{ textAlign: 'center' }}>
                    <Button
                      type="primary"
                      htmlType="submit"
                      loading={submitting}
                    >
                      {t('提交')}
                    </Button>
                  </Form.Action>
                </form>
              );
            }}
          </FinalForm>
        </Modal.Body>
      </Modal>
    </>
  );
};
