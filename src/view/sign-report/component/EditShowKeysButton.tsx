import React, { useEffect, useState } from 'react';
import { t } from '@src/utils/i18n';
import { Bubble, Button, Checkbox } from '@tencent/tea-component';
import _ from 'lodash';
import { allSignApplyColumns } from './const';
import { signShowKeys } from '@src/const/const';

export const EditShowKeysButton = React.memo((props: any) => {
  const { data: row, showKeys, setShowKeys } = props;
  const [visible, setVisible] = useState(false);
  const [checkedVal, setCheckedVal] = useState<any[]>([]);

  useEffect(() => {
    const allCheckBoxs = [...signShowKeys];
    setCheckedVal(
      showKeys[`${row.apply_id}_${row.provider_id}`] || allCheckBoxs,
    );
    setShowKeys({ [`${row.apply_id}_${row.provider_id}`]: allCheckBoxs });
  }, []);

  return (
    <Bubble
      style={{ maxWidth: 600 }}
      visible={visible}
      arrowPointAtCenter
      placement="bottom"
      trigger="click"
      onVisibleChange={setVisible}
      content={
        <Checkbox.Group
          value={checkedVal}
          onChange={(val) => {
            setCheckedVal(val);
            setShowKeys({
              [`${row.apply_id}_${row.provider_id}`]: val,
            });
          }}
        >
          {signShowKeys.map((k) => (
            <Checkbox name={k} key={k}>
              {allSignApplyColumns.find((el) => el.key === k)?.header}
            </Checkbox>
          ))}
        </Checkbox.Group>
      }
    >
      <Button type="link" onClick={() => setVisible(true)}>
        {t('编辑')}
      </Button>
    </Bubble>
  );
});
