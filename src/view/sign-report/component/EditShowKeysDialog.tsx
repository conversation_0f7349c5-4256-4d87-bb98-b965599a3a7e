import { useDialog, DialogRef } from '@src/utils/react-use/useDialog';
import { Modal, Button, Form, Checkbox, message } from '@tencent/tea-component';
import { Form as FinalForm, Field } from 'react-final-form';
import { getStatus } from '@src/utils/form/getStatus';
import { t } from '@src/utils/i18n';
import _ from 'lodash';
import { changeReportShowKeys } from '@src/apiV2/sign';
import { allSignApplyColumns } from './const';
import { signShowKeys } from '@src/const/const';

interface DialogProps {
  dialogRef: DialogRef;
}

export const EditShowKeysDialog = (props: DialogProps) => {
  const { dialogRef } = props;

  const [visible, setShowState, defaultVal] = useDialog<{
    selectedIds: string[];
    reload: () => void;
    initVals: any;
    type?: 'batch';
  }>(dialogRef);
  const { selectedIds, reload, initVals, type } = defaultVal;

  const _handlerSubmit = async (vals: any) => {
    try {
      const res = await changeReportShowKeys({
        report_ids: selectedIds.join(','),
        show_keys: vals.show_keys.join(','),
      });
      if (res.code === 0) {
        message.success({ content: t('操作成功') });
        setShowState(false);
        reload();
      }
    } catch (err) {
      console.log(err);
    }
  };

  return (
    <>
      <Modal
        visible={visible}
        size="m"
        caption={t('补充资料')}
        onClose={() => setShowState(false)}
      >
        <Modal.Body>
          <FinalForm
            onSubmit={_handlerSubmit}
            initialValuesEqual={(val, oldVal) => {
              return _.isEqual(val, oldVal);
            }}
            initialValues={{
              ...initVals,
              show_keys:
                type === 'batch' ? undefined : initVals?.show_keys.split(','),
            }}
          >
            {({ handleSubmit, validating, submitting, values }) => {
              return (
                <form onSubmit={handleSubmit}>
                  <Form layout="default">
                    <Field name="show_keys">
                      {({ input, meta }) => (
                        <Form.Item
                          showStatusIcon={false}
                          label={t('展示位')}
                          status={getStatus(meta, validating)}
                          message={
                            getStatus(meta, validating) === 'error' &&
                            meta.error
                          }
                        >
                          <Checkbox.Group {...input}>
                            {signShowKeys.map((k) => (
                              <Checkbox name={k} key={k}>
                                {
                                  allSignApplyColumns.find((el) => el.key === k)
                                    ?.header
                                }
                              </Checkbox>
                            ))}
                          </Checkbox.Group>
                        </Form.Item>
                      )}
                    </Field>
                  </Form>
                  <Form.Action style={{ textAlign: 'center' }}>
                    <Button
                      type="primary"
                      htmlType="submit"
                      loading={submitting}
                    >
                      {t('提交')}
                    </Button>
                  </Form.Action>
                </form>
              );
            }}
          </FinalForm>
        </Modal.Body>
      </Modal>
    </>
  );
};
