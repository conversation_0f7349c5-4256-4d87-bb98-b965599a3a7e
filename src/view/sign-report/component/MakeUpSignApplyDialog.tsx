import React, { useEffect, useMemo, useRef, useState } from 'react';
import { useDialog, DialogRef } from '@src/utils/react-use/useDialog';
import {
  Modal,
  Button,
  message,
  Justify,
  SearchBox,
  Table,
  Bubble,
  TextArea,
  Form,
} from '@tencent/tea-component';
import { t } from '@src/utils/i18n';
import _ from 'lodash';
import { signReportColumns } from './const';
import { EditShowKeysButton } from './EditShowKeysButton';
import { useAsyncFn, useSetState } from 'react-use';
import { autotip, selectable } from '@tencent/tea-component/lib/table/addons';
import {
  getProviderList,
  getSignAdditionalReportNeedReport,
  getSignApplyList,
  submitSignApply,
} from '@src/apiV2/sign';
import {
  findText,
  getStatusText,
  signApplyStatusOptions,
} from '@src/const/const';
import { smsTypeOptions, signShowKeys } from '@src/const/const';

interface DialogProps {
  dialogRef: DialogRef;
}

export const MakeUpSignDialog = (props: DialogProps) => {
  const { dialogRef } = props;
  const [visible, setShowState, defaultVal] = useDialog<{
    row?: any;
    reload?: () => void;
  }>(dialogRef);
  const { row, reload } = defaultVal;
  const [loading, setLoading] = useState(false);
  const [showKeys, setShowKeys] = useSetState<{
    [key: string]: string[];
  }>({});
  const [selectedRecords, setSelectedRecords] = useState<any[]>([]);
  const [searchKeys, setSearchKeys] = useSetState<{
    provider_id?: string;
    apply_id?: string;
  }>({});
  const [applyIdByHandle, setApplyIdByHandle] = useState<string>('');
  const [bubbleVisible, setBubbleVisible] = useState(false);
  const [allList, setAllList] = useState<any>([]);

  const [list, fetchList] = useAsyncFn(async () => {
    const { data } = await getSignAdditionalReportNeedReport({
      additional_ids: [row?.additional_id],
      additional_type: row?.type,
    });
    const applyInfo = data?.[0]?.data;
    const _list = _.flatMap(applyInfo, (a) => {
      return _.map(a.provider_info_arr, (p) => ({
        ..._.omit(p, ['apply_info']),
        ...p.apply_info,
        apply_id: a.apply_id,
        additional_id: a.additional_id,
        provider_name: p.provider_id,
      }));
    });
    await getProviderName(_list);
    return _list ?? [];
  }, [row]);

  async function getProviderName(list) {
    const ids = list?.map((el) => el.provider_id);
    const { data } = await getProviderList({
      page_index: 1,
      page_size: 1000,
      provider_ids: _.uniq(ids),
    });
    setAllList(
      list?.map((el) => ({
        ...el,
        provider_name: _.find(
          data?.list,
          (p) => p.provider_id === el.provider_id,
        )?.provider_name,
      })),
    );
  }

  const [applyList, fetchApplyList] = useAsyncFn(
    async function () {
      if (!applyIdByHandle.trim()) {
        return;
      }
      const res = await getSignApplyList({
        apply_ids: applyIdByHandle.split('\n').map((el) => Number(el)),
      });
      setAllList(_.uniqBy([...res.data.list, ...allList], 'apply_id'));
      setBubbleVisible(false);
      return res.data.list ?? [];
    },
    [allList, applyIdByHandle],
  );

  const filterList = useMemo(() => {
    return _.isEmpty(searchKeys)
      ? allList
      : allList?.filter((el) =>
          _.every(
            _.keys(_.pickBy(searchKeys, (v) => !!v)),
            (k) => el[k].toString() === searchKeys[k].toString(),
          ),
        );
  }, [allList, searchKeys]);

  const customerCols = [
    {
      header: t('uin'),
      key: 'uin',
    },
    {
      header: t('qappid'),
      key: 'qappid',
    },
  ];

  const providerCols = [
    {
      header: t('短信类型'),
      key: 'sms_type',
      render: (row) => findText(smsTypeOptions, row?.sms_type),
    },
  ];

  const cols = row?.type === 1 ? customerCols : providerCols;
  const _signReportColumns = _.cloneDeep(signReportColumns);
  const status: any = _signReportColumns?.find((el) => el.key === 'status');
  status.render = (row) => getStatusText(signApplyStatusOptions, row.status);

  const columns = [
    { header: t('申请单id'), key: 'apply_id' },
    ...cols,
    ..._.filter(_signReportColumns, (el) => !['report_id'].includes(el.key)),
    {
      header: t('编辑展示位'),
      key: 'operate',
      render: (row) => (
        <EditShowKeysButton
          data={row}
          showKeys={showKeys}
          setShowKeys={setShowKeys}
        />
      ),
    },
  ];

  async function submit() {
    if (selectedRecords.length === 0) {
      message.error({ content: t('请选择待报备的账号') });
      return;
    }
    const params = selectedRecords.map((el) => {
      return {
        apply_id: el.apply_id,
        additional_id: row?.additional_id,
        sign: el.sign,
        provider_info_arr: [
          {
            account: el.account,
            provider_id: el.provider_id,
            supplier_id: el.supplier_id,
            sub_code: el.sub_code || undefined,
            show_keys: showKeys[`${el.apply_id}_${el.provider_id}`]
              ? showKeys[`${el.apply_id}_${el.provider_id}`]?.join(',')
              : signShowKeys?.join(','),
          },
        ],
      };
    });
    setLoading(true);
    try {
      const res = await submitSignApply({
        params,
      });
      if (res.code === 0 && !res.data?.errors?.length) {
        message.success({ content: t('提交成功') });
        reload?.();
        setShowState(false);
        setSelectedRecords([]);
      } else {
        message.error({
          content: t('提交失败【{{attr0}}】', {
            attr0: JSON.stringify(res.msg?.errors[0].msg),
          }),
        });
      }
    } catch (err) {
      console.log(err);
    }
    setLoading(false);
  }

  function close() {
    setShowState(false);
    setSelectedRecords([]);
    setShowKeys({});
    setAllList([]);
    setApplyIdByHandle('');
  }

  useEffect(() => {
    if (visible) {
      fetchList();
    } else {
      close();
    }
  }, [fetchList, visible]);

  return (
    <>
      <Modal
        visible={visible}
        className="proTable"
        size={1100}
        caption={t('可报备账号列表')}
        onClose={close}
        destroyOnClose={true}
      >
        <Modal.Body>
          <Justify
            style={{ marginBottom: 10 }}
            left={
              <>
                <Bubble
                  placement="right-start"
                  trigger="click"
                  visible={bubbleVisible}
                  onVisibleChange={setBubbleVisible}
                  content={
                    <>
                      <Form>
                        <Form.Item label={t('申请单id')}>
                          <TextArea
                            placeholder={t('支持多个，一行一个')}
                            value={applyIdByHandle}
                            onChange={setApplyIdByHandle}
                          ></TextArea>
                        </Form.Item>
                      </Form>
                      <div style={{ textAlign: 'center', marginTop: 10 }}>
                        <Button
                          type="primary"
                          onClick={() => {
                            fetchApplyList();
                          }}
                          loading={applyList.loading}
                        >
                          {t('提交')}
                        </Button>
                      </div>
                    </>
                  }
                >
                  <Button type="primary">{t('新增申请单')}</Button>
                </Bubble>
              </>
            }
            right={
              <>
                <SearchBox
                  placeholder={t('输入申请单id')}
                  onSearch={(val) => {
                    setSearchKeys({ apply_id: val });
                  }}
                />
              </>
            }
          />
          <Table
            disableTextOverflow
            bordered
            recordKey={(row: any) => `${row?.apply_id}_${row?.provider_id}`}
            columns={columns}
            records={filterList}
            addons={[
              selectable({
                onChange: (keys, context) => {
                  setSelectedRecords(context.selectedRecords);
                },
              }),
              autotip({
                isLoading: list.loading,
              }),
            ]}
          />
        </Modal.Body>
        <Modal.Footer>
          <Button type="primary" onClick={() => submit()} loading={loading}>
            {t('提交')}
          </Button>
          <Button onClick={() => setShowState(false)}>{t('取消')}</Button>
        </Modal.Footer>
      </Modal>
    </>
  );
};
