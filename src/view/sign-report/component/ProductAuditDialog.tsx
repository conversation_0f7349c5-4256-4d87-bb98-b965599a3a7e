import { useDialog, DialogRef } from '@src/utils/react-use/useDialog';
import {
  Modal,
  Button,
  Form,
  Input,
  SelectMultiple,
} from '@tencent/tea-component';
import { Form as FinalForm, Field } from 'react-final-form';
import { getStatus } from '@src/utils/form/getStatus';
import { t } from '@src/utils/i18n';
import _ from 'lodash';
import {
  changeSignApplyStatus,
  deleteSignApply,
  getSignReportList,
} from '@src/apiV2/sign';
import { allSignApplyColumns } from './const';
import {
  SIGN_REPORT_APPLY_STATUS_REPORT_FAIL,
  SIGN_REPORT_APPLY_STATUS_WAIT_ADD_INFO,
  SIGN_REPORT_STATUS_REPORT_FAIL,
  SIGN_REPORT_STATUS_WAIT_ADD_INFO,
  signShowKeys,
} from '@src/const/const';
import { validateRequired } from '@src/utils/validateFn';
import ReportFailReasonCascader from '@src/global-components/ReportFailReasonCascader';
import { useCallback, useEffect, useMemo, useState } from 'react';

interface DialogProps {
  dialogRef: DialogRef;
}

export const ProductAuditDialog = (props: DialogProps) => {
  const { dialogRef } = props;
  const [collectFailureReason, setCollectFailureReason] = useState<any[]>([]);

  const [visible, setShowState, defaultVal] = useDialog<{
    row: any;
    type?: string;
    status: number;
    reload: () => void;
    caption?: string;
  }>(dialogRef);
  const { row = {}, type, reload, caption, status } = defaultVal;
  const { apply_id } = row;

  const _handlerSubmit = async (vals: any) => {
    try {
      type === 'delete'
        ? await deleteSignApply({ apply_id, msg: vals.msg })
        : await changeSignApplyStatus({
            apply_id,
            msg: vals.msg,
            status,
            failure_reason_ids: vals.failure_reason_ids
              ?.map((el) => +el[1])
              ?.join(','),
            wrong_keys: vals.wrong_keys?.join(',') || undefined,
          });
      setShowState(false);
      reload();
    } catch (err) {
      console.log(err);
    }
  };

  // 获取失败原因需要映射成报备状态
  const reportStatus = useMemo(() => {
    const maps = new Map([
      [SIGN_REPORT_APPLY_STATUS_REPORT_FAIL, SIGN_REPORT_STATUS_REPORT_FAIL],
      [
        SIGN_REPORT_APPLY_STATUS_WAIT_ADD_INFO,
        SIGN_REPORT_STATUS_WAIT_ADD_INFO,
      ],
    ]);
    return maps.get(status);
  }, [status]);

  const needFailureReason = useMemo(() => {
    return [
      SIGN_REPORT_APPLY_STATUS_WAIT_ADD_INFO,
      SIGN_REPORT_APPLY_STATUS_REPORT_FAIL,
    ].includes(status);
  }, [status]);

  const needWrongKeys = useMemo(() => {
    return [SIGN_REPORT_APPLY_STATUS_WAIT_ADD_INFO].includes(status);
  }, [status]);

  const getReportList = useCallback(async () => {
    const { data } = await getSignReportList({
      page_index: 1,
      page_size: 1000,
      status: reportStatus,
      apply_id: row.apply_id,
    });
    setCollectFailureReason(
      _.flatMap(data?.list, (el) => el.failure_reason_ids?.split(','))?.filter(
        (v) => !!v,
      ),
    );
  }, [reportStatus, row.apply_id]);

  useEffect(() => {
    if (needFailureReason) {
      getReportList();
    }
  }, [getReportList, needFailureReason]);

  useEffect(() => {
    if (visible) {
      setCollectFailureReason([]);
    }
  }, [visible]);

  return (
    <>
      <Modal
        visible={visible}
        size="m"
        caption={
          caption ? caption : type === 'pass' ? t('审核通过') : t('审核驳回')
        }
        onClose={() => setShowState(false)}
      >
        <Modal.Body>
          <FinalForm
            onSubmit={_handlerSubmit}
            initialValuesEqual={(val, oldVal) => {
              return _.isEqual(val, oldVal);
            }}
            initialValues={{ failure_reason_ids: [] }}
          >
            {({ handleSubmit, validating, submitting }) => {
              return (
                <form onSubmit={handleSubmit}>
                  <Form layout="default">
                    {needFailureReason && (
                      <Field
                        name="failure_reason_ids"
                        validateFields={[]}
                        validate={validateRequired}
                      >
                        {({ input, meta }) => (
                          <Form.Item
                            showStatusIcon={false}
                            label={t('原因')}
                            status={getStatus(meta, validating)}
                            message={
                              getStatus(meta, validating) === 'error' &&
                              meta.error
                            }
                          >
                            <ReportFailReasonCascader
                              initVals={collectFailureReason}
                              // value={input.value}
                              status={reportStatus}
                              {...input}
                            ></ReportFailReasonCascader>
                          </Form.Item>
                        )}
                      </Field>
                    )}
                    {needWrongKeys && (
                      <Field
                        name="wrong_keys"
                        validateFields={[]}
                        validate={validateRequired}
                      >
                        {({ input, meta }) => (
                          <Form.Item
                            showStatusIcon={false}
                            label={t('缺失/异常字段')}
                            status={getStatus(meta, validating)}
                            message={
                              getStatus(meta, validating) === 'error' &&
                              meta.error
                            }
                          >
                            <SelectMultiple
                              appearance="button"
                              size="m"
                              {...input}
                              options={signShowKeys?.map((v) => ({
                                value: v,
                                text: _.find(
                                  allSignApplyColumns,
                                  (c) => c.key === v,
                                )?.header,
                              }))}
                              clearable
                            ></SelectMultiple>
                          </Form.Item>
                        )}
                      </Field>
                    )}
                    <Field name="msg">
                      {({ input, meta }) => (
                        <Form.Item
                          showStatusIcon={false}
                          label={t('备注')}
                          status={getStatus(meta, validating)}
                          message={
                            getStatus(meta, validating) === 'error' &&
                            meta.error
                          }
                        >
                          <Input size="full" {...input}></Input>
                        </Form.Item>
                      )}
                    </Field>
                  </Form>
                  <Form.Action style={{ textAlign: 'center' }}>
                    <Button
                      type="primary"
                      htmlType="submit"
                      loading={submitting}
                    >
                      {t('提交')}
                    </Button>
                  </Form.Action>
                </form>
              );
            }}
          </FinalForm>
        </Modal.Body>
      </Modal>
    </>
  );
};
