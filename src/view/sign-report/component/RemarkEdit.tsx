import React, { useState } from 'react';
import { t } from '@src/utils/i18n';
import { ModalForm } from '@tencent/tea-material-pro-form';
import { Button, Icon, message } from '@tencent/tea-component';
import { editSignApply } from '@src/apiV2/sign';

export function RemarkEdit(props: Record<string, any>) {
  const [visible, setVisible] = useState(false);
  const { apply_id, remark } = props.row;

  async function onFinish(values) {
    const { code } = await editSignApply({
      apply_id,
      ...values,
      is_change_status: 0,
    });
    if (code === 0) {
      message.success({ content: t('修改成功') });
      setVisible(false);
      return true;
    }
    return false;
  }

  const fields: any[] = [
    {
      type: 'string',
      name: 'remark',
      title: t('供应商备注'),
      required: true,
      size: 'm',
    },
  ];

  return (
    <>
      <ModalForm
        visible={visible}
        title={t('编辑供应商备注')}
        fields={fields}
        onVisibleChange={setVisible}
        onFinish={onFinish}
        initialValues={{ remark }}
      ></ModalForm>
      <Button type="link" onClick={() => setVisible(true)}>
        <Icon type="pencil" />
      </Button>
    </>
  );
}
