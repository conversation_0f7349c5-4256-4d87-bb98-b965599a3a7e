import React, { useState, useMemo, useRef } from 'react';
import { Text, message, But<PERSON>, Modal } from '@tencent/tea-component';
import { t } from '@src/utils/i18n';
import { useHistory } from 'react-router-dom';
import { signReportColumns } from './const';
import { ActionType, ProTable } from '@tencent/tea-material-pro-table';
import {
  changeSignReportStatus,
  getReportFailureReason,
  getSignReportList,
  sendReportEmail,
} from '@src/apiV2/sign';
import useAsyncRetryFunc from '@src/utils/react-use/useAsyncFunc';
import _ from 'lodash';
import { getPurchaseSupplier } from '@src/apiV2/getPurchaseSupplier';
import { EditShowKeysDialog } from './EditShowKeysDialog';
import { useDialogRef } from '@src/utils/react-use/useDialog';
import {
  SIGN_REPORT_STATUS_INNER_STOP_USE,
  SIGN_REPORT_STATUS_REPORT_FAIL,
  SIGN_REPORT_STATUS_REPORT_SUCC,
  SIGN_REPORT_STATUS_REPORTING,
  SIGN_REPORT_STATUS_STOP_USE,
  SIGN_REPORT_STATUS_WAIT_ADD_INFO,
  SIGN_REPORT_STATUS_WAIT_REPORT,
} from '@src/const/const';

interface Props {
  record?: any;
  onSelectedChange?: (keys: string[]) => void;
}

export const SignReportList = (props: Props) => {
  const { record, onSelectedChange } = props;
  const history = useHistory();
  const isAdditional = history.location.pathname?.includes('additional');
  const [selectedKeys, setSelectedKeys] = useState<string[]>([]);
  const actionRef = useRef<ActionType>();
  const dialogRef = useDialogRef();

  const { value: failureReason } = useAsyncRetryFunc(async () => {
    const result = await getReportFailureReason({
      page_index: 1,
      page_size: 1000,
    });
    return result?.data;
  }, []);

  const failureReasonList = useMemo(() => {
    return failureReason?.list ?? [];
  }, [failureReason]);

  // 获取全量供应商列表
  const { value: supplierList } = useAsyncRetryFunc(async () => {
    const result = await getPurchaseSupplier({
      page_index: 1,
      page_size: 1000,
    });
    return result?.data?.list;
  }, []);

  const columns: any[] = useMemo(() => {
    const supplier: any =
      _.find(signReportColumns, (el) => el.key === 'supplier_id') ?? {};
    const failure_reason_ids: any =
      _.find(signReportColumns, (el) => el.key === 'failure_reason_ids') ?? {};
    supplier.render = (row) => {
      return _.find(supplierList, (el) => el.supplier_id === row.supplier_id)
        ?.name;
    };

    failure_reason_ids.render = (row) => {
      const reason = _.map(
        row.failure_reason_ids?.split(','),
        (r) => _.find(failureReasonList, (f) => +f.id === +r)?.son_category,
      )?.join(';');
      return reason || '-';
    };

    return [
      ...signReportColumns,
      {
        key: '',
        header: t('操作'),
        render: (row) => (
          <>
            {[
              SIGN_REPORT_STATUS_WAIT_REPORT,
              SIGN_REPORT_STATUS_REPORTING,
              SIGN_REPORT_STATUS_STOP_USE,
              SIGN_REPORT_STATUS_WAIT_ADD_INFO,
            ].includes(row.status) && (
              <Button type="link" onClick={() => sendEmail(row)}>
                <Text>{t('催单')}</Text>
              </Button>
            )}
            {[
              SIGN_REPORT_STATUS_REPORT_FAIL,
              SIGN_REPORT_STATUS_STOP_USE,
            ].includes(row.status) && (
              <Button
                type="link"
                onClick={() => changeStatus(row, SIGN_REPORT_STATUS_REPORTING)}
              >
                <Text>{t('重新报备')}</Text>
              </Button>
            )}
            {row.status === SIGN_REPORT_STATUS_REPORT_SUCC && (
              <Button
                type="link"
                onClick={() =>
                  changeStatus(row, SIGN_REPORT_STATUS_INNER_STOP_USE)
                }
              >
                <Text theme="danger">{t('停用')}</Text>
              </Button>
            )}
            {row.status === SIGN_REPORT_STATUS_INNER_STOP_USE && (
              <Button
                type="link"
                onClick={() => changeStatus(row, SIGN_REPORT_STATUS_REPORTING)}
              >
                <Text theme="success">{t('启用')}</Text>
              </Button>
            )}
            {row.status === SIGN_REPORT_STATUS_WAIT_ADD_INFO && (
              <Button
                type="link"
                onClick={() => {
                  dialogRef.current?.open({
                    selectedIds: [row.report_id],
                    reload: () => actionRef.current?.reload(),
                    initVals: { show_keys: row.show_keys },
                  });
                }}
              >
                {t('补充资料')}
              </Button>
            )}
          </>
        ),
      },
    ];
  }, [dialogRef, failureReasonList, supplierList]);

  async function changeStatus(row, status) {
    const yes = await Modal.confirm({
      message: t('确认此操作吗？'),
      okText: t('确认'),
      cancelText: t('取消'),
    });
    if (yes) {
      try {
        const res = await changeSignReportStatus({
          params: [
            {
              report_id: row.report_id,
              status,
            },
          ],
        });
        if (res.code === 0) {
          message.success({ content: t('操作成功') });
          actionRef.current?.reload();
        } else {
          message.error({ content: res.msg });
        }
      } catch (err) {
        message.error({ content: t('操作失败') });
        console.log(err);
      }
    }
  }

  async function sendEmail(row) {
    try {
      const res = await sendReportEmail({ report_ids: [row.report_id] });
      if (res.code === 0) {
        message.success({ content: t('催单成功') });
      }
    } catch (err) {}
  }

  // const sideBarWidth = useMemo(() => {
  //   console.log(document.querySelector('.tea-layout__sidebar')?.clientWidth);
  //   return document.querySelector('.tea-layout__sidebar')?.clientWidth;
  // }, []);

  return (
    <>
      <ProTable
        actionRef={actionRef}
        disableTextOverflow
        bordered
        recordKey="report_id"
        request={async (params) => {
          const _params = isAdditional
            ? {
                additional_id: record.additional_id,
              }
            : {
                apply_id: record.apply_id,
              };
          const { data } = await getSignReportList({
            ..._params,
            page_size: 1000,
          });
          const { list } = data;
          return {
            data: list ?? [],
            success: true,
            total: (list ?? [])?.length,
          };
        }}
        columns={columns}
        addons={[
          {
            type: 'selectable',
            value: selectedKeys,
            // rowSelectable: (rowKey, { record }) => record.status === 5,
            onChange: (keys, context) => {
              setSelectedKeys(keys);
              onSelectedChange?.(context?.selectedRecords);
            },
            rowSelect: true,
          },
        ]}
      />
      <EditShowKeysDialog dialogRef={dialogRef}></EditShowKeysDialog>
    </>
  );
};
