import React, { useEffect, useState } from 'react';
import { t } from '@src/utils/i18n';
import { Bubble, Button, StatusTip, message } from '@tencent/tea-component';
import { useAsyncFn } from 'react-use';
import { getSignApplyList, getSignReportList } from '@src/apiV2/sign';
import { flattenSignAttachColumns } from './const';
import { isOutSide } from '@src/const/envConst';

export const ViewAttachmentButton = (props: any) => {
  const { data: row } = props;
  const [visible, setVisible] = useState(false);

  const [state, fetchAttachment] = useAsyncFn(async (): Promise<any> => {
    try {
      const { code, data } = isOutSide
        ? await getSignReportList({ apply_id: row?.apply_id })
        : await getSignApplyList({
            apply_ids: [row?.apply_id],
          });
      if (code !== 0) {
        return message.error({ content: t('获取附件失败') });
      }
      return data?.list[0];
    } catch (err) {
      console.log(err);
    }
  }, [row]);

  useEffect(() => {
    visible && fetchAttachment();
  }, [fetchAttachment, visible]);

  return (
    <Bubble
      style={{ maxWidth: 600 }}
      visible={visible}
      arrowPointAtCenter
      placement="auto"
      trigger="click"
      onVisibleChange={setVisible}
      content={
        state.loading ? (
          <StatusTip status="loading"></StatusTip>
        ) : (
          flattenSignAttachColumns.map((el) => <p>{el.render(row)}</p>)
        )
      }
    >
      <Button type="link" onClick={() => setVisible(true)}>
        {t('查看附件')}
      </Button>
    </Bubble>
  );
};
