import {
  Button,
  Modal,
  StatusTip,
  TabPanel,
  Table,
  Tabs,
  Text,
  Input,
} from '@tencent/tea-component';
import { t } from '@src/utils/i18n';
import {
  findText,
  getStatusText,
  SIGN_TYPE_APP,
  SIGN_TYPE_TRADEMARK,
  signApplyStatusOptions,
  smsTypeOptions,
  signReportStatus,
  idCardType,
  signShowKeys,
} from '@src/const/const';
import _ from 'lodash';
import { pageable } from '@tencent/tea-component/lib/table/addons';
import { isOutSide } from '@src/const/envConst';
import { ViewAttachmentButton } from './ViewAttachmentButton';

const { TextArea } = Input;

export const getExtRender = (ext, status, type) => {
  const _ext = _.orderBy(ext, ['time'], ['desc']);
  const columns = [
    {
      header: type === 'hasCustomerReply' ? t('内部备注') : t('备注'),
      key: 'msg',
      width: 200,
    },
    {
      header: t('状态'),
      key: 'status',
      render: (row: { msg: string; status: number }) =>
        getStatusText(status, row?.status),
    },
    {
      header: t('时间'),
      key: 'time',
    },
    {
      header: t('操作人'),
      key: 'rtx_name',
    },
  ];
  const c = _.cloneDeep(columns);
  c.splice(1, 0, {
    header: t('to客户备注'),
    key: 'reply',
    width: 200,
  });

  return (
    <Table
      compact
      bordered
      style={{ marginTop: 10 }}
      disableTextOverflow
      records={_ext ?? []}
      topTip={!_ext?.length ? <StatusTip status="empty" /> : null}
      addons={[pageable()]}
      columns={type === 'hasCustomerReply' ? c : columns}
    />
  );
};
/* eslint-enable @tencent/tea-i18n/no-bare-zh-in-js */

const emptyText = isOutSide ? '-' : null;

export const flattenSignAttachColumns = [
  {
    key: 'registration_url',
    header: t('ICP备案截图/商标备案截图'),
    width: 80,
    size: 's',
    render: (row) =>
      (row.remark_type === SIGN_TYPE_APP ||
        row.remark_type === SIGN_TYPE_TRADEMARK) &&
      row.registration_url ? (
        <a href={row.registration_url} target="_blank" rel="noreferrer">
          {t('ICP备案截图/商标备案截图')}
        </a>
      ) : (
        emptyText
      ),
  },
  {
    key: 'attach_url',
    header: t('应用商店下载页截图/商标注册书'),
    width: 80,
    size: 's',
    render: (row) =>
      (row.remark_type === SIGN_TYPE_APP ||
        row.remark_type === SIGN_TYPE_TRADEMARK) &&
      row.attach_url ? (
        <a href={row.attach_url} target="_blank" rel="noreferrer">
          {t('{{text}}', {
            text:
              row.remark_type === SIGN_TYPE_APP
                ? t('应用商店下载页截图')
                : t('商标注册书'),
          })}
        </a>
      ) : (
        emptyText
      ),
  },
  {
    key: 'company_file_url',
    width: 100,
    header: t('企业资质证明'),
    hideForm: true,
    render: (row) =>
      row.company_file_url ? (
        <>
          <a href={row.company_file_url} target="_blank" rel="noreferrer">
            {t('企业资质证明')}
          </a>
        </>
      ) : (
        emptyText
      ),
  },
  {
    key: 'corp_idcard_url',
    width: 100,
    header: t('企业法人身份证照片正面'),
    hideForm: true,
    render: (row) =>
      row.corp_idcard_url ? (
        <>
          <a href={row.corp_idcard_url} target="_blank" rel="noreferrer">
            {t('法人身份证照片正面')}
          </a>
          <br />
        </>
      ) : (
        emptyText
      ),
  },
  {
    key: 'corp_idcard_back_url',
    width: 100,
    header: t('企业法人身份证照片反面'),
    hideForm: true,
    render: (row) =>
      row.corp_idcard_back_url ? (
        <>
          <a href={row.corp_idcard_back_url} target="_blank" rel="noreferrer">
            {t('法人身份证照片反面')}
          </a>
          <br />
        </>
      ) : (
        emptyText
      ),
  },
  {
    key: 'transactor_idcard_url',
    width: 100,
    header: t('经办人身份证照片正面'),
    hideForm: true,
    render: (row) =>
      row.transactor_idcard_url ? (
        <>
          <a href={row.transactor_idcard_url} target="_blank" rel="noreferrer">
            {t('经办人身份证照片正面')}
          </a>
          <br />
        </>
      ) : (
        emptyText
      ),
  },
  {
    key: 'transactor_idcard_back_url',
    width: 100,
    header: t('经办人身份证照片反面'),
    hideForm: true,
    render: (row) =>
      row.transactor_idcard_back_url ? (
        <>
          <a
            href={row.transactor_idcard_back_url}
            target="_blank"
            rel="noreferrer"
          >
            {t('经办人身份证照片反面')}
          </a>
          <br />
        </>
      ) : (
        emptyText
      ),
  },
  {
    key: 'transactor_photo_idcard_url',
    width: 100,
    header: t('经办人手持身份证照片'),
    hideForm: true,
    render: (row) =>
      row.transactor_photo_idcard_url ? (
        <>
          <a
            href={row.transactor_photo_idcard_url}
            target="_blank"
            rel="noreferrer"
          >
            {t('经办人身份证照片反面')}
          </a>
          <br />
        </>
      ) : (
        emptyText
      ),
  },
  {
    key: 'commitment_letter_url',
    width: 100,
    header: t('承诺函'),
    hideForm: true,
    render: (row) =>
      row.commitment_letter_url ? (
        <>
          <a href={row.commitment_letter_url} target="_blank" rel="noreferrer">
            {t('承诺函')}
          </a>
        </>
      ) : (
        emptyText
      ),
  },
  {
    key: 'authorization_letter_url',
    width: 100,
    header: t('授权函'),
    hideForm: true,
    render: (row) =>
      row.authorization_letter_url ? (
        <>
          <a
            href={row.authorization_letter_url}
            target="_blank"
            rel="noreferrer"
          >
            {t('授权函')}
          </a>
        </>
      ) : (
        emptyText
      ),
  },
  {
    key: 'other_attach_url',
    width: 100,
    header: t('其他附件'),
    hideForm: true,
    render: (row) =>
      row.other_attach_url ? (
        <>
          <a href={row.other_attach_url} target="_blank" rel="noreferrer">
            {t('其他附件')}
          </a>
        </>
      ) : (
        emptyText
      ),
  },
];

export const signApplyColumns: Array<any> = [
  {
    key: 'apply_id',
    header: t('申请单ID'),
    width: 80,
    size: 's',
  },
  {
    key: 'uin',
    header: t('客户uin'),
    width: 110,
    size: 's',
    render: (row) => row.uin || '_',
  },
  {
    key: 'qappid',
    header: t('客户qappid'),
    width: 100,
    size: 's',
    render: (row) => row.qappid || '_',
  },
  {
    key: 'sms_type',
    header: t('短信类型'),
    width: 70,
    hideForm: true,
    size: 's',
    render: (row) => findText(smsTypeOptions, row.sms_type),
  },
  {
    key: 'remark_type',
    header: t('签名类型'),
    width: 100,
    hideForm: true,
    size: 's',
    render: (row) => row.remark_type || '_',
  },
  {
    key: 'sign',
    header: t('签名内容'),
    width: 100,
    size: 's',
    type: 'string',
    component: ({ value, onChange }) => {
      return (
        <TextArea
          onChange={onChange}
          value={value}
          rows={4}
          placeholder={t('换行输入多个')}
          style={{ marginTop: -5 }}
        ></TextArea>
      );
    },
    render: (row) => row.sign || '_',
  },
  {
    key: 'registration_num',
    header: t(' ICP备案或许可证号/商标号'),
    width: 80,
    size: 's',
    render: (row) =>
      row.remark_type === SIGN_TYPE_APP ||
      row.remark_type === SIGN_TYPE_TRADEMARK
        ? row.registration_num
        : '-',
  },
  {
    key: 'app_resource_url',
    header: t('应用商店下载链接'),
    width: 80,
    size: 's',
    hideForm: true,
    render: (row) =>
      row.remark_type === SIGN_TYPE_APP ? (
        isOutSide ? (
          row.app_resource_url
        ) : (
          <Text overflow>{row.app_resource_url || '-'}</Text>
        )
      ) : (
        '-'
      ),
  },
  {
    key: 'sign_attach',
    header: t('签名材料'),
    width: 150,
    size: 's',
    hideForm: true,
    render: (row) => <ViewAttachmentButton data={row}></ViewAttachmentButton>,
    // 展开被合并的列字段
    flattenColumns: flattenSignAttachColumns,
  },
  {
    key: 'company_name',
    width: 100,
    header: t('公司名称'),
  },
  {
    key: 'unisc_id',
    width: 100,
    header: t('统一社会信用代码'),
  },
  {
    key: 'corp_cr_type',
    width: 100,
    header: t('企业法人证件类型'),
    hideForm: true,
    render: (row) => findText(idCardType, row.corp_cr_type),
  },
  {
    key: 'corp_name',
    width: 100,
    header: t('企业法人姓名'),
  },
  {
    header: t('企业法人身份证号'),
    key: 'corp_cr_num',
    width: 100,
    title: t('企业法人身份证号'),
  },

  {
    key: 'transactor_cr_type',
    width: 100,
    header: t('经办人证件类型'),
    hideForm: true,
    render: (row) => findText(idCardType, row.transactor_cr_type),
  },
  {
    key: 'transactor_name',
    width: 100,
    header: t('经办人姓名'),
  },

  {
    key: 'transactor_cr_num',
    width: 100,
    header: t('经办人身份证号'),
    title: t('经办人身份证号'),
  },

  {
    key: 'transactor_phone',
    width: 100,
    header: t('经办人手机号'),
    render: (row) => row.transactor_phone || '-',
  },

  {
    key: 'failure_reason_ids',
    width: 100,
    header: t('失败原因'),
    hideForm: true,
    render: () => {},
  },
  {
    key: 'wrong_keys',
    width: 100,
    header: t('缺失/异常字段'),
    hideForm: true,
    render: (row) => getHeaderByKey(row.wrong_keys),
  },
  {
    key: 'remark',
    width: 100,
    header: t('备注'),
    hideForm: true,
  },
  // /* eslint-disable @tencent/tea-i18n/no-bare-zh-in-js */
  // {
  //   key: '_ext',
  //   header: '操作日志',
  //   hideForm: true,
  //   width: 100,
  //   render: (row) => (
  //     <Button
  //       type="link"
  //       onClick={() =>
  //         Modal.alert({
  //           message: '操作历史',
  //           size: 'l',
  //           description: (
  //             <Tabs
  //               tabs={[
  //                 { id: 'inside', label: '内部操作日志' },
  //                 { id: 'outside', label: '供应商操作日志' },
  //               ]}
  //               placement="top"
  //             >
  //               <TabPanel id="inside">
  //                 {getExtRender(
  //                   row?.ext?.inside_record,
  //                   signApplyStatusOptions,
  //                   'hasCustomerReply',
  //                 )}
  //               </TabPanel>
  //               <TabPanel id="outside">
  //                 {getExtRender(
  //                   row?.ext?.outside_record,
  //                   signApplyStatusOptions,
  //                   'noCustomerReply',
  //                 )}
  //               </TabPanel>
  //             </Tabs>
  //           ),
  //         })
  //       }
  //     >
  //       {t('操作历史')}
  //     </Button>
  //   ),
  // },
  {
    key: 'created_at',
    header: t('创建时间'),
    width: 90,
    hideForm: true,
  },
  {
    key: 'updated_at',
    header: t('更新时间'),
    width: 90,
    hideForm: true,
  },
  {
    key: 'status',
    width: 100,
    header: t('状态'),
    fixed: 'right',
    appearance: 'button',
    size: 's',
    component: 'select',
    clearable: true,
    options: signApplyStatusOptions,
    render: (row) => getStatusText(signApplyStatusOptions, row.status),
    exportRender: (row) =>
      signApplyStatusOptions.find(
        (el) => el.value.toString() === row.status?.toString(),
      )?.text,
  },
];

export const allSignApplyColumns = _.flatMap(signApplyColumns, (el) => {
  if (el.flattenColumns?.length > 0) {
    return el.flattenColumns;
    // return ...el.flattenColumns
  } else {
    return el;
  }
});

/* eslint-disable @tencent/tea-i18n/no-bare-zh-in-js */

export const getHeaderByKey = (keys) => {
  return (
    _.map(
      keys?.split(','),
      (k) => _.find(allSignApplyColumns, (c) => c.key === k)?.header,
    )?.join(',') || '-'
  );
};

export const signReportColumns = [
  {
    key: 'report_id',
    header: '报备单ID',
  },
  {
    key: 'supplier_id',
    header: '供应商名称',
  },
  {
    key: 'provider_name',
    header: 'provider_name',
  },
  {
    key: 'account',
    header: 'account',
  },
  {
    key: 'sub_code',
    header: '签名子码',
  },
  {
    key: 'status',
    header: '报备状态',
    render: (row) => getStatusText(signReportStatus, row.status),
  },
  {
    key: 'failure_reason_ids',
    header: '失败原因',
    render: (row) => {},
  },
  {
    key: 'wrong_keys',
    header: '缺失/异常字段',
    render: (row) => getHeaderByKey(row.wrong_keys),
  },
  {
    key: 'ext',
    header: '备注',
    render: (row) => getExtMsg(row?.ext?.outside_record) || '-',
  },
  {
    key: 'created_at',
    header: '创建时间',
  },
  {
    key: '_ext',
    header: '操作日志',
    render: (row) => (
      <Button type="link" onClick={() => getInnerExtMsg(row)}>
        {t('操作历史')}
      </Button>
    ),
  },
];

/* eslint-enable @tencent/tea-i18n/no-bare-zh-in-js */

export const getExtMsg = (ext = [], status?) => {
  const _record = _.isNil(status)
    ? ext
    : ext.filter((el: any) => el.status.toString() === status.toString());
  const _ext: { msg?: string; status?: number } = _.last(_record) || {};
  return _ext?.msg;
};

export const getInnerExtMsg = (row) => {
  return Modal.alert({
    message: t('操作历史'),
    size: 'l',
    description: (
      <Tabs
        tabs={[
          { id: 'inside', label: t('内部操作日志') },
          { id: 'outside', label: t('供应商操作日志') },
        ]}
        placement="top"
      >
        <TabPanel id="inside">
          {getExtRender(
            row?.ext?.inside_record,
            signReportStatus,
            'noCustomerReply',
          )}
        </TabPanel>
        <TabPanel id="outside">
          {getExtRender(
            row?.ext?.outside_record,
            signReportStatus,
            'noCustomerReply',
          )}
        </TabPanel>
      </Tabs>
    ),
  });
};

export const groupSupplierAccountOptions = (data) => {
  return _.reduce(
    data,
    (
      result: Array<{
        label: string;
        value: string;
        children: any[];
      }>,
      cur,
    ) => {
      const index = result.findIndex(
        (el: any) => el.value.toString() === cur.supplier_id.toString(),
      );
      if (index === -1) {
        result.push({
          label: `${cur.supplier_name}(${cur.supplier_id})` ?? '',
          value: cur.supplier_id.toString() ?? '',
          children: [
            {
              label: `${cur.account_name}(${cur.account_id})`,
              value: cur.account_id.toString(),
            },
          ],
        });
      } else {
        result[index].children.push({
          label: `${cur.account_name}(${cur.account_id})`,
          value: cur.account_id.toString(),
        });
      }
      return result;
    },
    [],
  );
};

export function getSignShowKeys(detail) {
  const keysToRemove: string[] = [];

  if (
    detail?.remark_type !== SIGN_TYPE_APP &&
    detail?.remark_type !== SIGN_TYPE_TRADEMARK
  ) {
    keysToRemove.push('registration_num', 'registration_url', 'attach_url');
  }

  if (detail?.remark_type !== SIGN_TYPE_APP) {
    keysToRemove.push('app_resource_url');
  }

  return _.difference(_.cloneDeep(signShowKeys), keysToRemove);
}
