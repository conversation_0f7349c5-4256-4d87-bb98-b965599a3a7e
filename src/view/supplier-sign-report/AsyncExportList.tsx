import { useEffect, useMemo } from 'react';
import { Button, Col, Form, Modal, Table, Text } from '@tencent/tea-component';
import { t } from '@src/utils/i18n';
import { useDialogRef } from '@src/utils/react-use/useDialog';
import { useAsyncFn } from 'react-use';
import _ from 'lodash';
import { useTableTopTip } from '@src/global-components/table-top-tip/useTableTopTip';
import { getExportProgessList } from '@src/api/getExportProgressList';
import { signApplyColumns } from '../sign-report/component/const';
import { AsyncExportTaskInfoDialog } from './component/AsyncExportTaskInfoDialog';
import { findText, signReportStatus } from '@src/const/const';
import { render } from 'react-dom';

const AsyncTaskList = (props: { activeId: string }) => {
  const dialogRef = useDialogRef();
  const { activeId } = props;

  const [state, fetchList] = useAsyncFn(async () => {
    const response = await getExportProgessList({});
    return response?.data;
  }, []);

  const list = useMemo(() => {
    return (
      _.entries(state?.value)
        ?.map(([k, v]: any) => {
          const p = JSON.parse(v?.all_params ?? '{}') ?? {};
          return {
            ...(v ?? {}),
            ...p,
            progress_key: k,
          };
        })
        ?.sort((a: any, b: any) => {
          if (a.created_at && b.created_at) {
            return b.created_at.localeCompare(a.created_at);
          }
          return 0;
        }) ?? []
    );
  }, [state?.value]);

  const TableTopTip = useTableTopTip({
    record: list,
    loading: state.loading,
  });

  useEffect(() => {
    if (activeId === 'async_export_task') {
      fetchList();
    }
  }, [activeId, fetchList]);

  function renderInfo(info: any) {
    const o = _.filter(
      signApplyColumns?.concat([
        {
          header: t('创建日期范围'),
          key: 'from_time',
          render: (row) => `${row.from_time} - ${row.to_time}`,
        },
      ]),
      (box) => Object.keys(info).includes(box.key),
    );
    return _.map(o, (item) => {
      const text = item.render ? item.render(info) : info[item.key];
      return (
        <Col span={8} key={item.key}>
          <Form.Item label={item?.header}>
            <Form.Text>{text}</Form.Text>
          </Form.Item>
        </Col>
      );
    });
  }

  return (
    <>
      <Table.ActionPanel>
        <Button onClick={fetchList}>{t('刷新')}</Button>
        {/* <Text
          theme="warning"
          style={{ marginLeft: 10, verticalAlign: 'baseline' }}
        >
          {t('仅记录3小时内异步任务')}
        </Text> */}
      </Table.ActionPanel>
      <Table
        bordered
        records={list}
        recordKey="progress_key"
        topTip={TableTopTip}
        columns={[
          {
            key: 'progress_key',
            header: t('任务ID'),
            render: (row: any) => (
              <Text>{row.progress_key?.split('_').at(-1)}</Text>
            ),
          },
          {
            key: 'export_params',
            header: t('导出条件'),
            render: (row: any) => (
              <Button
                type="link"
                onClick={() => {
                  Modal.alert({
                    message: t('导出条件'),
                    description: renderInfo(
                      JSON.parse(row?.all_params ?? '{}'),
                    ),
                  });
                }}
              >
                {t('查看导出条件')}
              </Button>
            ),
          },
          {
            key: 'created_at',
            header: t('创建时间'),
          },
          {
            key: 'status',
            header: t('任务状态'),
            render: (row: any) =>
              row.split_finally_count === row.split_total_count ? (
                <Text theme="success">{t('已完成')}</Text>
              ) : (
                <Text theme="warning">{t('进行中')}</Text>
              ),
          },
          {
            key: 'operation',
            header: t('操作'),
            render: (row) => (
              <Button
                type="link"
                onClick={async () => {
                  dialogRef.current.open({
                    progress_key: row.progress_key,
                  });
                }}
              >
                {t('查看任务详情')}
              </Button>
            ),
          },
        ]}
      />
      <AsyncExportTaskInfoDialog
        dialogRef={dialogRef}
      ></AsyncExportTaskInfoDialog>
    </>
  );
};

export default AsyncTaskList;
