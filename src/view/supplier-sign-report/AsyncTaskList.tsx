import { useCallback, useMemo, useState } from 'react';
import { Button, Table, TabPanel, Tabs, Text } from '@tencent/tea-component';
import { t } from '@src/utils/i18n';
import { useDialogRef } from '@src/utils/react-use/useDialog';
import { useSetState } from 'react-use';
import _ from 'lodash';
import useAsyncRetryFunc from '@src/utils/react-use/useAsyncFunc';
import { useTableTopTip } from '@src/global-components/table-top-tip/useTableTopTip';
import { TaskInfoDialog } from './component/TaskInfoDialog';
import { getAsyncTaskList } from '@src/api/getAsyncTaskList';
import { taskStatusOptions } from '@src/const/const';
import AsyncExportList from './AsyncExportList';
import { useHistory, useParams } from 'react-router-dom';
import { useQuery } from '@src/utils/react-use/useQuery';

const { pageable } = Table.addons;

const AsyncTaskList = (props) => {
  const dialogRef = useDialogRef();
  const histroy = useHistory();
  const [pagination, setPagination] = useSetState({
    page_index: 1,
    page_size: 10,
  });
  const [searchKeys, setSearchKeys] = useState<any>({});
  const params = useQuery();
  const [activeId, setActiveId] = useState(params?.id || 'async_export_task');

  const {
    value: state,
    loading,
    retry,
  } = useAsyncRetryFunc(async () => {
    if (activeId === 'async_export_task') {
      return;
    }
    const response = await getAsyncTaskList({
      command: activeId,
      ...pagination,
      ...searchKeys,
    });
    return response?.data;
  }, [activeId, pagination, searchKeys]);

  const list = useMemo(() => {
    return state ?? [];
  }, [state]);

  const total = useMemo(() => {
    return state?.length ?? 0;
  }, [state]);

  const TableTopTip = useTableTopTip({
    record: list,
    loading: loading,
  });

  // function onSubmit(vals) {
  //   setSearchKeys(vals);
  //   setPagination({
  //     page_index: 1,
  //   });
  // }

  const renderTable = useCallback(() => {
    return (
      <>
        <Table.ActionPanel>
          <Button onClick={retry}>{t('刷新')}</Button>
          <Text
            theme="warning"
            style={{ marginLeft: 10, verticalAlign: 'baseline' }}
          >
            {t('仅记录24小时内异步任务')}
          </Text>
        </Table.ActionPanel>
        <Table
          bordered
          records={list}
          recordKey="task_id"
          topTip={TableTopTip}
          columns={[
            {
              key: 'task_id',
              header: t('任务ID'),
            },
            {
              key: 'task_num',
              header: t('任务总量'),
            },
            {
              key: 'succ_num',
              header: t('成功量'),
            },
            {
              key: 'err_num',
              header: t('失败量'),
            },
            {
              key: 'create_time',
              header: t('创建时间'),
            },
            {
              key: 'status',
              header: t('状态'),
              render: (row: any) => {
                const theme: any =
                  _.find(taskStatusOptions, (v) => v.value === row.status)
                    ?.theme || 'text';
                return (
                  <Text theme={theme}>
                    {
                      _.find(taskStatusOptions, (v) => v.value === row.status)
                        ?.text
                    }
                  </Text>
                );
              },
            },
            {
              key: 'operation',
              header: t('操作'),
              render: (row) => (
                <Button
                  type="link"
                  onClick={async () => {
                    dialogRef.current.open({
                      task_id: row.task_id,
                    });
                  }}
                >
                  {t('查看任务详情')}
                </Button>
              ),
            },
          ]}
          addons={[
            pageable({
              recordCount: total,
              pageIndex: pagination.page_index,
              pageSize: pagination.page_size,
              onPagingChange: (query) =>
                setPagination({
                  page_index: query.pageIndex,
                  page_size: query.pageSize,
                }),
            }),
          ]}
        />
      </>
    );
  }, [
    TableTopTip,
    dialogRef,
    list,
    pagination.page_index,
    pagination.page_size,
    retry,
    setPagination,
    total,
  ]);

  const [tabs] = useState([
    {
      id: 'async_export_task',
      label: t('导出任务'),
    },
    {
      id: 'change_sign_report_status',
      label: t('批量变更需求单状态'),
    },
  ]);

  return (
    <>
      <Tabs
        tabs={tabs}
        defaultActiveId={tabs[0].id}
        animated={false}
        activeId={activeId}
        onActive={(tab) => {
          setActiveId(tab.id);
        }}
      >
        {tabs.map((tab) => (
          <TabPanel id={tab.id} key={tab.id}>
            {activeId === 'async_export_task' ? (
              <AsyncExportList activeId={activeId}></AsyncExportList>
            ) : (
              renderTable()
            )}
          </TabPanel>
        ))}
      </Tabs>
      <TaskInfoDialog dialogRef={dialogRef} />
    </>
  );
};

export default AsyncTaskList;
