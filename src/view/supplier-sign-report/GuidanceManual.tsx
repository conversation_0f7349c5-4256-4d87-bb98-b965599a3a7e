import { useState, useEffect, useRef } from 'react';
import {
  Table,
  Text,
  Card,
  H3,
  List,
  ImagePreview,
} from '@tencent/tea-component';
import { t, Trans } from '@src/utils/i18n';
import statusChanges from './img/statusChanges.png';
import login from './img/login.png';
import supplierManage from './img/supplierManage.png';
import search from './img/search.png';
import exportOp from './img/exportOp.png';
import exportCon from './img/exportCon.png';
import exportConfirm from './img/exportConfirm.png';
import downloadLink from './img/downloadLink.png';
import taskCon from './img/taskCon.png';

const GuidanceManual = () => {
  const [activeSection, setActiveSection] = useState('version-info');
  const [isManualClick, setIsManualClick] = useState(false);
  const [isClicking, setIsClicking] = useState(false);
  const [disableScrollHand<PERSON>, setDisableScrollHandler] = useState(false);
  const [isSmallScreen, setIsSmallScreen] = useState(false); // 新增响应式状态
  const contentRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const checkScreenSize = () => {
      setIsSmallScreen(window.innerWidth < 1500);
    };

    checkScreenSize();

    window.addEventListener('resize', checkScreenSize);

    return () => {
      window.removeEventListener('resize', checkScreenSize);
    };
  }, []);

  // 目录结构
  const menuItems = [
    {
      id: 'version-info',
      title: t('操作手册版本信息'),
      level: 1,
    },
    {
      id: 'status-description',
      title: t('前言、报备状态值说明'),
      level: 1,
    },
    {
      id: 'login-guide',
      title: t('一、登录与进入页面'),
      level: 1,
      children: [
        {
          id: 'login-account',
          title: t('1、登录账号获取'),
          level: 2,
        },
        {
          id: 'enter-system',
          title: t('2、进入系统'),
          level: 2,
        },
      ],
    },
    {
      id: 'search-operation',
      title: t('二、查询操作'),
      level: 1,
    },
    {
      id: 'export-operation',
      title: t('三、导出操作'),
      level: 1,
    },
    {
      id: 'status-modification',
      title: t('四、报备状态修改操作'),
      level: 1,
      children: [
        {
          id: 'single-status-change',
          title: t('（一）页面单个签名变更报备状态'),
          level: 2,
        },
        {
          id: 'batch-status-change',
          title: t('（二）页面批量变更报备状态'),
          level: 2,
        },
        {
          id: 'batch-upload-result',
          title: t('（三）批量上传报备结果'),
          level: 2,
          children: [
            {
              id: 'fill-instructions',
              title: t('填写说明'),
              level: 3,
            },
            {
              id: 'column-relationship',
              title: t('各列关联性说明'),
              level: 3,
            },
          ],
        },
      ],
    },
    {
      id: 'notice',
      title: t('五、注意事项'),
      level: 1,
    },
  ];

  // 点击目录项滚动到对应位置
  const scrollToSection = (sectionId: string) => {
    // 设置点击状态
    setIsClicking(() => true);
    setIsManualClick(() => true);
    // 禁用滚动监听
    setDisableScrollHandler(() => true);

    if (!contentRef.current) {
      setIsClicking(false);
      setDisableScrollHandler(false);
      return;
    }

    const targetElement = document.getElementById(sectionId);
    if (!targetElement) {
      setIsClicking(false);
      setDisableScrollHandler(false);
      return;
    }

    const elementTop = targetElement.offsetTop;
    const container = contentRef.current;
    const containerHeight = container.clientHeight;
    const containerScrollTop = container.scrollTop;

    // 计算期望滚动位置（目标顶部 - 60px偏移）
    const desiredScrollTop = Math.max(0, elementTop - 60);
    // 计算最大可滚动位置
    const maxScrollTop = container.scrollHeight - containerHeight;

    // 判断目标顶部是否在视口内（考虑60px偏移）
    const isInView =
      elementTop >= containerScrollTop + 60 &&
      elementTop <= containerScrollTop + containerHeight;

    // 判断是否已达到容器顶部（考虑5px容差）
    const isAtTop = containerScrollTop <= 5;

    // 判断是否已达到容器底部（考虑5px容差）
    const isAtBottom = containerScrollTop >= maxScrollTop - 5;

    // 立即设置活动状态 - 确保点击时立即高亮
    setActiveSection(sectionId);

    // 计算实际需要滚动的位置
    let scrollToPosition = desiredScrollTop;

    // 如果期望位置超出最大滚动范围，滚动到底部
    if (desiredScrollTop > maxScrollTop) {
      scrollToPosition = maxScrollTop;
    }

    // 判断是否需要滚动
    let shouldScroll = true;

    // 如果目标已在视口内且满足以下条件之一，则不滚动：
    // 1. 已经在顶部且目标在视口内
    // 2. 已经在底部且目标在视口内
    // 3. 当前滚动位置已经是期望位置
    if (
      isInView &&
      (isAtTop ||
        isAtBottom ||
        Math.abs(containerScrollTop - desiredScrollTop) <= 5)
    ) {
      shouldScroll = false;
    }

    // 执行滚动
    if (shouldScroll) {
      container.scrollTo({
        top: scrollToPosition,
        behavior: 'smooth',
      });
    }

    // 统一延迟重置点击状态和手动点击标志（无论是否滚动都保持1000ms）
    setTimeout(() => {
      setIsClicking(false);
      setIsManualClick(false);
      // 不禁用滚动监听，等待用户主动滚动后恢复
    }, 500);
  };

  // 滚动监听，自动高亮当前章节
  useEffect(() => {
    const handleScroll = () => {
      // 如果滚动监听被禁用，直接返回
      if (
        disableScrollHandler ||
        isClicking ||
        !contentRef.current ||
        isManualClick
      )
        return;

      const sections = contentRef.current.querySelectorAll('[id]');
      const scrollTop = contentRef.current.scrollTop;

      let currentSection = '';

      // 从上到下遍历所有章节，找到当前应该高亮的章节
      sections.forEach((section) => {
        const element = section as HTMLElement;
        const elementTop = element.offsetTop;

        // 如果章节顶部位置小于等于当前滚动位置+100px偏移，则认为该章节应该被高亮
        if (elementTop <= scrollTop + 100) {
          currentSection = element.id || '';
        }
      });

      if (currentSection && currentSection !== activeSection) {
        setActiveSection(currentSection);
      }
    };

    // 添加恢复滚动监听的处理器
    const handleScrollToEnable = () => {
      if (disableScrollHandler) {
        setDisableScrollHandler(false);
      }
    };

    const contentElement = contentRef.current;
    if (contentElement) {
      contentElement.addEventListener('scroll', handleScroll);
      // 添加新的滚动监听来恢复处理
      contentElement.addEventListener('scroll', handleScrollToEnable);

      // 初始化时也执行一次
      setTimeout(handleScroll, 100);
      return () => {
        contentElement.removeEventListener('scroll', handleScroll);
        contentElement.removeEventListener('scroll', handleScrollToEnable);
      };
    }
  }, [activeSection, isManualClick, isClicking, disableScrollHandler]); // 添加disableScrollHandler依赖

  // 渲染目录菜单
  const renderMenuItem = (item: any) => {
    const isActive = activeSection === item.id;
    const hasActiveChild = item.children?.some(
      (child: any) =>
        activeSection === child.id ||
        child.children?.some((grand: any) => activeSection === grand.id),
    );

    return (
      <div key={item.id}>
        <div
          onClick={() => {
            // 点击时优先高亮，并且关闭滚动监听
            setActiveSection(item.id);
            setIsClicking(true); // 标记是点击触发
            scrollToSection(item.id);
          }}
          style={{
            padding:
              item.level === 1
                ? '8px 16px'
                : item.level === 2
                ? '6px 32px'
                : '4px 60px',
            cursor: 'pointer',
            fontSize:
              item.level === 1 ? '14px' : item.level === 2 ? '13px' : '12px',
            fontWeight: item.level === 1 ? 'bold' : 'normal',
            color: isActive ? '#006eff' : hasActiveChild ? '#006eff' : '#333',
            backgroundColor: isActive ? '#f0f8ff' : 'transparent',
            borderLeft: isActive
              ? '3px solid #006eff'
              : '3px solid transparent',
            transition: 'all 0.2s ease',
          }}
        >
          {item.title}
        </div>

        {/* 二级或三级目录 */}
        {item.children && (
          <div>{item.children.map((child: any) => renderMenuItem(child))}</div>
        )}
      </div>
    );
  };

  const versionTableColumns = [
    { key: 'version', header: t('版本号') },
    { key: 'date', header: t('更新日期') },
    { key: 'update', header: t('更新点') },
  ];

  const versionTableData = [
    { version: 'v1.0', date: t('2025年7月30日'), update: t('新创建') },
    {
      version: 'v1.1',
      date: t('2025年8月20日'),
      update: t('更新导出操作流程说明'),
    },
  ];

  const statusTableColumns = [
    { key: 'status', header: t('状态值'), width: '150px' },
    { key: 'description', header: t('状态值说明') },
  ];

  const statusTableData = [
    { status: t('待报备'), description: t('还未提交至运营商报备') },
    {
      status: t('报备中'),
      description: t('已提交至运营商报备，但还未返回报备结果'),
    },
    {
      status: t('资料异常'),
      description: t(
        '签名资料不符合要求或者缺失，更新为正确资料或者补全资料后即可完成报备。',
      ),
    },
    {
      status: t('报备成功'),
      description: t('已成功在运营商完成报备，可发送短信，且可保障成功率'),
    },
    {
      status: t('报备失败'),
      description: (
        <span>
          <Trans>
            <Text theme="danger" style={{ fontWeight: 'bold' }}>
              ⚠️谨慎使用！！！
            </Text>
            <br />
            即使更换资料或者补全资料也无法完成报备，如公司经营状态异常、签名内容不合规等。
          </Trans>
        </span>
      ),
    },
    {
      status: t('停用'),
      description: (
        <span>
          <Trans>
            <Text theme="danger" style={{ fontWeight: 'bold' }}>
              ⚠️谨慎使用！！！会影响客户现网使用
            </Text>
            <br />
            签名完全不能发送且无法在短时间内切换备用通道时选择，如签名长时间未使用被运营商清理或者通道资源被下架完全无兜底资源可使用时
          </Trans>
        </span>
      ),
    },
    {
      status: t('客户停用'),
      description: t(
        '该状态值为客户侧主动关停签名的状态同步，仅可读，不属于可操作值。',
      ),
    },
  ];

  return (
    <div style={{ display: 'flex', height: '85vh', backgroundColor: '#fff' }}>
      {/* 左侧目录 */}
      <div
        style={{
          width: '280px',
          backgroundColor: '#fafafa',
          borderRight: '1px solid #e8e8e8',
          overflow: 'auto',
          height: '100%',
        }}
      >
        <div style={{ padding: '16px 0' }}>
          <div
            style={{
              padding: '0 16px 16px',
              borderBottom: '1px solid #e8e8e8',
              marginBottom: '16px',
            }}
          >
            <Text style={{ fontSize: '16px', fontWeight: 'bold' }}>
              {t('操作手册目录')}
            </Text>
          </div>
          <div>{menuItems.map((item) => renderMenuItem(item))}</div>
        </div>
      </div>

      {/* 右侧内容 */}
      <div
        style={{
          flex: 1,
          display: 'flex',
          justifyContent: 'center',
        }}
      >
        <div
          style={{
            display: 'flex',
            flexDirection: 'column',
            width: isSmallScreen ? '100%' : '60%', // 修改为响应式宽度
            height: '100%',
          }}
        >
          <div
            ref={contentRef}
            style={{
              flex: 1,
              overflow: 'auto',
              padding: '20px',
            }}
          >
            <Card>
              <Card.Body>
                <div style={{ fontSize: '26px', fontWeight: 'bold' }}>
                  {t('腾讯云短信供应商报备签名平台操作手册V1.1')}
                </div>

                <div id="version-info">
                  <div
                    style={{
                      margin: '10px 0',
                      fontSize: '22px',
                      fontWeight: 'bold',
                    }}
                  >
                    {t('操作手册版本信息')}
                  </div>
                  <Table
                    bordered
                    compact
                    records={versionTableData}
                    recordKey="version"
                    columns={versionTableColumns}
                    style={{ marginBottom: '20px' }}
                  />
                </div>

                <div id="status-description">
                  <div
                    style={{
                      margin: '10px 0',
                      fontSize: '22px',
                      fontWeight: 'bold',
                    }}
                  >
                    {t('前言、报备状态值说明')}
                  </div>
                  <Table
                    bordered
                    compact
                    records={statusTableData}
                    recordKey="status"
                    columns={statusTableColumns}
                    style={{ marginBottom: '20px' }}
                  />
                  <div
                    style={{
                      display: 'flex',
                      flexDirection: 'column',
                      alignItems: 'center',
                    }}
                  >
                    <H3 style={{ margin: '10px 0' }}>{t('状态扭转流程图')}</H3>
                    <ImagePreview
                      src={statusChanges}
                      previewSrc={statusChanges}
                      maskClosable
                      alt="statusChanges"
                      style={{ width: isSmallScreen ? '70%' : '40%' }} // 图片响应式宽度
                    />
                  </div>
                </div>

                <div id="login-guide">
                  <div
                    style={{
                      margin: '10px 0',
                      fontSize: '22px',
                      fontWeight: 'bold',
                    }}
                  >
                    {t('一、登录与进入页面')}
                  </div>
                  <Text theme="danger" style={{ fontSize: '14px' }}>
                    {t('推荐使用chrome浏览器')}
                  </Text>

                  <div id="login-account">
                    <H3 style={{ margin: '10px 0' }}>{t('1、登录账号获取')}</H3>
                    <Text style={{ marginBottom: '16px' }}>
                      <Trans>
                        登录地址：
                        <a
                          href="https://csms-supplier.qcloud.com/index"
                          target="_blank"
                          rel="noopener noreferrer"
                        >
                          https://csms-supplier.qcloud.com/index
                        </a>
                      </Trans>
                    </Text>
                    <br />
                    <Text style={{ marginBottom: '16px' }}>
                      <Trans>
                        如需要新增登录账号，可找腾讯云侧运营同学朱灿获取账号开通申请表，填写后反馈至朱灿，后台开通。
                      </Trans>
                      <div
                        style={{
                          display: 'flex',
                          flexDirection: 'column',
                          alignItems: 'center',
                          justifyContent: 'center',
                        }}
                      >
                        <ImagePreview
                          src={login}
                          previewSrc={login}
                          maskClosable
                          style={{
                            width: isSmallScreen ? '70%' : '30%',
                            margin: '10px 0',
                          }} // 图片响应式宽度
                          alt="login"
                        />
                      </div>
                    </Text>
                  </div>

                  <div id="enter-system">
                    <H3 style={{ margin: '10px 0' }}>{t('2、进入系统')}</H3>
                    <List style={{ marginBottom: '20px' }}>
                      <List.Item>
                        {t(
                          '（1）供应商登录平台后，进入"需求单管理"页面，在此页面可查看账号下的全量签名，涵盖已报备、未报备、报备中等全流程状态。',
                        )}
                      </List.Item>
                      <List.Item>
                        {t('（2）支持修改密码：页面右上角，可支持修改密码。')}
                      </List.Item>
                      <div
                        style={{
                          display: 'flex',
                          flexDirection: 'column',
                          alignItems: 'center',
                          justifyContent: 'center',
                        }}
                      >
                        <ImagePreview
                          alt="supplierManage"
                          previewSrc={supplierManage}
                          src={supplierManage}
                          style={{
                            width: isSmallScreen ? '70%' : '40%',
                            margin: '10px 0',
                          }} // 图片响应式宽度
                          maskClosable
                        />
                      </div>
                    </List>
                  </div>
                </div>

                <div id="search-operation">
                  <div
                    style={{
                      margin: '10px 0',
                      fontSize: '22px',
                      fontWeight: 'bold',
                    }}
                  >
                    {t('二、查询操作')}
                  </div>
                  <Text style={{ marginBottom: '20px' }}>
                    {t(
                      '支持多维度查询，用户可根据实际需求，在相应查询框中输入具体内容，点击"查询"按钮进行数据筛选。',
                    )}
                  </Text>
                  <div
                    style={{
                      display: 'flex',
                      flexDirection: 'column',
                      alignItems: 'center',
                    }}
                  >
                    <ImagePreview
                      src={search}
                      previewSrc={search}
                      style={{
                        width: isSmallScreen ? '70%' : '40%',
                        margin: '10px 0',
                      }} // 图片响应式宽度
                      alt="search"
                      maskClosable
                    />
                  </div>
                </div>

                <div id="export-operation">
                  <div
                    style={{
                      margin: '10px 0',
                      fontSize: '22px',
                      fontWeight: 'bold',
                    }}
                  >
                    {t('三、导出操作')}
                  </div>
                  <List type="number" style={{ marginBottom: '20px' }}>
                    <List.Item style={{ marginBottom: '12px' }}>
                      <Text>{t('导出操作：')}</Text>
                      {t(
                        '供应商如需导出签名数据，点击"导出"按钮。导出操作需进行二次校验，采用邮件验证码模式。在输入正确的验证码后，系统将根据当前设置的查询条件，在后端处理并生成导出文件。',
                      )}
                    </List.Item>
                    <List.Item style={{ marginBottom: '12px' }}>
                      <Text>{t('导出当前页报备单数据：')}</Text>
                      <div
                        style={{
                          display: 'flex',
                          flexDirection: 'column',
                          alignItems: 'center',
                        }}
                      >
                        <ImagePreview
                          src={exportOp}
                          previewSrc={exportOp}
                          style={{
                            width: isSmallScreen ? '70%' : '30%',
                            margin: '10px 0',
                          }} // 图片响应式宽度
                          alt="exportOp"
                          maskClosable
                        />
                      </div>
                      <br />
                      {t(
                        '如上图，勾选红框处则会全选当前页报备单，此时再点击"导出"，即可导出当前页数据。同理也可勾选部分报备单进行导出。',
                      )}
                    </List.Item>
                    <List.Item style={{ marginBottom: '12px' }}>
                      <Text>{t('按条件导出：')}</Text>
                      <div
                        style={{
                          display: 'flex',
                          flexDirection: 'column',
                          alignItems: 'center',
                        }}
                      >
                        <ImagePreview
                          src={exportCon}
                          previewSrc={exportCon}
                          style={{
                            width: isSmallScreen ? '70%' : '50%',
                            margin: '10px 0',
                          }} // 图片响应式宽度
                          alt="exportCon"
                          maskClosable
                        />
                      </div>
                      <br />
                      {t(
                        '填写上图中检索条件后，点击"导出"，即可导出检索条件对应数据',
                      )}
                      <Text style={{ fontWeight: 'bold' }}>
                        （{' '}
                        <Trans>
                          注意按条件导出时，不要勾选报备单，如果勾选报备单则为导出当前页报备单数据
                        </Trans>{' '}
                        ）
                      </Text>
                    </List.Item>
                    <List.Item style={{ marginBottom: '12px' }}>
                      <Text>{t('导出确认项：')}</Text>
                      <List type="bullet" style={{ marginTop: '8px' }}>
                        <List.Item>
                          {t(
                            '可选是否导出附件，选择"否"时仅导出报备excel（导出速度相对快一些），选择"是"时会导出营业执照图片及App（商标）备案截图；',
                          )}
                        </List.Item>
                        <List.Item>
                          {t(
                            '如果导出的数据，报备状态为"待报备"，可以在导出的同时选择把导出签名的报备状态，由"待报备"扭转为"报备中"。',
                          )}
                        </List.Item>
                        <div
                          style={{
                            display: 'flex',
                            flexDirection: 'column',
                            alignItems: 'center',
                          }}
                        >
                          <ImagePreview
                            src={exportConfirm}
                            previewSrc={exportConfirm}
                            style={{
                              width: isSmallScreen ? '70%' : '25%',
                              margin: '10px 0',
                            }} // 图片响应式宽度
                            alt="exportConfirm"
                            maskClosable
                          />
                        </div>
                      </List>
                    </List.Item>
                    <List.Item style={{ marginBottom: '12px' }}>
                      {t(
                        '导出操作为异步完成，提交导出后，附件下载链接将以邮件形式推送至您配置的报备邮箱，请点击下载附件',
                      )}
                      <Text style={{ color: 'red' }}>
                        （{' '}
                        <Trans>
                          请注意：附件下载链接自导出时起，24小时内有效
                        </Trans>{' '}
                        ）。
                      </Text>
                      <div
                        style={{
                          display: 'flex',
                          flexDirection: 'column',
                          alignItems: 'center',
                        }}
                      >
                        <ImagePreview
                          src={downloadLink}
                          previewSrc={downloadLink}
                          style={{
                            width: isSmallScreen ? '70%' : '30%',
                            margin: '10px 0',
                          }} // 图片响应式宽度
                          alt="downloadLink"
                          maskClosable
                        />
                      </div>
                    </List.Item>
                    <List.Item style={{ marginBottom: '12px' }}>
                      {t('可在"异步任务列表"查询导出任务的执行情况。')}
                      <div
                        style={{
                          display: 'flex',
                          flexDirection: 'column',
                          alignItems: 'center',
                        }}
                      >
                        <ImagePreview
                          src={taskCon}
                          previewSrc={taskCon}
                          style={{
                            width: isSmallScreen ? '70%' : '40%',
                            margin: '10px 0',
                          }} // 图片响应式宽度
                          alt="taskCon"
                          maskClosable
                        />
                      </div>
                    </List.Item>
                    <List.Item style={{ marginBottom: '12px' }}>
                      {t(
                        '所下载附件为zip压缩文件，需要解压密码，密码生成规则腾讯云运营同事已经邮件。如有疑问请咨询腾讯云运营同事。',
                      )}
                    </List.Item>
                  </List>
                </div>

                <div id="status-modification">
                  <div
                    style={{
                      margin: '10px 0',
                      fontSize: '22px',
                      fontWeight: 'bold',
                    }}
                  >
                    {t('四、报备状态修改操作')}
                  </div>

                  <div id="single-status-change">
                    <H3 style={{ margin: '10px 0' }}>
                      {t('（一）页面单个签名变更报备状态')}
                    </H3>
                    <Text style={{ marginBottom: '16px' }}>
                      {t(
                        '在签名列表中，点击某一签名记录对应的"操作"列中的报备状态按钮，可对该签名的报备状态进行单个修改。修改操作需遵循签名实际报备情况做修改。',
                      )}
                    </Text>
                  </div>

                  <div id="batch-status-change">
                    <H3 style={{ margin: '10px 0' }}>
                      {t('（二）页面批量变更报备状态')}
                    </H3>
                    <Text style={{ marginBottom: '16px' }}>
                      {t(
                        '点击"批量变更报备状态"按钮，可对多个签名的报备状态进行批量修改。操作时，先通过查询条件筛选出需要修改状态的签名记录，勾选对应的复选框，然后在弹出的批量修改窗口中操作填写。',
                      )}
                    </Text>
                  </div>

                  <div id="batch-upload-result">
                    <H3 style={{ margin: '10px 0' }}>
                      {t('（三）批量上传报备结果')}
                    </H3>
                    <Text style={{ marginBottom: '16px' }}>
                      {t(
                        '点击"批量上传报备结果"按钮，可将已整理好的包含报备状态及相关信息的文件上传至系统。上传文件需符合批量导入结果填写说明的要求。',
                      )}
                    </Text>
                  </div>

                  <div id="fill-instructions">
                    <H3 style={{ fontWeight: 'bold', margin: '10px 0' }}>
                      {t('填写说明')}
                    </H3>
                    <List type="number" style={{ marginBottom: '16px' }}>
                      <List.Item>
                        <Text style={{ fontWeight: 'bold' }}>
                          {t('B列 - "报备状态"')}
                        </Text>
                        {t('：必填，单选，仅支持下拉选择签名真实报备状态。')}
                      </List.Item>
                      <List.Item>
                        <Text style={{ fontWeight: 'bold' }}>
                          {t('C列 - "失败原因"')}
                        </Text>
                        {t('：可填写多个失败原因，序号用英文逗号隔开。')}
                      </List.Item>
                      <List.Item>
                        <Text style={{ fontWeight: 'bold' }}>
                          {t('D列 - "缺失字段/异常字段"')}
                        </Text>
                        {t(
                          '：当修改某些字段信息或者补充某些字段信息即可重新报备时，请明确，如有多个资料需修改/补充，序号用英文逗号隔开。',
                        )}
                      </List.Item>
                      <List.Item>
                        <Text style={{ fontWeight: 'bold' }}>
                          {t('E列 - "备注"')}
                        </Text>
                        {t(
                          '：当"C\\D列"有任意一个选择为"其他"时，E列必填，其他情况则选填。',
                        )}
                      </List.Item>
                    </List>
                  </div>

                  <div id="column-relationship">
                    <H3 style={{ fontWeight: 'bold', margin: '10px 0' }}>
                      {t('各列关联性说明')}
                    </H3>
                    <List type="number" style={{ marginBottom: '20px' }}>
                      <List.Item>
                        <Text style={{ fontWeight: 'bold' }}>
                          {t('B列为"报备成功"、"报备中"时')}
                        </Text>
                        {t('：C列和D列保持为空，不能填写。')}
                      </List.Item>
                      <List.Item>
                        <Text style={{ fontWeight: 'bold' }}>
                          {t('B列为"报备失败"、"停用"时')}
                        </Text>
                        {t(
                          '：C列（失败原因）必填。如果C列是"21资料缺失"，则D列（缺失字段/异常字段）必填，否则选填。',
                        )}
                      </List.Item>
                      <List.Item>
                        <Text style={{ fontWeight: 'bold' }}>
                          {t('B列为"资料异常"时')}
                        </Text>
                        {t(
                          '：C列（失败原因）必填，C列选择"21资料缺失"时，D列（缺失字段/异常字段）必填，否则D列选填。',
                        )}
                      </List.Item>
                      <List.Item>
                        <Text style={{ fontWeight: 'bold' }}>
                          {t('C列选择"资料缺失"时')}
                        </Text>
                        {t('：不可同时选择其他失败原因，否则可多选。')}
                      </List.Item>
                      <List.Item>
                        <Text style={{ fontWeight: 'bold' }}>
                          {t('C列需在B列已选定的前提下进行选择')}
                        </Text>
                        {t('：且仅能选择"失败原因"示例中对应分类的选项。')}
                      </List.Item>
                    </List>
                  </div>
                </div>

                <div id="notice">
                  <div
                    style={{
                      margin: '10px 0',
                      fontSize: '22px',
                      fontWeight: 'bold',
                    }}
                  >
                    {t('五、注意事项')}
                  </div>
                  <List type="number" style={{ marginBottom: '20px' }}>
                    <List.Item style={{ marginBottom: '8px' }}>
                      {t(
                        '请严格控制数据使用范围和确保数据安全，数据仅能用于运营商签名实名报备使用，确保数据最小范围使用；',
                      )}
                    </List.Item>
                    <List.Item style={{ marginBottom: '8px' }}>
                      {t(
                        '批量导入前请仔细理解报备状态值说明，检查数据，严格按照填写规范操作，避免因格式错误导致导入失败，特别是各列间的关联关系',
                      )}
                    </List.Item>
                    <List.Item style={{ marginBottom: '8px' }}>
                      {t('如有疑问，请联系运营对接同学朱灿。')}
                    </List.Item>
                  </List>
                </div>
              </Card.Body>
            </Card>
          </div>
        </div>
      </div>
    </div>
  );
};

export default GuidanceManual;
