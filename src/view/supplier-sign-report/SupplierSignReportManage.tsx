import React, { useState, useEffect, useMemo, useCallback } from 'react';
import {
  Button,
  Input,
  Select,
  Form,
  Text,
  Table,
  Bubble,
  Modal,
  DatePicker,
  Radio,
} from '@tencent/tea-component';
import { app } from '@src/utils/tea/app';
import { Form as FinalForm, Field } from 'react-final-form';
import { getStatus } from '@src/utils/form/getStatus';
import { t, Trans, Slot } from '@src/utils/i18n';
import { useDialogRef } from '@src/utils/react-use/useDialog';
import { useSetState, useAsyncFn } from 'react-use';
import _ from 'lodash';

import {
  getStatusText,
  findText,
  SIGN_REPORT_STATUS_WAIT_REPORT,
  SIGN_REPORT_STATUS_STOP_USE,
  SIGN_REPORT_STATUS_REPORT_FAIL,
  SIGN_REPORT_STATUS_REPORT_SUCC,
  SIGN_REPORT_STATUS_WAIT_ADD_INFO,
  SIGN_REPORT_STATUS_REPORTING,
} from '@src/const/const';
import {
  getExtMsg,
  getHeaderByKey,
  allSignApplyColumns,
} from '../sign-report/component/const';
import { signReportStatus, signShowKeys } from '@src/const/const';
import { AuditOperateDialog } from './component/AuditOperateDialog';
import { scrollable, selectable } from '@tea/component/table/addons';
import { ImportExcelDialog } from '@src/global-components/import-component/ImportExcelDialog';
import { changeSignReportStatus, exportSignReport } from '@src/api/sign';
import { getSignReportList } from '@src/api/sign';
import { NeedCheckAccountButton } from '@src/global-components/NeedCheckAccountButton';
import useFetchFailureReason from '@src/global-components/useFetch/useFetchFailureReason';
import { addAsyncTask } from '@src/api/addAsyncTask';
import { ExportOptionsDialog } from './component/ExportOptionsDialog';
import moment from 'moment';

const { RangePicker } = DatePicker;

const { autotip } = Table.addons;
const { pageable } = Table.addons;

function downloadFile(href, filename, target = '_self') {
  const link = document.createElement('a');
  link.download = filename;
  link.target = target;
  link.href = href;
  link.click();
}

function handleData(data: any[]) {
  return _.map(data?.slice(2), (item) => {
    return {
      ...item,
    };
  });
}

function mapImportKeys(keys: string) {
  const keyArr = keys?.toString()?.split(',');
  const mapRules = new Map([
    ['1', { text: t('公司名称'), value: 'company_name' }],
    ['2', { text: t('社会信用代码'), value: 'unisc_id' }],
    ['3', { text: t('法人姓名'), value: 'corp_name' }],
    ['4', { text: t('法人证件类型'), value: 'corp_cr_type' }],
    ['5', { text: t('法人证件号码'), value: 'corp_cr_num' }],
    ['6', { text: t('经办人姓名'), value: 'transactor_name' }],
    ['7', { text: t('经办人证件类型'), value: 'transactor_cr_type' }],
    ['8', { text: t('经办人证件号码'), value: 'transactor_cr_num' }],
    ['9', { text: t('经办人联系电话'), value: 'transactor_phone' }],
    ['10', { text: t('合规使用承诺函'), value: 'commitment_letter_url' }],
    ['11', { text: t('其他资质资料'), value: 'other_attach_url' }],
    ['12', { text: t('经办人证件正面'), value: 'transactor_idcard_url' }],
    ['13', { text: t('经办人证件反面'), value: 'transactor_idcard_back_url' }],
    [
      '14',
      { text: t('经办人手持证件照片'), value: 'transactor_photo_idcard_url' },
    ],
    ['15', { text: t('法人证件正面'), value: 'corp_idcard_url' }],
    ['16', { text: t('法人证件反面'), value: 'corp_idcard_back_url' }],
    ['17', { text: t('企业资质证件'), value: 'company_file_url' }],
    ['18', { text: t('授权书'), value: '' }],
    ['19', { text: t('ICP备案或许可证号/商标号'), value: 'registration_num' }],
    [
      '20',
      {
        text: t('ICP备案截图/商标备案截图/商标备案截图'),
        value: 'registration_url',
      },
    ],
    ['21', { text: t('APP类型-应用商店下载链接'), value: 'app_resource_url' }],
    ['22', { text: t('APP应用商店截图/商标注册书截图'), value: 'attach_url' }],
  ]);
  return {
    text: _.map(keyArr, (key) => mapRules.get(key)?.text),
    value: _.map(keyArr, (key) => mapRules.get(key)?.value),
  };
}

export const SupplierSignReportManage = (props) => {
  const auditDialogRef = useDialogRef();
  const [selected, setSelected] = useState<any[]>([]);
  const importDialogRef = useDialogRef();
  const exportDialogRef = useDialogRef();
  const exportSuccessDialogRef = useDialogRef();

  const [pagination, setPagination] = useSetState({
    page_index: 1,
    page_size: 10,
  });
  const [searchKeys, setSearchKeys] = useState<any>({
    time: [moment().startOf('d'), moment().endOf('d')],
  });
  const { list: faliureReasonList } = useFetchFailureReason();

  const openFailureReasonModal = useCallback(
    async (title: string, row: any) => {
      const reason = _.map(
        row.failure_reason_ids?.split(','),
        (r) => _.find(faliureReasonList, (f) => +f.id === +r)?.son_category,
      )?.join(';');
      await Modal.alert({
        type: 'warning',
        message: title,
        description: (
          <>
            <p>
              <Trans>
                原因：
                <Slot content={reason || '-'} />
              </Trans>
            </p>
            {row.wrong_keys && (
              <p>
                <Trans>
                  缺失/异常字段：
                  <Slot content={getHeaderByKey(row.wrong_keys)} />
                </Trans>
              </p>
            )}

            <p>
              <Trans>
                备注：
                <Slot content={getExtMsg(row.ext, row.status) || '-'} />
              </Trans>
            </p>
          </>
        ),
      });
    },
    [faliureReasonList],
  );

  const [state, fetchList] = useAsyncFn(async () => {
    const res = await getSignReportList({
      ..._.omit(searchKeys, ['sign', 'time']),
      ...pagination,
      signs: searchKeys.sign
        ?.split('\n')
        ?.map((s) => s.trim())
        ?.filter((s) => !!s),
      from_time: searchKeys.time?.[0]?.format('YYYY-MM-DD 00:00:00'),
      to_time: searchKeys.time?.[1]?.format('YYYY-MM-DD 23:59:59'),
    });
    return res.data || {};
  }, [pagination, searchKeys]);

  const list = useMemo(() => {
    return state?.value?.list ?? [];
  }, [state]);

  useEffect(() => {
    fetchList();
  }, [searchKeys, pagination, fetchList]);

  const total = useMemo(() => {
    return state?.value?.count ?? 0;
  }, [state]);

  const showKeysColumns = useMemo(() => {
    const showKeys = _.uniq(
      _.flatten(
        state?.value?.list?.map((item) => {
          return _.keys(
            _.pickBy(item, (value, key) => signShowKeys.includes(key)),
          );
        }) ?? [],
      ),
    );
    return allSignApplyColumns.filter((el) => showKeys.includes(el.key)) ?? [];
  }, [state?.value?.list]);

  const columns = useMemo(() => {
    return [
      {
        key: 'report_id',
        header: t('报备单ID'),
        width: 80,
      },
      {
        key: 'account',
        header: t('账号'),
        width: 80,
      },
      {
        key: 'sub_code',
        header: t('签名子码'),
        width: 80,
      },
      {
        key: 'remark_type',
        header: t('签名类型'),
        width: 80,
      },
      {
        key: 'sign',
        header: t('签名内容'),
        width: 80,
      },
      ...showKeysColumns,
      {
        key: 'status',
        header: t('报备状态'),
        width: 100,
        fixed: 'right',
        render: (row) => getStatusText(signReportStatus, row.status),
        exportRender: (row) => findText(signReportStatus, row.status),
      },
      {
        key: 'created_at',
        header: t('创建时间'),
        width: 100,
      },
      {
        key: 'updated_at',
        header: t('更新时间'),
        width: 100,
      },
      {
        key: 'operation',
        header: t('操作'),
        width: 160,
        fixed: 'right',
        render: (row) => (
          <>
            {row.status === SIGN_REPORT_STATUS_WAIT_REPORT && (
              <Button type="link">
                <Text
                  onClick={async () => {
                    auditDialogRef.current.open({
                      caption: t('报备中'),
                      status: SIGN_REPORT_STATUS_REPORTING,
                      rows: [row],
                    });
                  }}
                >
                  {t('报备中')}
                </Text>
              </Button>
            )}
            {[
              SIGN_REPORT_STATUS_WAIT_REPORT,
              SIGN_REPORT_STATUS_REPORTING,
              SIGN_REPORT_STATUS_REPORT_FAIL,
              SIGN_REPORT_STATUS_STOP_USE,
            ].includes(row.status) && (
              <Button type="link">
                <Text
                  theme="success"
                  onClick={async () => {
                    auditDialogRef.current.open({
                      rows: [row],
                      status: SIGN_REPORT_STATUS_REPORT_SUCC,
                    });
                  }}
                >
                  {t('报备成功')}
                </Text>
              </Button>
            )}
            {[
              SIGN_REPORT_STATUS_WAIT_REPORT,
              SIGN_REPORT_STATUS_REPORTING,
            ].includes(row.status) && (
              <Button type="link">
                <Text
                  theme="danger"
                  onClick={async () => {
                    auditDialogRef.current.open({
                      rows: [row],
                      status: SIGN_REPORT_STATUS_REPORT_FAIL,
                    });
                  }}
                >
                  {t('报备失败')}
                </Text>
              </Button>
            )}
            {[
              SIGN_REPORT_STATUS_WAIT_REPORT,
              SIGN_REPORT_STATUS_REPORTING,
              SIGN_REPORT_STATUS_STOP_USE,
            ].includes(row.status) && (
              <Button type="link">
                <Text
                  theme="warning"
                  onClick={async () => {
                    auditDialogRef.current.open({
                      caption: t('资料异常'),
                      status: SIGN_REPORT_STATUS_WAIT_ADD_INFO,
                      rows: [row],
                    });
                  }}
                >
                  {t('资料异常')}
                </Text>
              </Button>
            )}
            {row.status === SIGN_REPORT_STATUS_REPORT_SUCC && (
              <Button type="link">
                <Text
                  theme="danger"
                  onClick={async () => {
                    auditDialogRef.current.open({
                      caption: t('停用'),
                      status: SIGN_REPORT_STATUS_STOP_USE,
                      rows: [row],
                    });
                  }}
                >
                  {t('停用')}
                </Text>
              </Button>
            )}
            {row.status === SIGN_REPORT_STATUS_REPORT_FAIL && (
              <Button type="link">
                <Text
                  onClick={async () => {
                    await openFailureReasonModal(t('驳回原因'), row);
                  }}
                >
                  {t('驳回原因')}
                </Text>
              </Button>
            )}
            {row.status === SIGN_REPORT_STATUS_STOP_USE && (
              <>
                <Button type="link">
                  <Text
                    onClick={async () => {
                      await openFailureReasonModal(t('停用原因'), row);
                    }}
                  >
                    {t('停用原因')}
                  </Text>
                </Button>
              </>
            )}
            {row.status === SIGN_REPORT_STATUS_WAIT_ADD_INFO && (
              <Button type="link">
                <Text
                  onClick={async () => {
                    await openFailureReasonModal(t('资料缺失原因'), row);
                  }}
                >
                  {t('资料缺失原因')}
                </Text>
              </Button>
            )}
          </>
        ),
      },
    ];
  }, [auditDialogRef, openFailureReasonModal, showKeysColumns]);

  function onSubmit(values) {
    setSearchKeys({ ...values });
  }

  async function changeStatus(params) {
    const _params = _.map(params, (el) => ({
      report_id: el.id,
      status: el.status,
      msg: el.msg,
      failure_reason_ids: el.failure_reason_ids || undefined,
      wrong_keys: el.wrong_keys || undefined,
    }));
    if (params.length >= 2) {
      const res1 = await addAsyncTask({
        command: 'change_sign_report_status',
        params: _params,
      });
      if (res1?.code === 0) {
        fetchList();
        app.tips.success(
          t('状态变更任务下发成功，请至“异步任务列表”页面查看详细结果'),
        );
      }
      return res1;
    }
    const res = await changeSignReportStatus({
      params: _params,
    });
    if (res?.code === 0 && !res?.data?.errors?.length) {
      fetchList();
      app.tips.success(t('签名状态更新成功'));
    }
    return res;
  }

  async function exportData(values) {
    try {
      const v = _.pickBy(values, (v: any) => v !== '' || _.isNil(v));
      const params = selected?.length
        ? {
            verify_code: v.verify_code,
            report_ids: selected?.map((el) => el.report_id),
          }
        : { ...v };
      exportSuccessDialogRef.current.open({
        searchKeys,
        params,
      });
      return Promise.resolve();
    } catch (err: any) {}
  }

  const collectWrongKeys = useCallback(
    (row) => {
      const failureIds = row.failure_reason_ids;
      const signKeys = _.flatMap(failureIds?.toString()?.split(','), (id) => {
        return faliureReasonList
          .find((item) => item.id === +id)
          ?.wrong_keys?.split(',');
      });
      const addWrongKeys = _.uniq(signKeys)?.filter((s) => !!s);
      const wrongKeys = mapImportKeys(row.wrong_keys)?.value ?? [];
      wrongKeys.push(...addWrongKeys);
      return wrongKeys?.join(',');
    },
    [faliureReasonList],
  );

  const importCols = useMemo(() => {
    const col = _.filter(_.cloneDeep(columns), (c) =>
      [
        'report_id',
        'status',
        'account',
        'sign',
        // 'sub_code',
      ].includes(c.key),
    );
    const status = _.find(col, (c) => c.key === 'status');
    status.render = undefined;
    col.push(
      ...[
        {
          header: t('报备原因'),
          key: 'failure_reason_ids',
          width: 120,
          render: (row) => {
            const text = _.map(
              row?.failure_reason_ids?.toString()?.split(','),
              (id) =>
                faliureReasonList.find((el) => +el.id === +id)?.son_category,
            )?.join(',');
            return (
              <Text tooltip={text} overflow>
                {text || '-'}
              </Text>
            );
          },
        },
        {
          header: t('缺失/异常字段'),
          key: 'wrong_keys',
          width: 80,
          render: (row) => getHeaderByKey(collectWrongKeys(row)) || '-',
        },
        { header: t('备注'), key: 'msg', width: 80 },
        { header: t('签名类型'), key: 'remark_type', width: 80 },
      ],
    );
    // 期望的排序顺序
    const desiredOrder = [
      'report_id',
      'status',
      'failure_reason_ids',
      'wrong_keys',
      'msg',
      'account',
      'sign',
      'remark_type',
      // 'sub_code',
    ];
    return _.sortBy(col, (item) => {
      return desiredOrder.indexOf(item.key);
    });
  }, [collectWrongKeys, columns, faliureReasonList]);

  return (
    <>
      <FinalForm
        onSubmit={onSubmit}
        initialValuesEqual={(val, oldVal) => {
          return _.isEqual(val, oldVal);
        }}
        initialValues={{
          time: [moment().startOf('d'), moment().endOf('d')],
        }}
      >
        {({ handleSubmit, validating, values }) => {
          return (
            <form onSubmit={handleSubmit}>
              <Form layout="inline">
                <Field name="account" validateFields={[]}>
                  {({ input, meta }) => (
                    <Form.Item
                      showStatusIcon={false}
                      label={t('账号')}
                      status={getStatus(meta, validating)}
                      message={
                        getStatus(meta, validating) === 'error' && meta.error
                      }
                    >
                      <Input {...(input as any)} placeholder={t('请输入')} />
                    </Form.Item>
                  )}
                </Field>
                <Field name="sign" validateFields={[]}>
                  {({ input, meta }) => (
                    <Form.Item
                      showStatusIcon={false}
                      label={t('签名内容')}
                      status={getStatus(meta, validating)}
                      message={
                        getStatus(meta, validating) === 'error' && meta.error
                      }
                    >
                      <Input.TextArea
                        {...(input as any)}
                        placeholder={t('换行输入多个')}
                        rows={4}
                      />
                    </Form.Item>
                  )}
                </Field>
                <Field name="company_name" validateFields={[]}>
                  {({ input, meta }) => (
                    <Form.Item
                      showStatusIcon={false}
                      label={t('企业名称')}
                      status={getStatus(meta, validating)}
                      message={
                        getStatus(meta, validating) === 'error' && meta.error
                      }
                    >
                      <Input {...(input as any)} placeholder={t('请输入')} />
                    </Form.Item>
                  )}
                </Field>
                <Field name="transactor_name" validateFields={[]}>
                  {({ input, meta }) => (
                    <Form.Item
                      showStatusIcon={false}
                      label={t('经办人姓名')}
                      status={getStatus(meta, validating)}
                      message={
                        getStatus(meta, validating) === 'error' && meta.error
                      }
                    >
                      <Input {...(input as any)} placeholder={t('请输入')} />
                    </Form.Item>
                  )}
                </Field>
                <Field name="transactor_cr_num" validateFields={[]}>
                  {({ input, meta }) => (
                    <Form.Item
                      showStatusIcon={false}
                      label={t('经办人身份证号')}
                      status={getStatus(meta, validating)}
                      message={
                        getStatus(meta, validating) === 'error' && meta.error
                      }
                    >
                      <Input {...(input as any)} placeholder={t('请输入')} />
                    </Form.Item>
                  )}
                </Field>
                <Field name="status" validateFields={[]}>
                  {({ input, meta }) => (
                    <Form.Item
                      showStatusIcon={false}
                      label={t('报备状态')}
                      status={getStatus(meta, validating)}
                      message={
                        getStatus(meta, validating) === 'error' && meta.error
                      }
                    >
                      <Select
                        clearable
                        {...(input as any)}
                        type="simulate"
                        appearance="button"
                        options={signReportStatus}
                        placeholder={t('请选择')}
                        size="s"
                        autoComplete="off"
                      />
                    </Form.Item>
                  )}
                </Field>
                <Field name="time" validateFields={[]}>
                  {({ input, meta }) => (
                    <Form.Item
                      showStatusIcon={false}
                      label={t('创建日期范围')}
                      status={getStatus(meta, validating)}
                      message={
                        getStatus(meta, validating) === 'error' && meta.error
                      }
                    >
                      <RangePicker
                        clearable
                        {...(input as any)}
                        placeholder={t('请选择日期')}
                      />
                    </Form.Item>
                  )}
                </Field>
                <div style={{ display: 'inline-block' }}>
                  <Button
                    type="primary"
                    loading={state.loading}
                    htmlType="submit"
                    style={{ marginLeft: 5 }}
                  >
                    {t('查询')}
                  </Button>
                  <Bubble
                    placement="top-start"
                    content={
                      <>
                        {selected.length
                          ? t('按勾选项导出，如需按查询条件导出，请清空勾选项')
                          : t('按查询条件导出')}
                      </>
                    }
                  >
                    <NeedCheckAccountButton
                      route="/sign/report/export"
                      style={{ marginLeft: 10 }}
                      loading={state.loading}
                      dialogRef={exportDialogRef}
                      onSubmit={({ verify_code }) => {
                        return exportData({
                          ..._.omit(values, ['sign', 'time']),
                          verify_code: verify_code,
                          signs: values.sign
                            ?.split('\n')
                            ?.map((s) => s.trim())
                            ?.filter((s) => !!s),
                          from_time: values.time?.[0]?.format(
                            'YYYY-MM-DD 00:00:00',
                          ),
                          to_time: values.time?.[1]?.format(
                            'YYYY-MM-DD 23:59:59',
                          ),
                        });
                      }}
                    >
                      {t('导出')}
                    </NeedCheckAccountButton>
                  </Bubble>
                </div>
              </Form>
            </form>
          );
        }}
      </FinalForm>
      <div style={{ margin: '10px 0' }}>
        <Button
          type="primary"
          onClick={() => {
            if (!selected.length) {
              app.tips.warning(t('请选择要操作的数据'));
              return;
            }
            auditDialogRef.current.open({
              rows: selected,
              status: undefined,
            });
          }}
        >
          {t('批量变更报备状态')}
        </Button>
        <Button
          type="primary"
          onClick={() => {
            importDialogRef.current.open();
          }}
          style={{ marginLeft: 10 }}
        >
          {t('批量上传报备结果')}
        </Button>
      </div>
      <Table
        bordered
        disableTextOverflow
        records={list}
        recordKey="report_id"
        columns={columns}
        addons={[
          autotip({
            isLoading: state.loading,
          }),
          pageable({
            recordCount: total,
            onPagingChange: (query) =>
              setPagination({
                page_index: query.pageIndex,
                page_size: query.pageSize,
              }),
          }),
          selectable({
            onChange: (selectedKeys, context) => {
              setSelected(context.selectedRecords);
            },
          }),
        ]}
      />
      <AuditOperateDialog dialogRef={auditDialogRef} onSubmit={changeStatus} />
      <ImportExcelDialog
        dialogRef={importDialogRef}
        columns={importCols}
        header={importCols.map((v) => v.key)}
        onSubmit={async (params) => {
          const indexes = _.reduce(
            params.params,
            (result: number[], cur, index: number) => {
              if (
                cur.failure_reason_ids?.toString()?.includes('，') ||
                cur.wrong_keys?.toString()?.includes('，')
              ) {
                result.push(index);
              }
              return result;
            },
            [],
          );
          if (indexes?.length > 0) {
            Modal.error({
              message: t(
                '第{{attr0}}行数据中，失败原因/缺失字段/异常字段不能包含中文逗号，请使用英文逗号分隔',
                { attr0: indexes?.join(',') },
              ),
            });
            return;
          }
          const _params = _.map(params.params, (p: any) => {
            const status = _.find(
              signReportStatus,
              (s) => s.text === p.status,
            )?.value;
            return {
              report_id: p.report_id,
              status: status,
              failure_reason_ids: p.failure_reason_ids
                ? `${p.failure_reason_ids}`
                : undefined,
              wrong_keys:
                status === SIGN_REPORT_STATUS_WAIT_ADD_INFO
                  ? collectWrongKeys(p)
                  : undefined,
              msg: p.msg ? `${p.msg}` : undefined,
            };
          })?.filter(
            (e: any) =>
              ![
                SIGN_REPORT_STATUS_REPORTING,
                SIGN_REPORT_STATUS_WAIT_REPORT,
              ].includes(e.status),
          );
          if (_params?.length === 0) {
            Modal.error({
              message: t('导入数据为空，请检查数据'),
              description: t(
                '表格中所填写的数据，报备状态不能为“报备中”，否则会被忽略',
              ),
            });
            return Promise.reject();
          }
          return await addAsyncTask({
            command: 'change_sign_report_status',
            params: _params,
          });
        }}
        onSuccess={() => {
          Modal.success({
            message: t('任务下发成功'),
            description: t(
              '报备单状态批量变更任务下发成功，请至“异步任务列表”页面查看变更结果',
            ),
          });
          fetchList();
        }}
        tableAddons={[
          scrollable({
            maxHeight: 192,
            virtualizedOptions: {
              height: 310,
              itemHeight: 30,
            },
          }),
        ]}
        downloadFile={() => downloadFile('', t('报备结果导入模板'))}
        handleData={handleData}
        range={2}
        realRange={4}
        assignStep1={
          <>
            {t(
              '1、从报备邮件获取报备列表（excel表格），根据表格指引，填写报备状态、原因等后上传',
            )}
          </>
        }
        resultDialogSize="xl"
      />
      <ExportOptionsDialog
        dialogRef={exportSuccessDialogRef}
        onSuccess={() => {
          exportDialogRef.current.close();
        }}
      ></ExportOptionsDialog>
    </>
  );
};

export default SupplierSignReportManage;
