import { DialogRef, useDialog } from '@src/utils/react-use/useDialog';
import { Modal } from '@tencent/tea-component/lib/modal/ModalMain';
import { t, Trans, Slot } from '@src/utils/i18n';
import {
  Button,
  Text,
  Icon,
  LoadingTip,
  Form,
  Progress,
  EmptyTip,
} from '@tencent/tea-component';
import _ from 'lodash';
import { useEffect, useMemo, useRef, useState } from 'react';
import { sleep } from '@src/global-components/import-component/utils';
import { useAsyncFn } from 'react-use';
import { getExportProgessInfo } from '@src/api/getExportProgressInfo';

interface DialogProps {
  dialogRef: DialogRef;
}

export const AsyncExportTaskInfoDialog = (props: DialogProps) => {
  const { dialogRef } = props;
  const [visible, setShowState, defaultVal] = useDialog<{
    progress_key: string;
  }>(dialogRef);
  const { progress_key } = defaultVal;
  const [info, setInfo] = useState<any>({});

  const _visible = useRef(visible);

  const [state, fetchInfo] = useAsyncFn(async () => {
    try {
      const { data } = await getExportProgessInfo({
        progress_key,
      });
      setInfo(data);
      if (data.split_finally_count !== data.split_total_count) {
        await sleep(2000);
        _visible.current && fetchInfo();
      }
      return data ?? {};
    } catch (error) {
      if (_visible.current) {
        await sleep(2000);
        fetchInfo();
      }
    }
  }, [progress_key]);

  const percent = useMemo(() => {
    const pendingCount = _.chain(info)
      .pickBy((v, k) => v !== '0' && /^split_\d+_file_finally$/.test(k))
      ?.values()
      ?.value()?.length;
    const sumTotal = _.chain(info)
      .pickBy((v, k) => /^split_\d+_file_total$/.test(k))
      ?.mapValues((v) => +v)
      ?.values()
      .sum()
      .value();
    const sumFinally = _.chain(info)
      .pickBy((v, k) => /^split_\d+_file_finally$/.test(k))
      ?.mapValues((v) => +v)
      ?.values()
      .sum()
      .value();
    return sumTotal === 0
      ? (info?.split_finally_count / info?.split_total_count) * 100
      : ((sumFinally / sumTotal || 0) *
          (pendingCount / info?.split_total_count) *
          0.9 +
          (info?.split_finally_count / info?.split_total_count) * 0.1) *
          100;
  }, [info]);

  useEffect(() => {
    if (visible) {
      setInfo({});
      fetchInfo();
    }
    _visible.current = visible;
  }, [fetchInfo, visible]);

  return (
    <>
      <Modal
        visible={visible}
        size="l"
        caption={
          <>
            <Icon
              type="infoblue"
              size="l"
              style={{ margin: '0 10px 10px 0' }}
            />
            <Text>{t('任务详情')}</Text>
          </>
        }
        onClose={() => setShowState(false)}
        disableEscape
      >
        <Modal.Body>
          {state?.value?.report_count !== '0' ? (
            <>
              {percent < 100 ? <LoadingTip /> : null}
              <Progress
                percent={percent}
                text={(percent) => `${Math.floor(percent)} %`}
              />
              <Form layout="inline">
                <Form.Item label={t('任务ID')}>
                  <Form.Text>{progress_key?.split('_')?.at(-1)}</Form.Text>
                </Form.Item>
              </Form>
            </>
          ) : (
            <EmptyTip emptyText={t('当前导出条件下暂无数据')}></EmptyTip>
          )}
        </Modal.Body>
        <Modal.Footer>
          <Button
            type="weak"
            onClick={() => {
              setShowState(false);
            }}
          >
            {t('关闭')}
          </Button>
        </Modal.Footer>
      </Modal>
    </>
  );
};
