import React, { useEffect, useState } from 'react';
import { useDialog, DialogRef } from '@src/utils/react-use/useDialog';
import {
  Modal,
  Button,
  message,
  Form,
  Input,
  TextArea,
  Select,
  SelectMultiple,
  Alert,
} from '@tencent/tea-component';
import { Form as FinalForm, Field } from 'react-final-form';
import { getStatus } from '@src/utils/form/getStatus';
import { t, Trans, Slot } from '@src/utils/i18n';
import _ from 'lodash';
import {
  getStatusText,
  SIGN_REPORT_STATUS_REPORT_FAIL,
  SIGN_REPORT_STATUS_REPORT_SUCC,
  SIGN_REPORT_STATUS_REPORTING,
  SIGN_REPORT_STATUS_STOP_USE,
  SIGN_REPORT_STATUS_WAIT_ADD_INFO,
  signReportStatus,
  signShowKeys,
} from '@src/const/const';
import { validateRequired } from '@src/utils/validateFn';
import ReportFailReasonCascader from '@src/global-components/ReportFailReasonCascader';
import { allSignApplyColumns } from '@src/view/sign-report/component/const';
import { app } from '@src/utils/tea/app';
import { Link } from 'react-router-dom';

interface DialogProps {
  dialogRef: DialogRef;
  onSubmit: (
    params: {
      status: number;
      id: number;
      msg: string;
    }[],
  ) => Promise<any>;
}

export const AuditOperateDialog = (props: DialogProps) => {
  const { dialogRef, onSubmit } = props;
  const [visible, setShowState, defaultVal] = useDialog<{
    rows: any[];
    status?: number;
  }>(dialogRef);

  const { rows, status } = defaultVal;

  const isBatch = rows?.length > 1;

  const wrongShowKeys = isBatch
    ? signShowKeys
    : rows?.[0]?.show_keys?.split(',');

  const _handlerSubmit = async (vals: any) => {
    if (
      vals.status === SIGN_REPORT_STATUS_WAIT_ADD_INFO &&
      _.isEmpty(vals.wrong_keys)
    ) {
      app.tips.error(t('资料异常状态，请选择缺失/异常字段'));
      return;
    }
    const res = await onSubmit(
      _.map(rows, (el) => ({
        status: vals.status,
        msg: vals.msg,
        id: el.report_id,
        failure_reason_ids: vals.failure_reason_ids
          ?.map((el) => +el[1])
          ?.join(','),
        wrong_keys: vals.wrong_keys?.join(','),
      })),
    );
    if (res?.code === 0) {
      setShowState(false);
    }
  };

  return (
    <>
      <Modal
        visible={visible}
        size="s"
        caption={
          isBatch ? t('多个签名更新报备状态') : t('单个签名更新报备状态')
        }
        onClose={() => setShowState(false)}
      >
        <Modal.Body>
          {isBatch ? (
            <Alert>
              <Trans>
                多个签名变更状态将下发异步变更任务，变更结果请至
                <Link to="/task/list?id=change_sign_report_status">
                  异步任务列表
                </Link>
                页面查看
              </Trans>
            </Alert>
          ) : null}
          <FinalForm
            onSubmit={_handlerSubmit}
            initialValuesEqual={(val, oldVal) => {
              return _.isEqual(val, oldVal);
            }}
            initialValues={{ status, failure_reason_ids: [] }}
          >
            {({ handleSubmit, validating, submitting, values, form }) => {
              return (
                <form onSubmit={handleSubmit}>
                  <Form layout="default">
                    {isBatch ? (
                      <>
                        <Form.Item label={t('签名')}>
                          <Form.Text>
                            <Trans>
                              涉及签名数
                              <Slot content={rows.length} />个
                            </Trans>
                          </Form.Text>
                        </Form.Item>
                      </>
                    ) : (
                      <>
                        <Form.Item label={t('账号')}>
                          <Form.Text>{rows[0].account}</Form.Text>
                        </Form.Item>
                        <Form.Item label={t('签名子码')}>
                          <Form.Text>{rows[0].sub_code}</Form.Text>
                        </Form.Item>
                        <Form.Item label={t('签名内容')}>
                          <Form.Text>{rows[0].sign}</Form.Text>
                        </Form.Item>
                      </>
                    )}
                    <Field
                      name="status"
                      validateFields={[]}
                      validate={validateRequired}
                    >
                      {({ input, meta }) => (
                        <Form.Item
                          showStatusIcon={false}
                          label={t('报备状态')}
                          status={getStatus(meta, validating)}
                          message={
                            getStatus(meta, validating) === 'error' &&
                            meta.error
                          }
                        >
                          <Form.Text>
                            {status ? (
                              getStatusText(signReportStatus, status)
                            ) : (
                              <Select
                                {...input}
                                options={signReportStatus
                                  .filter((el) =>
                                    [
                                      SIGN_REPORT_STATUS_REPORTING,
                                      SIGN_REPORT_STATUS_REPORT_SUCC,
                                      SIGN_REPORT_STATUS_REPORT_FAIL,
                                      SIGN_REPORT_STATUS_STOP_USE,
                                      SIGN_REPORT_STATUS_WAIT_ADD_INFO,
                                    ].includes(el.value),
                                  )
                                  ?.map((el) => ({
                                    ...el,
                                    value: `${el.value}`,
                                  }))}
                                appearance="button"
                                size="m"
                              ></Select>
                            )}
                          </Form.Text>
                        </Form.Item>
                      )}
                    </Field>
                    {[
                      SIGN_REPORT_STATUS_REPORT_FAIL,
                      SIGN_REPORT_STATUS_STOP_USE,
                      SIGN_REPORT_STATUS_WAIT_ADD_INFO,
                    ].includes(status ?? +values.status) && (
                      <Field
                        name="failure_reason_ids"
                        validateFields={[]}
                        validate={validateRequired}
                        key={values.status}
                      >
                        {({ input, meta }) => (
                          <Form.Item
                            required
                            showStatusIcon={false}
                            label={t('原因')}
                            status={getStatus(meta, validating)}
                            message={
                              getStatus(meta, validating) === 'error' &&
                              meta.error
                            }
                          >
                            <ReportFailReasonCascader
                              {...input}
                              status={values.status}
                              form={form}
                            ></ReportFailReasonCascader>
                          </Form.Item>
                        )}
                      </Field>
                    )}
                    {[SIGN_REPORT_STATUS_WAIT_ADD_INFO].includes(
                      status ?? +values.status,
                    ) && (
                      <Field
                        name="wrong_keys"
                        validateFields={[]}
                        validate={validateRequired}
                      >
                        {({ input, meta }) => (
                          <Form.Item
                            required
                            showStatusIcon={false}
                            label={t('缺失/异常字段')}
                            status={getStatus(meta, validating)}
                            message={
                              getStatus(meta, validating) === 'error' &&
                              meta.error
                            }
                          >
                            <SelectMultiple
                              appearance="button"
                              size="m"
                              {...input}
                              options={wrongShowKeys?.map((v) => ({
                                value: v,
                                text: _.find(
                                  allSignApplyColumns,
                                  (c) => c.key === v,
                                )?.header,
                              }))}
                              clearable
                            ></SelectMultiple>
                          </Form.Item>
                        )}
                      </Field>
                    )}
                    <Field name="msg">
                      {({ input, meta }) => (
                        <Form.Item
                          showStatusIcon={false}
                          label={t('备注')}
                          status={getStatus(meta, validating)}
                          message={
                            getStatus(meta, validating) === 'error' &&
                            meta.error
                          }
                        >
                          <TextArea
                            {...(input as any)}
                            placeholder={t('请输入')}
                            size="m"
                            disabled={submitting}
                          />
                        </Form.Item>
                      )}
                    </Field>
                  </Form>
                  <Form.Action style={{ textAlign: 'center' }}>
                    <Button
                      type="primary"
                      htmlType="submit"
                      loading={submitting}
                    >
                      {t('提交')}
                    </Button>
                  </Form.Action>
                </form>
              );
            }}
          </FinalForm>
        </Modal.Body>
      </Modal>
    </>
  );
};
