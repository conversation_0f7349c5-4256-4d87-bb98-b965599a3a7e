import React from 'react';
import { useDialog, DialogRef } from '@src/utils/react-use/useDialog';
import { Modal, Button, Form, Radio, Icon, Text } from '@tencent/tea-component';
import { Form as FinalForm, Field } from 'react-final-form';
import { getStatus } from '@src/utils/form/getStatus';
import { t } from '@src/utils/i18n';
import _ from 'lodash';
import {
  SIGN_REPORT_STATUS_REPORTING,
  SIGN_REPORT_STATUS_WAIT_REPORT,
} from '@src/const/const';
import { validateRequired } from '@src/utils/validateFn';
import { app } from '@src/utils/tea/app';
import { exportSignReport, getSignReportIdList } from '@src/api/sign';
import { addAsyncTask } from '@src/api/addAsyncTask';

interface DialogProps {
  dialogRef: DialogRef;
  onSuccess: () => void;
}

export const ExportOptionsDialog = (props: DialogProps) => {
  const { dialogRef, onSuccess } = props;
  const [visible, setShowState, defaultVal] = useDialog<{
    searchKeys: any;
    params: any;
  }>(dialogRef);

  const { searchKeys, params } = defaultVal;

  const _handlerSubmit = async (vals: any) => {
    try {
      const res: any = await exportSignReport({
        ...params,
        need_attachment: +vals.need_attachment,
      });
      if (res.code === 0) {
        onSuccess();
        app.tips.success(
          t('导出任务下发成功，请至“异步任务列表 - 导出任务 ”页面查看详细结果'),
        );
        if (vals.is_change_status === '1') {
          const res = await getSignReportIdList({
            ..._.omit(searchKeys, 'time'),
            status: SIGN_REPORT_STATUS_WAIT_REPORT,
            from_time: searchKeys.time?.[0]?.format('YYYY-MM-DD 00:00:00'),
            to_time: searchKeys.time?.[1]?.format('YYYY-MM-DD 23:59:59'),
          });
          if (res.code !== 0) {
            app.tips.error(t('获取报备数据失败，请重试'));
            return res;
          }
          if (!res.data.length) {
            setShowState(false);
            app.tips.warning(t('当前筛选条件下无待报备数据，无需变更状态'));
            return Promise.resolve();
          }
          const res1 = await addAsyncTask({
            command: 'change_sign_report_status',
            params: _.map(res.data, (id) => ({
              report_id: id,
              status: SIGN_REPORT_STATUS_REPORTING,
            })),
          });
          if (res1?.code === 0) {
            app.tips.success(
              t(
                '状态变更任务下发成功，请至“异步任务列表 - 批量变更需求单状态”页面查看详细结果',
              ),
            );
            setShowState(false);
          }
          return res1;
        } else {
          setShowState(false);
        }
      }
    } catch (err: any) {
      app.tips.error(err.toString());
      setShowState(false);
    }
  };

  return (
    <>
      <Modal
        visible={visible}
        size="m"
        caption={<>{t('导出选项')}</>}
        onClose={() => setShowState(false)}
      >
        <Modal.Body>
          <p style={{ marginBottom: 10 }}>
            <Text theme="warning">
              {t(
                '注意：导出任务异步执行，数据量较大时相对耗时，请耐心等待，导出完毕后会推送标题为“TENCENT 签名报备（导出）”的邮件，请注意查收',
              )}
            </Text>
          </p>
          <FinalForm
            onSubmit={_handlerSubmit}
            initialValuesEqual={(val, oldVal) => {
              return _.isEqual(val, oldVal);
            }}
            initialValues={{
              need_attachment: '0',
            }}
          >
            {({ handleSubmit, validating, submitting, values, form }) => {
              return (
                <form onSubmit={handleSubmit}>
                  <Form layout="default">
                    <Field
                      name="need_attachment"
                      validateFields={[]}
                      validate={validateRequired}
                    >
                      {({ input, meta }) => (
                        <Form.Item
                          required
                          showStatusIcon={false}
                          label={t('是否导出附件')}
                          status={getStatus(meta, validating)}
                          message={
                            getStatus(meta, validating) === 'error' &&
                            meta.error
                          }
                        >
                          <Radio.Group {...input}>
                            <Radio name="1">{t('是')}</Radio>
                            <Radio name="0">{t('否')}</Radio>
                          </Radio.Group>
                        </Form.Item>
                      )}
                    </Field>
                    <Field
                      name="is_change_status"
                      validateFields={[]}
                      validate={validateRequired}
                    >
                      {({ input, meta }) => (
                        <Form.Item
                          required
                          showStatusIcon={false}
                          label={t('是否同步变更状态')}
                          status={getStatus(meta, validating)}
                          message={
                            getStatus(meta, validating) === 'error' &&
                            meta.error
                          }
                          extra={t(
                            '选择是，会将当前所导出单据中，状态为“待报备”的单据变更为“报备中”状态',
                          )}
                        >
                          <Radio.Group {...input}>
                            <Radio name="1">{t('是')}</Radio>
                            <Radio name="0">{t('否')}</Radio>
                          </Radio.Group>
                        </Form.Item>
                      )}
                    </Field>
                  </Form>
                  <Form.Action style={{ textAlign: 'center' }}>
                    <Button
                      type="primary"
                      htmlType="submit"
                      loading={submitting}
                    >
                      {t('确认')}
                    </Button>
                  </Form.Action>
                </form>
              );
            }}
          </FinalForm>
        </Modal.Body>
      </Modal>
    </>
  );
};
