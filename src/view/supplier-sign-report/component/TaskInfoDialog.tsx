import { DialogRef, useDialog } from '@src/utils/react-use/useDialog';
import { Modal } from '@tencent/tea-component/lib/modal/ModalMain';
import { t, Trans, Slot } from '@src/utils/i18n';
import {
  Button,
  Text,
  Icon,
  LoadingTip,
  Form,
  Progress,
} from '@tencent/tea-component';
import _ from 'lodash';
import { useEffect, useMemo, useRef, useState } from 'react';
import { getAsyncTaskInfo } from '@src/api/getAsyncTaskInfo';
import { useHistory } from 'react-router-dom';
import { sleep } from '@src/global-components/import-component/utils';

interface DialogProps {
  dialogRef: DialogRef;
}

const initState = {
  count: 0,
  succNum: 0,
  totalNum: 0,
  status: 0,
  err_msg: {},
};

export const TaskInfoDialog = (props: DialogProps) => {
  const { dialogRef } = props;
  const history = useHistory();
  const [visible, setShowState, defaultVal] = useDialog<{
    task_id: string;
  }>(dialogRef);
  const { task_id } = defaultVal;

  const _visible = useRef(visible);

  const [{ count, succNum, totalNum, err_msg }, setState] = useState({
    ...initState,
  });

  const percent = useMemo(() => {
    return totalNum ? ((succNum + count) / totalNum) * 100 : 0;
  }, [count, succNum, totalNum]);

  useEffect(() => {
    setState({ ...initState });
    const fetchInfo = async () => {
      try {
        const { data } = await getAsyncTaskInfo({
          task_id,
          command: 'change_sign_report_status',
        });
        const res = {
          ...data,
          totalNum: data?.task_num || 0,
          succNum: data?.succ_num || 0,
          count: data?.err_num || 0,
        };
        setState(res);

        if (res.status < 2 && _visible.current) {
          await sleep(2000);
          fetchInfo();
        }
      } catch (error) {
        if (_visible.current) {
          await sleep(2000);
          fetchInfo();
        }
      }
    };
    if (visible) {
      fetchInfo();
    }
    _visible.current = visible;
  }, [task_id, visible]);

  return (
    <>
      <Modal
        visible={visible}
        size="l"
        caption={
          <>
            <Icon
              type="infoblue"
              size="l"
              style={{ margin: '0 10px 10px 0' }}
            />
            <Text>{t('任务详情')}</Text>
          </>
        }
        onClose={() => setShowState(false)}
        disableEscape
      >
        <Modal.Body>
          {percent < 100 ? <LoadingTip /> : null}
          <Progress
            percent={percent}
            text={(percent) => `${Math.floor(percent)} %`}
          />
          <Form layout="inline">
            <Form.Item label={t('任务ID')}>
              <Form.Text>{task_id}</Form.Text>
            </Form.Item>
            <Form.Item label={t('任务总量')}>
              <Form.Text>{totalNum}</Form.Text>
            </Form.Item>
            <Form.Item label={t('任务成功量')}>
              <Form.Text>
                <Text theme="success">{succNum}</Text>
              </Form.Text>
            </Form.Item>
            <Form.Item label={t('任务失败量')}>
              <Form.Text>
                <Text theme="danger">{count}</Text>
              </Form.Text>
            </Form.Item>
          </Form>
          {count > 0 ? (
            <>
              <div style={{ marginBottom: 15 }}>
                <Trans count={count}>
                  <Text theme="danger">
                    {{ count }} 条数据错误，请修改后导入重新上传
                  </Text>
                </Trans>
              </div>
              <div style={{ maxHeight: 300, overflow: 'auto' }}>
                {_.map(err_msg ?? {}, (v: any, k) => {
                  const _index = Number(k?.split('_')?.[1]);
                  return (
                    <div key={_index}>
                      {typeof v === 'string' ? (
                        <Trans>
                          错误信息：导入表格第 <Slot content={_index + 1} />{' '}
                          行错误，对应模版文件第 <Slot content={_index + 5} />{' '}
                          行， <Slot content={v} />
                        </Trans>
                      ) : (
                        <Trans>
                          错误信息：报备单ID: <Slot content={v.report_id} />
                          , <Slot content={v.msg} />
                          （通过批量上传报备结果变更的，对应导入表格第{' '}
                          <Slot content={_index} /> 行错误，模版文件第{' '}
                          <Slot content={_index + 5} /> 行 ）
                        </Trans>
                      )}
                    </div>
                  );
                })}
              </div>
            </>
          ) : null}
        </Modal.Body>
        <Modal.Footer>
          <Button
            type="weak"
            onClick={() => {
              setShowState(false);
            }}
          >
            {t('关闭')}
          </Button>
        </Modal.Footer>
      </Modal>
    </>
  );
};
