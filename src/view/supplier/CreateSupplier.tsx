import { useState } from 'react';
import { t } from '@src/utils/i18n';
import { Button, Form, Input, InputNumber } from '@tencent/tea-component';
import { useHistory } from 'react-router-dom';
import { Form as FinalForm, Field } from 'react-final-form';
import { getStatus } from '@src/utils/form/getStatus';
import _ from 'lodash';
import { validateRequired } from '@src/utils/validateFn';
import { editPurchaseSupplier } from '@src/apiV2/editPurchaseSupplier';
import { addPurchaseSupplier } from '@src/apiV2/addPurchaseSupplier';
import { app } from '@src/utils/tea/app';

const { TextArea } = Input;

function getInitState(state) {
  if (!state) return {};
  return {
    ...state,
    report_email: state?.report_email?.split(',')?.join('\n') ?? '',
  };
}

export const CreateSupplier = () => {
  const history: any = useHistory();
  const [initValues] = useState<any>(
    getInitState(history?.location?.state?.row ?? {}),
  );
  const isModify = history.location.pathname?.includes(
    'supplier/manage/modify',
  );

  function handleBack() {
    history.push('/supplier/manage');
  }

  const _handlerSubmit = async (formValue) => {
    try {
      let res;
      if (isModify) {
        res = await editPurchaseSupplier({
          ..._.omit(formValue, ['created_time', 'modified_time']),
          report_email: formValue.report_email?.replace(/\n/g, ','),
          supplier_id: initValues?.supplier_id,
        });
      } else {
        res = await addPurchaseSupplier({
          ...formValue,
          report_email: formValue.report_email?.replace(/\n/g, ','),
        });
      }
      if (res.code === 0) {
        app.tips.success(t('操作成功'));
        handleBack();
      }
    } catch (error: any) {}
  };

  return (
    <div>
      <Form.Title>
        {isModify ? t('编辑供应商账号') : t('创建供应商账号')}
      </Form.Title>
      <hr />
      <FinalForm
        onSubmit={_handlerSubmit}
        initialValuesEqual={(val, oldVal) => {
          return _.isEqual(val, oldVal);
        }}
        initialValues={initValues}
      >
        {({ handleSubmit, validating, submitting, form, ...rest }) => {
          return (
            <form onSubmit={handleSubmit}>
              <Form layout="default">
                <Field
                  name="name"
                  validateFields={[]}
                  validate={validateRequired}
                >
                  {({ input, meta }) => (
                    <Form.Item
                      required
                      showStatusIcon={false}
                      label={t('供应商名称')}
                      status={getStatus(meta, validating)}
                      message={
                        getStatus(meta, validating) === 'error' && meta.error
                      }
                    >
                      <Input
                        {...(input as any)}
                        placeholder={t('请输入')}
                        autoComplete="off"
                        size="m"
                        disabled={submitting}
                      />
                    </Form.Item>
                  )}
                </Field>
                <Field
                  name="alias"
                  validateFields={[]}
                  validate={validateRequired}
                >
                  {({ input, meta }) => (
                    <Form.Item
                      required
                      showStatusIcon={false}
                      label={t('供应商简称')}
                      status={getStatus(meta, validating)}
                      message={
                        getStatus(meta, validating) === 'error' && meta.error
                      }
                    >
                      <Input
                        {...(input as any)}
                        placeholder={t('请输入')}
                        autoComplete="off"
                        size="m"
                        disabled={submitting}
                      />
                    </Form.Item>
                  )}
                </Field>
                <Field
                  name="alias_pinyin"
                  validateFields={[]}
                  validate={validateRequired}
                >
                  {({ input, meta }) => (
                    <Form.Item
                      required
                      showStatusIcon={false}
                      label={t('供应商拼音简称')}
                      status={getStatus(meta, validating)}
                      message={
                        getStatus(meta, validating) === 'error' && meta.error
                      }
                    >
                      <Input
                        {...(input as any)}
                        placeholder={t('请输入')}
                        autoComplete="off"
                        size="m"
                        disabled={submitting}
                      />
                    </Form.Item>
                  )}
                </Field>
                <Field
                  name="report_email"
                  validateFields={[]}
                  validate={validateRequired}
                >
                  {({ input, meta }) => (
                    <Form.Item
                      required
                      showStatusIcon={false}
                      label={t('报备邮箱')}
                      status={getStatus(meta, validating)}
                      message={
                        getStatus(meta, validating) === 'error' && meta.error
                      }
                    >
                      <Input.TextArea
                        {...(input as any)}
                        placeholder={t('换行输入多个')}
                        size="m"
                        disabled={submitting}
                      />
                    </Form.Item>
                  )}
                </Field>
                <Field
                  name="email_file_secret"
                  validateFields={[]}
                  validate={validateRequired}
                >
                  {({ input, meta }) => (
                    <Form.Item
                      showStatusIcon={false}
                      label={t('邮箱附件密钥')}
                      status={getStatus(meta, validating)}
                      message={
                        getStatus(meta, validating) === 'error' && meta.error
                      }
                    >
                      <Input
                        {...(input as any)}
                        placeholder={t('请输入')}
                        size="m"
                        disabled={submitting}
                      />
                    </Form.Item>
                  )}
                </Field>
              </Form>
              <Form.Action style={{ textAlign: 'center' }}>
                <Button
                  onClick={(e) => {
                    e?.preventDefault();
                    handleBack();
                  }}
                >
                  {t('取消')}
                </Button>
                <Button type="primary" loading={submitting} htmlType="submit">
                  {t('提交')}
                </Button>
              </Form.Action>
            </form>
          );
        }}
      </FinalForm>
    </div>
  );
};
export default CreateSupplier;
