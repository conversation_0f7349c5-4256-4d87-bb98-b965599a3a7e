import { useDialog, DialogRef } from '@src/utils/react-use/useDialog';
import { Modal, Button, Form, Input, Select } from '@tencent/tea-component';
import { Form as FinalForm, Field } from 'react-final-form';
import { getStatus } from '@src/utils/form/getStatus';
import { t } from '@src/utils/i18n';
import _ from 'lodash';
import { addSupplierAccount } from '@src/apiV2/addSupplierAccount';
import { app } from '@src/utils/tea/app';
import {
  validateEmail,
  validatePassword,
  validateRequired,
} from '@src/utils/validateFn';

interface DialogProps {
  dialogRef: DialogRef;
}

export const CreateSupplierLoginAccount = (props: DialogProps) => {
  const { dialogRef } = props;
  const [visible, setShowState, defaultVal] = useDialog<{
    supplier_id: number;
    onSuccess: () => void;
  }>(dialogRef);
  const { supplier_id, onSuccess } = defaultVal;

  const _handlerSubmit = async (vals: any) => {
    try {
      const params: any = {
        supplier_id,
        ..._.mapValues(vals, (val) =>
          typeof val === 'string' ? val?.trim() : val,
        ),
      };
      if (vals.ips) {
        params.ips = vals.ips?.split('\n');
      }
      const res = await addSupplierAccount(params);
      if (res?.code === 0) {
        app.tips.success(t('添加成功'));
        setShowState(false);
        onSuccess();
      }
    } catch (error) {
      app.tips.error(t('添加失败'));
    }
  };

  return (
    <>
      <Modal
        visible={visible}
        size="s"
        caption={t('添加供应商登录账号')}
        onClose={() => setShowState(false)}
      >
        <Modal.Body>
          <FinalForm
            onSubmit={_handlerSubmit}
            initialValuesEqual={(val, oldVal) => {
              return _.isEqual(val, oldVal);
            }}
          >
            {({ handleSubmit, validating, submitting }) => {
              return (
                <form onSubmit={handleSubmit}>
                  <Form layout="default">
                    <Form.Item showStatusIcon={false} label={t('供应商ID')}>
                      <Form.Text>{supplier_id}</Form.Text>
                    </Form.Item>
                    <Field name="name" validate={validateRequired}>
                      {({ input, meta }) => (
                        <Form.Item
                          required
                          showStatusIcon={false}
                          label={t('账号名称')}
                          status={getStatus(meta, validating)}
                          message={
                            getStatus(meta, validating) === 'error' &&
                            meta.error
                          }
                        >
                          <Input
                            {...(input as any)}
                            placeholder={t('请输入')}
                            size="m"
                            disabled={submitting}
                          />
                        </Form.Item>
                      )}
                    </Field>
                    <Field name="email" validate={validateEmail}>
                      {({ input, meta }) => (
                        <Form.Item
                          required
                          showStatusIcon={false}
                          label={t('登录邮箱')}
                          status={getStatus(meta, validating)}
                          message={
                            getStatus(meta, validating) === 'error' &&
                            meta.error
                          }
                        >
                          <Input
                            {...(input as any)}
                            placeholder={t('请输入')}
                            size="m"
                            disabled={submitting}
                          />
                        </Form.Item>
                      )}
                    </Field>
                    <Field name="password" validate={validatePassword}>
                      {({ input, meta }) => (
                        <Form.Item
                          required
                          showStatusIcon={false}
                          label={t('登录密码')}
                          status={getStatus(meta, validating)}
                          message={
                            getStatus(meta, validating) === 'error' &&
                            meta.error
                          }
                        >
                          <Input
                            {...(input as any)}
                            placeholder={t('请输入')}
                            size="m"
                            disabled={submitting}
                          />
                        </Form.Item>
                      )}
                    </Field>

                    <Field name="ips">
                      {({ input, meta }) => (
                        <Form.Item
                          showStatusIcon={false}
                          label={t('登陆ip白名单')}
                          status={getStatus(meta, validating)}
                          message={
                            getStatus(meta, validating) === 'error' &&
                            meta.error
                          }
                        >
                          <Input.TextArea
                            {...(input as any)}
                            placeholder={t('换行输入多个')}
                            size="m"
                            disabled={submitting}
                          />
                        </Form.Item>
                      )}
                    </Field>
                  </Form>
                  <Form.Action style={{ textAlign: 'center' }}>
                    <Button
                      type="primary"
                      htmlType="submit"
                      loading={submitting}
                    >
                      {t('提交')}
                    </Button>
                  </Form.Action>
                </form>
              );
            }}
          </FinalForm>
        </Modal.Body>
      </Modal>
    </>
  );
};
