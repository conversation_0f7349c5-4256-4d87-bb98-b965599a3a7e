import { useDialog, DialogRef } from '@src/utils/react-use/useDialog';
import { Modal, Button, Form, Input } from '@tencent/tea-component';
import { Form as FinalForm, Field } from 'react-final-form';
import { getStatus } from '@src/utils/form/getStatus';
import { t } from '@src/utils/i18n';
import _ from 'lodash';
import { app } from '@src/utils/tea/app';
import { validateRequired } from '@src/utils/validateFn';
import { editSupplierLoginAccount } from '@src/apiV2/editSupplierLoginAccount';

interface DialogProps {
  dialogRef: DialogRef;
}

export const EditSupplierLoginAccount = (props: DialogProps) => {
  const { dialogRef } = props;
  const [visible, setShowState, defaultVal] = useDialog<{
    user_id: number;
    ips: string[];
    onSuccess: () => void;
  }>(dialogRef);
  const { user_id, ips, onSuccess } = defaultVal;

  const _handlerSubmit = async (vals: any) => {
    const res = await editSupplierLoginAccount({
      user_id,
      ips: vals.ips.split('\n'),
      deleted: 0,
    });
    if (res?.code === 0) {
      app.tips.success(t('编辑成功'));
      setShowState(false);
      onSuccess();
    }
  };

  return (
    <>
      <Modal
        visible={visible}
        size="s"
        caption={t('编辑供应商登录账号')}
        onClose={() => setShowState(false)}
      >
        <Modal.Body>
          <FinalForm
            onSubmit={_handlerSubmit}
            initialValuesEqual={(val, oldVal) => {
              return _.isEqual(val, oldVal);
            }}
            initialValues={{
              ips: ips?.join('\n'),
            }}
          >
            {({ handleSubmit, validating, submitting }) => {
              return (
                <form onSubmit={handleSubmit}>
                  <Form layout="default">
                    <Form.Item showStatusIcon={false} label={t('用户ID')}>
                      <Form.Text>{user_id}</Form.Text>
                    </Form.Item>
                    <Field name="ips" validate={validateRequired}>
                      {({ input, meta }) => (
                        <Form.Item
                          required
                          showStatusIcon={false}
                          label={t('登陆ip白名单')}
                          status={getStatus(meta, validating)}
                          message={
                            getStatus(meta, validating) === 'error' &&
                            meta.error
                          }
                        >
                          <Input.TextArea
                            {...(input as any)}
                            placeholder={t('换行输入多个')}
                            size="m"
                            disabled={submitting}
                          />
                        </Form.Item>
                      )}
                    </Field>
                  </Form>
                  <Form.Action style={{ textAlign: 'center' }}>
                    <Button
                      type="primary"
                      htmlType="submit"
                      loading={submitting}
                    >
                      {t('提交')}
                    </Button>
                  </Form.Action>
                </form>
              );
            }}
          </FinalForm>
        </Modal.Body>
      </Modal>
    </>
  );
};
