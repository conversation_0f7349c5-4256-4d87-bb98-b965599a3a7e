import { useDialog, DialogRef } from '@src/utils/react-use/useDialog';
import { Modal, Button, Form, Input } from '@tencent/tea-component';
import { Form as FinalForm, Field } from 'react-final-form';
import { getStatus } from '@src/utils/form/getStatus';
import { t } from '@src/utils/i18n';
import _ from 'lodash';
import { app } from '@src/utils/tea/app';
import { validatePassword } from '@src/utils/validateFn';
import { resetLoginPassword } from '@src/apiV2/resetLoginPassword';

interface DialogProps {
  dialogRef: DialogRef;
}

export const ResetPasswordDialog = (props: DialogProps) => {
  const { dialogRef } = props;
  const [visible, setShowState, defaultVal] = useDialog<{
    user_id: number;
  }>(dialogRef);
  const { user_id } = defaultVal;

  const _handlerSubmit = async (vals: any) => {
    const res = await resetLoginPassword({
      user_id,
      password: vals.password,
    });
    if (res?.code === 0) {
      app.tips.success(t('重置成功'));
      setShowState(false);
    }
  };

  return (
    <>
      <Modal
        visible={visible}
        size="s"
        caption={t('编辑供应商登录账号')}
        onClose={() => setShowState(false)}
      >
        <Modal.Body>
          <FinalForm
            onSubmit={_handlerSubmit}
            initialValuesEqual={(val, oldVal) => {
              return _.isEqual(val, oldVal);
            }}
          >
            {({ handleSubmit, validating, submitting }) => {
              return (
                <form onSubmit={handleSubmit}>
                  <Form layout="default">
                    <Form.Item showStatusIcon={false} label={t('用户ID')}>
                      <Form.Text>{user_id}</Form.Text>
                    </Form.Item>
                    <Field name="password" validate={validatePassword}>
                      {({ input, meta }) => (
                        <Form.Item
                          required
                          showStatusIcon={false}
                          label={t('新密码')}
                          status={getStatus(meta, validating)}
                          message={
                            getStatus(meta, validating) === 'error' &&
                            meta.error
                          }
                        >
                          <Input
                            {...(input as any)}
                            placeholder={t('请输入')}
                            size="m"
                            disabled={submitting}
                          />
                        </Form.Item>
                      )}
                    </Field>
                  </Form>
                  <Form.Action style={{ textAlign: 'center' }}>
                    <Button
                      type="primary"
                      htmlType="submit"
                      loading={submitting}
                    >
                      {t('提交')}
                    </Button>
                  </Form.Action>
                </form>
              );
            }}
          </FinalForm>
        </Modal.Body>
      </Modal>
    </>
  );
};
