import { useMemo, useState } from 'react';
import { getStatus } from '@src/utils/form/getStatus';
import { t } from '@src/utils/i18n';
import { Button, Table, Input, Form, PopConfirm } from '@tencent/tea-component';
import { Form as FinalForm, Field } from 'react-final-form';
import { pageable, expandable } from '@tencent/tea-component/lib/table/addons';
import _ from 'lodash';
import { useSetState, useToggle } from 'react-use';
import { getPurchaseSupplier } from '@src/apiV2/getPurchaseSupplier';
import useAsyncRetryFunc from '@src/utils/react-use/useAsyncFunc';
import { useTableTopTip } from '@src/global-components/table-top-tip/useTableTopTip';
import { deletePurchaseSupplier } from '@src/apiV2/deletePurchaseSupplier';
import { app } from '@src/utils/tea/app';
import { useHistory } from 'react-router-dom';
import { CreateSupplierLoginAccount } from './CreateSupplierLoginAccount';
import { useDialogRef } from '@src/utils/react-use/useDialog';
import { SupplierLoginAccountTable } from './component/SupplierLoginAccountTable';

type FormType = {
  supplier_id?: string;
  name?: string;
};

const initalValues = {
  supplier_id: '',
  name: '',
};

export const SupplierManage = () => {
  const history = useHistory();
  const createLoginAccountRef = useDialogRef();
  const [expandedKeys, setExpandedKeys] = useState<string[]>([]);
  const [expandReload, toggleReload] = useToggle(false);
  const [searchKeys, setSearchKeys] = useState<any>();
  const [pagination, setPagination] = useSetState({
    page_index: 1,
    page_size: 50,
  });

  const {
    value: state,
    loading,
    retry,
  } = useAsyncRetryFunc(async () => {
    const result = await getPurchaseSupplier({
      ...searchKeys,
      ...pagination,
      page_index: 1,
    });
    return result?.data;
  }, [pagination, searchKeys]);

  const supplierList = useMemo(() => {
    return state?.list ?? [];
  }, [state]);

  const total = useMemo(() => {
    return state?.count ?? 0;
  }, [state]);

  const TableTopTip = useTableTopTip({
    record: supplierList,
    loading: loading,
  });

  function modify(row: any) {
    history.push({
      pathname: '/supplier/manage/modify',
      state: { row },
    });
  }

  const _handlerSubmit = (formValue: FormType) => {
    setSearchKeys({ ...initalValues, ...formValue, page_index: 1 });
    setPagination({ page_index: 1 });
  };

  async function handleDelete(supplier_id: number) {
    const res = await deletePurchaseSupplier({ supplier_id });
    if (res.code === 0) {
      app.tips.success(t('删除成功'));
      retry();
    }
  }

  return (
    <>
      <Button
        type="primary"
        style={{ marginBottom: 10, borderRadius: '5px' }}
        onClick={() => {
          history.push('/supplier/manage/create');
        }}
      >
        {t('+新增供应商')}
      </Button>
      <Table.ActionPanel>
        <div>
          <FinalForm
            onSubmit={_handlerSubmit}
            initialValuesEqual={(val, oldVal) => {
              return _.isEqual(val, oldVal);
            }}
            initialValues={initalValues}
          >
            {({ handleSubmit, validating, submitting }) => {
              return (
                <form onSubmit={handleSubmit}>
                  <Form layout="inline">
                    <Field name="supplier_id" validateFields={[]}>
                      {({ input, meta }) => (
                        <Form.Item
                          showStatusIcon={false}
                          label={t('供应商ID')}
                          status={getStatus(meta, validating)}
                          message={
                            getStatus(meta, validating) === 'error' &&
                            meta.error
                          }
                        >
                          <Input
                            {...(input as any)}
                            autoComplete="off"
                            size="m"
                          />
                        </Form.Item>
                      )}
                    </Field>
                    <Field name="name" validateFields={[]}>
                      {({ input, meta }) => (
                        <Form.Item
                          showStatusIcon={false}
                          label={t('供应商名称')}
                          status={getStatus(meta, validating)}
                          message={
                            getStatus(meta, validating) === 'error' &&
                            meta.error
                          }
                        >
                          <Input
                            {...(input as any)}
                            autoComplete="off"
                            size="m"
                          />
                        </Form.Item>
                      )}
                    </Field>
                    <Field name="alias" validateFields={[]}>
                      {({ input, meta }) => (
                        <Form.Item
                          showStatusIcon={false}
                          label={t('供应商简称')}
                          status={getStatus(meta, validating)}
                          message={
                            getStatus(meta, validating) === 'error' &&
                            meta.error
                          }
                        >
                          <Input
                            {...(input as any)}
                            autoComplete="off"
                            size="m"
                          />
                        </Form.Item>
                      )}
                    </Field>
                    <Field name="alias_pinyin" validateFields={[]}>
                      {({ input, meta }) => (
                        <Form.Item
                          showStatusIcon={false}
                          label={t('供应商拼音简称')}
                          status={getStatus(meta, validating)}
                          message={
                            getStatus(meta, validating) === 'error' &&
                            meta.error
                          }
                        >
                          <Input
                            {...(input as any)}
                            autoComplete="off"
                            size="m"
                          />
                        </Form.Item>
                      )}
                    </Field>
                    <Button
                      type="primary"
                      htmlType="submit"
                      loading={loading}
                      style={{ marginLeft: 5 }}
                    >
                      {t('查询')}
                    </Button>
                  </Form>
                </form>
              );
            }}
          </FinalForm>
        </div>
      </Table.ActionPanel>
      <Table
        compact
        bordered
        records={supplierList}
        recordKey="supplier_id"
        topTip={TableTopTip}
        columns={[
          { key: 'supplier_id', header: t('供应商id') },
          { key: 'name', header: t('供应商名称') },
          { key: 'alias', header: t('供应商简称') },
          { key: 'alias_pinyin', header: t('供应商拼音简称') },
          {
            key: 'report_email',
            header: t('报备邮箱'),
            render: (row: any) => {
              return _.map(
                row?.report_email?.split(',') ?? [],
                (item, index) => {
                  return <div key={index}>{item}</div>;
                },
              );
            },
          },
          {
            key: 'email_file_secret',
            header: t('邮箱附件密钥'),
          },
          { key: 'created_time', header: t('创建时间') },
          { key: 'modified_time', header: t('修改时间') },
          {
            key: 'operation',
            header: t('操作'),
            render: (row: any) => {
              return (
                <>
                  <Button
                    type="link"
                    onClick={() => {
                      modify(row);
                    }}
                  >
                    {t('编辑')}
                  </Button>
                  <PopConfirm
                    title={t('确定删除？')}
                    footer={(close) => (
                      <>
                        <Button
                          type="link"
                          onClick={() => {
                            close();
                            handleDelete(row.supplier_id);
                          }}
                        >
                          {t('确定')}
                        </Button>
                        <Button
                          type="text"
                          onClick={() => {
                            close();
                          }}
                        >
                          {t('取消')}
                        </Button>
                      </>
                    )}
                    placement="top-start"
                  >
                    <Button type="link">{t('删除')}</Button>
                  </PopConfirm>
                  <Button
                    type="link"
                    onClick={() => {
                      toggleReload(false);
                      createLoginAccountRef.current.open({
                        supplier_id: row.supplier_id,
                        onSuccess: () => {
                          toggleReload(true);
                        },
                      });
                    }}
                  >
                    {t('添加登录账号')}
                  </Button>
                </>
              );
            },
          },
        ]}
        addons={[
          pageable({
            recordCount: total,
            pageSize: pagination.page_size,
            pageIndex: pagination.page_index,
            onPagingChange: (query) =>
              setPagination({
                page_index: query.pageIndex,
                page_size: query.pageSize,
              }),
          }),
          expandable({
            expandedKeys,
            onExpandedKeysChange: (keys) => {
              setExpandedKeys(keys);
            },
            render(record) {
              return (
                <SupplierLoginAccountTable
                  id={record.supplier_id}
                  reload={expandReload}
                />
              );
            },
          }),
        ]}
      />
      <CreateSupplierLoginAccount dialogRef={createLoginAccountRef} />
    </>
  );
};
export default SupplierManage;
