import { useMemo } from 'react';
import { t } from '@src/utils/i18n';
import { Button, Table } from '@tencent/tea-component';
import { pageable } from '@tencent/tea-component/lib/table/addons';
import _ from 'lodash';
import { useSetState, useUpdateEffect } from 'react-use';
import useAsyncRetryFunc from '@src/utils/react-use/useAsyncFunc';
import { useTableTopTip } from '@src/global-components/table-top-tip/useTableTopTip';
import { getSupplierLoginAccount } from '@src/apiV2/getSupplierLoginAccount';
import { app } from '@src/utils/tea/app';
import { editSupplierLoginAccount } from '@src/apiV2/editSupplierLoginAccount';
import { useDialogRef } from '@src/utils/react-use/useDialog';
import { EditSupplierLoginAccount } from '../EditSupplierLoginAccount';
import { ResetPasswordDialog } from '../ResetPasswordDialog';

export const SupplierLoginAccountTable = ({ id, reload }) => {
  const editLoginAccountRef = useDialogRef();
  const resetPasswordRef = useDialogRef();

  const [pagination, setPagination] = useSetState<any>({
    page_index: 1,
    page_size: 10,
  });

  const {
    value: state,
    loading,
    retry,
  } = useAsyncRetryFunc(async () => {
    const result = await getSupplierLoginAccount({
      ...pagination,
      supplier_id: id,
    });
    return result?.data;
  }, [pagination, id]);

  useUpdateEffect(() => {
    reload && retry();
  }, [reload]);

  const accountList = useMemo(() => {
    return state?.list ?? [];
  }, [state]);

  const total = useMemo(() => {
    return state?.count ?? 0;
  }, [state]);

  const TableTopTip = useTableTopTip({
    record: accountList,
    loading: loading,
  });

  async function handleOperate(row: any, status: 0 | 1) {
    const res = await editSupplierLoginAccount({
      user_id: row.user_id,
      ips: row.ips,
      deleted: status,
    });
    if (res.code === 0) {
      app.tips.success(t('操作成功'));
      retry();
    }
  }

  return (
    <>
      <Table
        compact
        bordered
        records={accountList}
        recordKey="user_id"
        topTip={TableTopTip}
        columns={[
          { key: 'user_id', header: t('用户id') },
          { key: 'user_name', header: t('用户名') },
          {
            key: 'ips',
            header: t('ip白名单'),
            render: (row: any) => {
              return _.map(row?.ips ?? [], (item) => {
                return <div key={item}>{item}</div>;
              });
            },
          },
          { key: 'email', header: t('登录邮箱') },
          {
            key: 'operation',
            header: t('操作'),
            render: (row: any) => {
              if (row.deleted) {
                return (
                  <Button
                    type="link"
                    onClick={() => {
                      handleOperate(row, 0);
                    }}
                  >
                    {t('启用')}
                  </Button>
                );
              }
              return (
                <>
                  <Button
                    type="link"
                    onClick={() => {
                      handleOperate(row, 1);
                    }}
                  >
                    {t('禁用')}
                  </Button>
                  <Button
                    type="link"
                    onClick={() => {
                      editLoginAccountRef.current.open({
                        user_id: row.user_id,
                        ips: row.ips,
                        onSuccess: retry,
                      });
                    }}
                  >
                    {t('编辑')}
                  </Button>
                  <Button
                    type="link"
                    onClick={() => {
                      resetPasswordRef.current.open({
                        user_id: row.user_id,
                      });
                    }}
                  >
                    {t('重置密码')}
                  </Button>
                </>
              );
            },
          },
        ]}
        addons={[
          pageable({
            recordCount: total,
            onPagingChange: (query) =>
              setPagination({
                page_index: query.pageIndex,
                page_size: query.pageSize,
              }),
          }),
        ]}
      />
      <EditSupplierLoginAccount dialogRef={editLoginAccountRef} />
      <ResetPasswordDialog dialogRef={resetPasswordRef} />
    </>
  );
};
