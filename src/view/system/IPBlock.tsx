import { useMemo } from 'react';
import { getStatus } from '@src/utils/form/getStatus';
import { t } from '@src/utils/i18n';
import { Button, Table, Input, Form, PopConfirm } from '@tencent/tea-component';
import { Form as FinalForm, Field } from 'react-final-form';
import { pageable } from '@tencent/tea-component/lib/table/addons';
import _ from 'lodash';
import { useSetState } from 'react-use';
import useAsyncRetryFunc from '@src/utils/react-use/useAsyncFunc';
import { useTableTopTip } from '@src/global-components/table-top-tip/useTableTopTip';
import { app } from '@src/utils/tea/app';
import { useDialogRef } from '@src/utils/react-use/useDialog';
import { deleteUserConfig } from '@src/apiV2/deleteUserConfig';
import { AddIPBlock } from './component/AddIPBlock';
import { getIPBlock } from '@src/apiV2/getIPBlock';
import { deleteIPBlock } from '@src/apiV2/deleteIPBlock';

type FormType = {
  ip?: string;
};

const initalValues = {
  ip: '',
};

export const IPBlock = () => {
  const addDialogRef = useDialogRef();
  const [searchKeys, setSearchKeys] = useSetState<any>({
    page_index: 1,
    page_size: 10,
  });

  const {
    value: state,
    loading,
    retry,
  } = useAsyncRetryFunc(async () => {
    const result = await getIPBlock({ ...searchKeys });
    return result?.data;
  }, [searchKeys]);

  const list = useMemo(() => {
    return state?.list ?? [];
  }, [state]);

  const total = useMemo(() => {
    return state?.count ?? 0;
  }, [state]);

  const TableTopTip = useTableTopTip({
    record: list,
    loading: loading,
  });

  const _handlerSubmit = (formValue: FormType) => {
    setSearchKeys({ ...initalValues, ...formValue, page_index: 1 });
  };

  async function handleDelete(ip: string) {
    const res = await deleteIPBlock({ ip });
    if (res.code === 0) {
      app.tips.success(t('删除成功'));
      retry();
    }
  }

  return (
    <>
      <Button
        type="primary"
        style={{ marginBottom: 10 }}
        onClick={() => {
          addDialogRef.current.open({ onSuccess: retry });
        }}
      >
        {t('新增')}
      </Button>
      <Table.ActionPanel>
        <div>
          <FinalForm
            onSubmit={_handlerSubmit}
            initialValuesEqual={(val, oldVal) => {
              return _.isEqual(val, oldVal);
            }}
            initialValues={initalValues}
          >
            {({ handleSubmit, validating }) => {
              return (
                <form onSubmit={handleSubmit}>
                  <Form layout="inline">
                    <Field name="ip" validateFields={[]}>
                      {({ input, meta }) => (
                        <Form.Item
                          showStatusIcon={false}
                          label={t('ip')}
                          status={getStatus(meta, validating)}
                          message={
                            getStatus(meta, validating) === 'error' &&
                            meta.error
                          }
                        >
                          <Input
                            {...(input as any)}
                            autoComplete="off"
                            size="s"
                          />
                        </Form.Item>
                      )}
                    </Field>
                    <Button
                      type="primary"
                      htmlType="submit"
                      loading={loading}
                      style={{ marginLeft: 5 }}
                    >
                      {t('查询')}
                    </Button>
                  </Form>
                </form>
              );
            }}
          </FinalForm>
        </div>
      </Table.ActionPanel>
      <Table
        compact
        bordered
        records={list}
        recordKey="ip"
        topTip={TableTopTip}
        columns={[
          { key: 'ip', header: 'ip' },
          { key: 'updated_at', header: t('更新时间') },
          {
            key: 'operation',
            header: t('操作'),
            render: (row: any) => {
              return (
                <>
                  <PopConfirm
                    title={t('确定删除？')}
                    footer={(close) => (
                      <>
                        <Button
                          type="link"
                          onClick={() => {
                            close();
                            handleDelete(row.ip);
                          }}
                        >
                          {t('确定')}
                        </Button>
                        <Button
                          type="text"
                          onClick={() => {
                            close();
                          }}
                        >
                          {t('取消')}
                        </Button>
                      </>
                    )}
                    placement="top-start"
                  >
                    <Button type="link">{t('删除')}</Button>
                  </PopConfirm>
                </>
              );
            },
          },
        ]}
        addons={[
          pageable({
            recordCount: total,
            onPagingChange: (query) =>
              setSearchKeys({
                page_index: query.pageIndex,
                page_size: query.pageSize,
              }),
          }),
        ]}
      />
      <AddIPBlock dialogRef={addDialogRef} />
    </>
  );
};
