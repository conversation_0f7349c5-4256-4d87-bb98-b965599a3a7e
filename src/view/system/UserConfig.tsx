import { useMemo } from 'react';
import { getStatus } from '@src/utils/form/getStatus';
import { t } from '@src/utils/i18n';
import {
  Button,
  Table,
  Input,
  Form,
  PopConfirm,
  Select,
  Tag,
  Modal,
} from '@tencent/tea-component';
import { Form as FinalForm, Field } from 'react-final-form';
import { pageable } from '@tencent/tea-component/lib/table/addons';
import _ from 'lodash';
import { useSetState } from 'react-use';
import useAsyncRetryFunc from '@src/utils/react-use/useAsyncFunc';
import { useTableTopTip } from '@src/global-components/table-top-tip/useTableTopTip';
import { app } from '@src/utils/tea/app';
import { useDialogRef } from '@src/utils/react-use/useDialog';
import { getUserConfig } from '@src/apiV2/getUserConfig';
import { deleteUserConfig } from '@src/apiV2/deleteUserConfig';
import { AddUserConfig } from './component/AddUserConfig';
import { EditUserConfig } from './component/EditUserConfig';
import { findText, roleOptions } from '@src/const/const';
import { editUserConfig } from '@src/apiV2/editUserConfig';

type FormType = {
  user_id?: string;
  name?: string;
};

const initalValues = {
  role: '',
  staff_name: '',
  status: '',
};

export const UserConfig = () => {
  const addUserRef = useDialogRef();
  const editUserRef = useDialogRef();
  const [searchKeys, setSearchKeys] = useSetState<any>({
    page_index: 1,
    page_size: 10,
  });

  const {
    value: state,
    loading,
    retry,
  } = useAsyncRetryFunc(async () => {
    const result = await getUserConfig({ ...searchKeys });
    return result?.data;
  }, [searchKeys]);

  const userList = useMemo(() => {
    return state?.list ?? [];
  }, [state]);

  const total = useMemo(() => {
    return state?.count ?? 0;
  }, [state]);

  const TableTopTip = useTableTopTip({
    record: userList,
    loading: loading,
  });

  async function changeStatus(row: any, status) {
    Modal.confirm({
      message: t('确认执行此操作吗？'),
      onOk: async () => {
        const { user_id, role } = row;
        const res = await editUserConfig({ user_id, role, status });
        if (res.code === 0) {
          app.tips.success(t('操作成功'));
          retry();
        }
      },
    });
  }

  const _handlerSubmit = (formValue: FormType) => {
    setSearchKeys({ ...initalValues, ...formValue, page_index: 1 });
  };

  async function handleDelete(user_id: number) {
    const res = await deleteUserConfig({ user_id });
    if (res.code === 0) {
      app.tips.success(t('删除成功'));
      retry();
    }
  }

  return (
    <>
      <Button
        type="primary"
        style={{ marginBottom: 10 }}
        onClick={() => {
          addUserRef.current.open({ onSuccess: retry });
        }}
      >
        {t('+新增用户')}
      </Button>
      <Table.ActionPanel>
        <div>
          <FinalForm
            onSubmit={_handlerSubmit}
            initialValuesEqual={(val, oldVal) => {
              return _.isEqual(val, oldVal);
            }}
            initialValues={initalValues}
          >
            {({ handleSubmit, validating, submitting }) => {
              return (
                <form onSubmit={handleSubmit}>
                  <Form layout="inline">
                    <Field name="staff_name" validateFields={[]}>
                      {({ input, meta }) => (
                        <Form.Item
                          showStatusIcon={false}
                          label={t('用户名')}
                          status={getStatus(meta, validating)}
                          message={
                            getStatus(meta, validating) === 'error' &&
                            meta.error
                          }
                        >
                          <Input
                            {...(input as any)}
                            autoComplete="off"
                            size="s"
                          />
                        </Form.Item>
                      )}
                    </Field>
                    <Field name="role" validateFields={[]}>
                      {({ input, meta }) => (
                        <Form.Item
                          showStatusIcon={false}
                          label={t('角色')}
                          status={getStatus(meta, validating)}
                          message={
                            getStatus(meta, validating) === 'error' &&
                            meta.error
                          }
                        >
                          <Select
                            {...(input as any)}
                            autoComplete="off"
                            size="s"
                            clearable
                            appearance="button"
                            options={roleOptions}
                          />
                        </Form.Item>
                      )}
                    </Field>
                    <Field name="status" validateFields={[]}>
                      {({ input, meta }) => (
                        <Form.Item
                          showStatusIcon={false}
                          label={t('状态')}
                          status={getStatus(meta, validating)}
                          message={
                            getStatus(meta, validating) === 'error' &&
                            meta.error
                          }
                        >
                          <Select
                            {...(input as any)}
                            autoComplete="off"
                            size="s"
                            clearable
                            appearance="button"
                            options={[
                              {
                                value: 0,
                                text: t('正常'),
                              },
                              {
                                value: 1,
                                text: t('禁用'),
                              },
                            ]}
                          />
                        </Form.Item>
                      )}
                    </Field>
                    <Button
                      type="primary"
                      htmlType="submit"
                      loading={loading}
                      style={{ marginLeft: 5 }}
                    >
                      {t('查询')}
                    </Button>
                  </Form>
                </form>
              );
            }}
          </FinalForm>
        </div>
      </Table.ActionPanel>
      <Table
        compact
        bordered
        records={userList}
        recordKey="user_id"
        topTip={TableTopTip}
        columns={[
          { key: 'staff_name', header: t('用户名') },
          {
            key: 'role',
            header: t('角色'),
            render: (row: any) => findText(roleOptions, row.role),
          },
          {
            key: 'status',
            header: t('状态'),
            render: (row: any) =>
              row.status === 1 ? (
                <Tag theme="error">{t('禁用')}</Tag>
              ) : (
                <Tag theme="success">{t('正常')}</Tag>
              ),
          },
          { key: 'created_at', header: t('创建时间') },
          { key: 'updated_at', header: t('更新时间') },
          {
            key: 'operation',
            header: t('操作'),
            render: (row: any) => {
              return (
                <>
                  <Button
                    type="link"
                    onClick={() => {
                      editUserRef.current.open({ ...row, onSuccess: retry });
                    }}
                  >
                    {t('编辑')}
                  </Button>
                  {row.status === 0 && (
                    <Button
                      type="link"
                      onClick={() => {
                        changeStatus(row, 1);
                      }}
                    >
                      {t('禁用')}
                    </Button>
                  )}
                  {row.status === 1 && (
                    <Button
                      type="link"
                      onClick={() => {
                        changeStatus(row, 0);
                      }}
                    >
                      {t('启用')}
                    </Button>
                  )}
                  <PopConfirm
                    title={t('确定删除？')}
                    footer={(close) => (
                      <>
                        <Button
                          type="link"
                          onClick={() => {
                            close();
                            handleDelete(row.user_id);
                          }}
                        >
                          {t('确定')}
                        </Button>
                        <Button
                          type="text"
                          onClick={() => {
                            close();
                          }}
                        >
                          {t('取消')}
                        </Button>
                      </>
                    )}
                    placement="top-start"
                  >
                    <Button type="link">{t('删除')}</Button>
                  </PopConfirm>
                </>
              );
            },
          },
        ]}
        addons={[
          pageable({
            recordCount: total,
            onPagingChange: (query) =>
              setSearchKeys({
                page_index: query.pageIndex,
                page_size: query.pageSize,
              }),
          }),
        ]}
      />
      <AddUserConfig dialogRef={addUserRef} />
      <EditUserConfig dialogRef={editUserRef} />
    </>
  );
};
export default UserConfig;
