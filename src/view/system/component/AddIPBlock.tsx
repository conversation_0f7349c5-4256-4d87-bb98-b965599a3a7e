import { useDialog, DialogRef } from '@src/utils/react-use/useDialog';
import { Modal, Button, Form, Input } from '@tencent/tea-component';
import { Form as FinalForm, Field } from 'react-final-form';
import { getStatus } from '@src/utils/form/getStatus';
import { t } from '@src/utils/i18n';
import _ from 'lodash';
import { app } from '@src/utils/tea/app';
import { validateRequired } from '@src/utils/validateFn';
import { addIPBlock } from '@src/apiV2/addIPConfig';

interface DialogProps {
  dialogRef: DialogRef;
}

export const AddIPBlock = (props: DialogProps) => {
  const { dialogRef } = props;
  const [visible, setShowState, defaultVal] = useDialog<{
    onSuccess: () => void;
  }>(dialogRef);
  const { onSuccess } = defaultVal;

  const _handlerSubmit = async (vals: any) => {
    const res = await addIPBlock({ ...vals });
    if (res?.code === 0) {
      app.tips.success(t('添加成功'));
      setShowState(false);
      onSuccess();
    }
  };

  return (
    <>
      <Modal
        visible={visible}
        size="s"
        caption={t('新增IP黑名单')}
        onClose={() => setShowState(false)}
      >
        <Modal.Body>
          <FinalForm
            onSubmit={_handlerSubmit}
            initialValuesEqual={(val, oldVal) => {
              return _.isEqual(val, oldVal);
            }}
          >
            {({ handleSubmit, validating, submitting }) => {
              return (
                <form onSubmit={handleSubmit}>
                  <Form layout="default">
                    <Field name="ip" validate={validateRequired}>
                      {({ input, meta }) => (
                        <Form.Item
                          required
                          showStatusIcon={false}
                          label={t('IP')}
                          status={getStatus(meta, validating)}
                          message={
                            getStatus(meta, validating) === 'error' &&
                            meta.error
                          }
                        >
                          <Input
                            {...(input as any)}
                            placeholder={t('请输入')}
                            size="m"
                            disabled={submitting}
                          />
                        </Form.Item>
                      )}
                    </Field>
                  </Form>
                  <Form.Action style={{ textAlign: 'center' }}>
                    <Button
                      type="primary"
                      htmlType="submit"
                      loading={submitting}
                    >
                      {t('提交')}
                    </Button>
                  </Form.Action>
                </form>
              );
            }}
          </FinalForm>
        </Modal.Body>
      </Modal>
    </>
  );
};
