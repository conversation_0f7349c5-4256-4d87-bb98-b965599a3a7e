import { useDialog, DialogRef } from '@src/utils/react-use/useDialog';
import { Modal, Button, Form, Input, Select } from '@tencent/tea-component';
import { Form as FinalForm, Field } from 'react-final-form';
import { getStatus } from '@src/utils/form/getStatus';
import { t } from '@src/utils/i18n';
import _ from 'lodash';
import { app } from '@src/utils/tea/app';
import { validateRequired } from '@src/utils/validateFn';
import { addUserConfig } from '@src/apiV2/addUserConfig';
import { roleOptions } from '@src/const/const';

interface DialogProps {
  dialogRef: DialogRef;
}

export const AddUserConfig = (props: DialogProps) => {
  const { dialogRef } = props;
  const [visible, setShowState, defaultVal] = useDialog<{
    onSuccess: () => void;
  }>(dialogRef);
  const { onSuccess } = defaultVal;

  const _handlerSubmit = async (vals: any) => {
    const res = await addUserConfig({ ...vals });
    if (res?.code === 0) {
      app.tips.success(t('添加成功'));
      setShowState(false);
      onSuccess();
    }
  };

  return (
    <>
      <Modal
        visible={visible}
        size="s"
        caption={t('新增用户')}
        onClose={() => setShowState(false)}
      >
        <Modal.Body>
          <FinalForm
            onSubmit={_handlerSubmit}
            initialValuesEqual={(val, oldVal) => {
              return _.isEqual(val, oldVal);
            }}
          >
            {({ handleSubmit, validating, submitting }) => {
              return (
                <form onSubmit={handleSubmit}>
                  <Form layout="default">
                    <Field name="staff_name" validate={validateRequired}>
                      {({ input, meta }) => (
                        <Form.Item
                          required
                          showStatusIcon={false}
                          label={t('用户名')}
                          status={getStatus(meta, validating)}
                          message={
                            getStatus(meta, validating) === 'error' &&
                            meta.error
                          }
                        >
                          <Input
                            {...(input as any)}
                            placeholder={t('请输入')}
                            size="m"
                            disabled={submitting}
                          />
                        </Form.Item>
                      )}
                    </Field>
                    <Field name="role" validate={validateRequired}>
                      {({ input, meta }) => (
                        <Form.Item
                          required
                          showStatusIcon={false}
                          label={t('角色')}
                          status={getStatus(meta, validating)}
                          message={
                            getStatus(meta, validating) === 'error' &&
                            meta.error
                          }
                        >
                          <Select
                            {...(input as any)}
                            autoComplete="off"
                            size="m"
                            clearable
                            appearance="button"
                            options={roleOptions}
                          />
                        </Form.Item>
                      )}
                    </Field>
                  </Form>
                  <Form.Action style={{ textAlign: 'center' }}>
                    <Button
                      type="primary"
                      htmlType="submit"
                      loading={submitting}
                    >
                      {t('提交')}
                    </Button>
                  </Form.Action>
                </form>
              );
            }}
          </FinalForm>
        </Modal.Body>
      </Modal>
    </>
  );
};
