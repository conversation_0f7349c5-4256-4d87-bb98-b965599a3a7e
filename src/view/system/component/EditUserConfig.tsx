import { useDialog, DialogRef } from '@src/utils/react-use/useDialog';
import {
  Modal,
  Button,
  Form,
  Select,
  RadioGroup,
  Radio,
} from '@tencent/tea-component';
import { Form as FinalForm, Field } from 'react-final-form';
import { getStatus } from '@src/utils/form/getStatus';
import { t } from '@src/utils/i18n';
import _ from 'lodash';
import { app } from '@src/utils/tea/app';
import { validateRequired } from '@src/utils/validateFn';
import { editUserConfig } from '@src/apiV2/editUserConfig';
import { roleOptions } from '@src/const/const';

interface DialogProps {
  dialogRef: DialogRef;
}

export const EditUserConfig = (props: DialogProps) => {
  const { dialogRef } = props;
  const [visible, setShowState, defaultVal] = useDialog<{
    staff_name: number;
    user_id: string;
    role: string;
    status: number;
    onSuccess: () => void;
  }>(dialogRef);
  const { user_id, role, staff_name, status, onSuccess } = defaultVal;

  const _handlerSubmit = async (vals: { role: string; status: number }) => {
    const res = await editUserConfig({
      ...vals,
      status: +vals.status,
      user_id,
    });
    if (res?.code === 0) {
      app.tips.success(t('编辑成功'));
      setShowState(false);
      onSuccess();
    }
  };

  return (
    <>
      <Modal
        visible={visible}
        size="s"
        caption={t('编辑用户配置')}
        onClose={() => setShowState(false)}
      >
        <Modal.Body>
          <FinalForm
            onSubmit={_handlerSubmit}
            initialValuesEqual={(val, oldVal) => {
              return _.isEqual(val, oldVal);
            }}
            initialValues={{ role, status: status + '' }}
          >
            {({ handleSubmit, validating, submitting }) => {
              return (
                <form onSubmit={handleSubmit}>
                  <Form layout="default">
                    <Form.Item showStatusIcon={false} label={t('用户名')}>
                      <Form.Text>{staff_name}</Form.Text>
                    </Form.Item>
                    <Field name="role" validate={validateRequired}>
                      {({ input, meta }) => (
                        <Form.Item
                          required
                          showStatusIcon={false}
                          label={t('角色')}
                          status={getStatus(meta, validating)}
                          message={
                            getStatus(meta, validating) === 'error' &&
                            meta.error
                          }
                        >
                          <Select
                            {...(input as any)}
                            autoComplete="off"
                            size="m"
                            clearable
                            appearance="button"
                            options={roleOptions}
                          />
                        </Form.Item>
                      )}
                    </Field>
                    <Field name="status" validate={validateRequired}>
                      {({ input, meta }) => (
                        <Form.Item
                          required
                          showStatusIcon={false}
                          label={t('状态')}
                          status={getStatus(meta, validating)}
                          message={
                            getStatus(meta, validating) === 'error' &&
                            meta.error
                          }
                        >
                          <RadioGroup {...input}>
                            <Radio name="0">{t('启用')}</Radio>
                            <Radio name="1">{t('禁用')}</Radio>
                          </RadioGroup>
                        </Form.Item>
                      )}
                    </Field>
                  </Form>
                  <Form.Action style={{ textAlign: 'center' }}>
                    <Button
                      type="primary"
                      htmlType="submit"
                      loading={submitting}
                    >
                      {t('提交')}
                    </Button>
                  </Form.Action>
                </form>
              );
            }}
          </FinalForm>
        </Modal.Body>
      </Modal>
    </>
  );
};
