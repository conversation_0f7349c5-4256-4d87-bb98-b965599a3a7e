{"compilerOptions": {"target": "ESNext", "useDefineForClassFields": true, "lib": ["DOM", "DOM.Iterable", "ESNext"], "skipLibCheck": true, "esModuleInterop": false, "allowSyntheticDefaultImports": true, "strict": true, "forceConsistentCasingInFileNames": true, "module": "ESNext", "moduleResolution": "Node", "resolveJsonModule": true, "isolatedModules": true, "allowJs": true, "noEmit": true, "noImplicitAny": false, "jsx": "react-jsx", "paths": {"@tea/app": ["./node_modules/@tencent/tea-app"], "@tea/app/*": ["./node_modules/@tencent/tea-app/lib/*"], "@tea/component": ["./node_modules/@tencent/tea-component/lib"], "@tea/component/*": ["./node_modules/@tencent/tea-component/lib/*"], "@src/*": ["./src/*"], "@i18n/*": ["./i18n/*"]}, "experimentalDecorators": true}, "include": ["src", "./vite.config.ts"], "references": [{"path": "./tsconfig.node.json"}]}