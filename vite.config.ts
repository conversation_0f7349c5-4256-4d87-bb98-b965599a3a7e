import { defineConfig, loadEnv, ConfigEnv, UserConfig } from 'vite';
import react from '@vitejs/plugin-react';
import path from 'path';

// https://vitejs.dev/config/
export default defineConfig(({ mode }: ConfigEnv): UserConfig => {
  const env = loadEnv(mode, process.cwd(), '');
  const drop = JSON.parse(env.BUILD_DROP || '[]');

  return {
    plugins: [
      //  commonjs({
      //   include: /i18n/,
      //   transformMixedEsModules: true,
      //  }),
      react(),
    ],
    resolve: {
      alias: {
        '@src': path.resolve(__dirname, 'src/'),
        '@i18n': path.resolve(__dirname, 'i18n/'),
        '@tea/app': path.resolve(
          __dirname,
          'node_modules/@tencent/tea-app/lib',
        ),
        '@tea/component': path.resolve(
          __dirname,
          'node_modules/@tencent/tea-component/lib',
        ),
      },
    },
    optimizeDeps: {
      include: [
        '@i18n/translation',
        '@i18n/translation/en',
        '@i18n/translation/zh',
      ],
      force: true,
    },
    build: {
      commonjsOptions: {
        include: [/node_modules/, /i18n/],
      },
      rollupOptions: {
        output: {
          manualChunks(id) {
            if (id.includes('node_modules/@tencent/tea-component/')) {
              return 'vendor';
            }
          },
        },
      },
      minify: 'esbuild',
    },
    esbuild: {
      drop,
    },
    server: {
      port: 8080,
      cors: true,
      proxy: {
        '/apis/': {
          target: 'http://test-api-csms-supplier.polaris',
          // target: 'http://30.46.63.118',
          changeOrigin: true,
          rewrite: (path) => path.replace(/^\/apis/, '/'),
        },
        '/api-inner/': {
          target: 'http://test-inner-api-csms-supplier.polaris',
          // target: 'http://test-inner-api-isms-purchase.polaris',

          // target: 'http://30.46.63.118',
          changeOrigin: true,
          rewrite: (path) => path.replace(/^\/api-inner/, '/'),
        },
      },
    },
  };
});
